'use client'

import React, { Component, ReactNode } from 'react'
import { useRouter } from 'next/navigation'
import Cookies from 'js-cookie'

interface Props {
  children: ReactNode
}

interface State {
  hasError: boolean
  error: Error | null
}

class GlobalAuthErrorBoundaryClass extends Component<Props & { router: any }, State> {
  constructor(props: Props & { router: any }) {
    super(props)
    this.state = { hasError: false, error: null }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('GlobalAuthErrorBoundary caught error:', error, errorInfo)
    
    // Check if it's an authentication error
    if (
      error.message?.includes('Unauthorized') ||
      error.message?.includes('401') ||
      error.message?.includes('403') ||
      error.message?.includes('getCurrentUser')
    ) {
      // Clear auth data
      Cookies.remove('auth-token')
      Cookies.remove('refresh-token')
      Cookies.remove('session-id')
      
      // Clear cached user
      if (typeof window !== 'undefined') {
        localStorage.removeItem('cached-user')
      }
      
      // Redirect to login
      this.props.router.push('/login')
    }
  }

  render() {
    if (this.state.hasError && this.state.error) {
      // Check if it's an auth error
      if (
        this.state.error.message?.includes('Unauthorized') ||
        this.state.error.message?.includes('401') ||
        this.state.error.message?.includes('403')
      ) {
        return (
          <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <h2 className="text-2xl font-semibold text-gray-900 mb-2">Session Expired</h2>
              <p className="text-gray-600">Redirecting to login...</p>
            </div>
          </div>
        )
      }
      
      // For other errors, show error UI
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">Something went wrong</h2>
            <p className="text-gray-600 mb-4">{this.state.error.message}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Reload Page
            </button>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// Wrapper component to use hooks
export function GlobalAuthErrorBoundary({ children }: Props) {
  const router = useRouter()
  return (
    <GlobalAuthErrorBoundaryClass router={router}>
      {children}
    </GlobalAuthErrorBoundaryClass>
  )
}