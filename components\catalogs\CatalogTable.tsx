'use client'

import React from 'react'
import { Edit, Trash2, Eye, MoreVertical, ChevronUp, ChevronDown, Calendar, Hash, FileText, Settings } from 'lucide-react'
import type { Catalog } from '@/types/catalog'
import { Button } from '@/components/ui/Button'
import { TooltipProvider } from '@/components/ui/Tooltip'
import { TruncatedText } from '@/components/ui/TruncatedText'

interface CatalogTableProps {
  catalogs: Catalog[]
  onEdit: (catalog: Catalog) => void
  onDelete: (catalog: Catalog) => void
  onView?: (catalog: Catalog) => void
  isLoading?: boolean
  sortBy?: 'catalogCode' | 'catalogName' | 'createdAt'
  sortOrder?: 'asc' | 'desc'
  onSort?: (column: 'catalogCode' | 'catalogName' | 'createdAt') => void
}

export function CatalogTable({ 
  catalogs, 
  onEdit, 
  onDelete, 
  onView,
  isLoading,
  sortBy = 'createdAt',
  sortOrder = 'desc',
  onSort
}: CatalogTableProps) {
  
  const renderSortIcon = (column: 'catalogCode' | 'catalogName' | 'createdAt') => {
    if (sortBy !== column) return null
    return sortOrder === 'asc' ? 
      <ChevronUp className="w-4 h-4 inline-block ml-1" /> : 
      <ChevronDown className="w-4 h-4 inline-block ml-1" />
  }
  
  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="animate-pulse">
          <div className="h-14 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800"></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="border-t border-gray-200 dark:border-gray-700">
              <div className="h-20 bg-gray-50 dark:bg-gray-800 p-6">
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded-md w-3/4"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded-md w-1/2"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (catalogs.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="text-center py-16">
          <div className="mx-auto w-20 h-20 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-full flex items-center justify-center mb-6">
            <FileText className="w-10 h-10 text-blue-500 dark:text-blue-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Chưa có danh mục nào
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 max-w-sm mx-auto">
            Tạo danh mục đầu tiên để bắt đầu quản lý thiết bị của bạn một cách có tổ chức
          </p>
        </div>
      </div>
    )
  }

  return (
    <TooltipProvider>
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
            <tr>
              <th 
                className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 group"
                onClick={() => onSort && onSort('catalogCode')}
              >
                <div className="flex items-center gap-2">
                  <Hash className="w-4 h-4 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-200" />
                  Mã danh mục
                  {renderSortIcon('catalogCode')}
                </div>
              </th>
              <th 
                className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 group"
                onClick={() => onSort && onSort('catalogName')}
              >
                <div className="flex items-center gap-2">
                  <FileText className="w-4 h-4 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-200" />
                  Tên danh mục
                  {renderSortIcon('catalogName')}
                </div>
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                Mô tả
              </th>
              <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                Số thiết bị
              </th>
              <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                <div className="flex items-center justify-center gap-2">
                  <Settings className="w-4 h-4 text-gray-400" />
                  Trạng thái
                </div>
              </th>
              <th 
                className="px-6 py-4 text-center text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 group"
                onClick={() => onSort && onSort('createdAt')}
              >
                <div className="flex items-center justify-center gap-2">
                  <Calendar className="w-4 h-4 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-200" />
                  Ngày tạo
                  {renderSortIcon('createdAt')}
                </div>
              </th>
              <th className="px-6 py-4 text-right text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                Thao tác
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-100 dark:divide-gray-700">
            {catalogs.map((catalog, index) => (
              <tr 
                key={catalog.id} 
                className={`hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/10 dark:hover:to-indigo-900/10 transition-all duration-200 group ${
                  index % 2 === 0 ? 'bg-white dark:bg-gray-800' : 'bg-gray-50/50 dark:bg-gray-800/50'
                }`}
              >
                <td className="px-6 py-5 whitespace-nowrap">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center text-white font-semibold text-sm">
                      {catalog.catalogCode.charAt(0)}
                    </div>
                    <div>
                      <div className="text-sm font-semibold text-gray-900 dark:text-gray-100 font-mono">
                        {catalog.catalogCode}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        ID: {catalog.id.slice(-8)}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-5 whitespace-nowrap">
                  <TruncatedText 
                    text={catalog.catalogName}
                    className="text-sm font-medium text-gray-900 dark:text-gray-100"
                    maxWidth="max-w-[200px]"
                  />
                </td>
                <td className="px-6 py-5">
                  {catalog.description ? (
                    <TruncatedText
                      text={catalog.description}
                      className="text-sm text-gray-600 dark:text-gray-300"
                      maxWidth="max-w-[250px]"
                    />
                  ) : (
                    <span className="italic text-gray-400 dark:text-gray-500">Chưa có mô tả</span>
                  )}
                </td>
                <td className="px-6 py-5 whitespace-nowrap text-center">
                  <div className="inline-flex items-center gap-1 px-3 py-1.5 rounded-full text-xs font-semibold bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 dark:from-blue-900/40 dark:to-blue-800/40 dark:text-blue-200 border border-blue-200 dark:border-blue-700">
                    <Hash className="w-3 h-3" />
                    {catalog.equipmentCount || 0}
                  </div>
                </td>
                <td className="px-6 py-5 whitespace-nowrap text-center">
                  <span className={`inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-semibold rounded-full border ${
                    catalog.status === 'ACTIVE'
                      ? 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200 dark:from-green-900/40 dark:to-emerald-900/40 dark:text-green-200 dark:border-green-700'
                      : 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 border-gray-200 dark:from-gray-800/40 dark:to-gray-700/40 dark:text-gray-300 dark:border-gray-600'
                  }`}>
                    <div className={`w-2 h-2 rounded-full ${
                      catalog.status === 'ACTIVE' ? 'bg-green-500' : 'bg-gray-400'
                    }`} />
                    {catalog.status === 'ACTIVE' ? 'Hoạt động' : 'Không hoạt động'}
                  </span>
                </td>
                <td className="px-6 py-5 whitespace-nowrap text-center">
                  <div className="text-sm text-gray-600 dark:text-gray-300 font-medium">
                    {new Date(catalog.createdAt).toLocaleDateString('vi-VN')}
                  </div>
                  <div className="text-xs text-gray-400 dark:text-gray-500">
                    {new Date(catalog.createdAt).toLocaleTimeString('vi-VN', { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </div>
                </td>
                <td className="px-6 py-5 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex items-center justify-end gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    {onView && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onView(catalog)}
                        title="Xem chi tiết"
                        className="w-8 h-8 hover:bg-blue-100 hover:text-blue-600 dark:hover:bg-blue-900/20 dark:hover:text-blue-400 transition-colors"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => onEdit(catalog)}
                      title="Chỉnh sửa"
                      className="w-8 h-8 hover:bg-amber-100 hover:text-amber-600 dark:hover:bg-amber-900/20 dark:hover:text-amber-400 transition-colors"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => onDelete(catalog)}
                      title="Xóa"
                      className="w-8 h-8 hover:bg-red-100 hover:text-red-600 dark:hover:bg-red-900/20 dark:hover:text-red-400 transition-colors"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
    </TooltipProvider>
  )
}