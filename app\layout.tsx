import type { Metadata } from 'next'
import { Plus_Jakarta_Sans } from 'next/font/google'
import './globals.css'
import { QueryProvider } from '@/components/providers/QueryProvider'
import { AuthProvider } from '@/contexts/AuthContext'
import { GlobalAuthErrorBoundary } from '@/components/GlobalAuthErrorBoundary'
import { Toaster } from 'sonner'

const plusJakartaSans = Plus_Jakarta_Sans({ 
  subsets: ['latin'],
  variable: '--font-plus-jakarta-sans',
})

export const metadata: Metadata = {
  title: 'Hệ thống thầu thông minh',
  description: 'Smart Bidding System - Quản lý đấu thầu hiệu quả',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="vi" className={plusJakartaSans.variable}>
      <body className="font-sans">
        <QueryProvider>
          <GlobalAuthErrorBoundary>
            <AuthProvider>
              {children}
              <Toaster position="top-right" />
            </AuthProvider>
          </GlobalAuthErrorBoundary>
        </QueryProvider>
      </body>
    </html>
  )
}