'use client'

import * as React from 'react'
import { DayPicker } from 'react-day-picker'
import { format } from 'date-fns'
import { vi } from 'date-fns/locale'
import { Calendar } from 'lucide-react'
import 'react-day-picker/style.css'

interface MultiDatePickerProps {
  selectedDates: Date[]
  onDatesChange: (dates: Date[]) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function MultiDatePicker({
  selectedDates,
  onDatesChange,
  placeholder = "Chọn ngày",
  className = "",
  disabled = false
}: MultiDatePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false)
  const containerRef = React.useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  const handleSelect = (dates: Date[] | undefined) => {
    onDatesChange(dates || [])
  }

  const formatSelectedDates = () => {
    if (selectedDates.length === 0) {
      return placeholder
    } else if (selectedDates.length === 1) {
      return format(selectedDates[0], 'dd/MM/yyyy', { locale: vi })
    } else if (selectedDates.length === 2) {
      const sortedDates = [...selectedDates].sort((a, b) => a.getTime() - b.getTime())
      return `${format(sortedDates[0], 'dd/MM/yyyy')} - ${format(sortedDates[1], 'dd/MM/yyyy')}`
    } else {
      return `Đã chọn ${selectedDates.length} ngày`
    }
  }

  const footer = (
    <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
      <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
        {selectedDates.length > 0
          ? `Đã chọn ${selectedDates.length} ngày`
          : 'Vui lòng chọn ngày'}
      </p>
    </div>
  )

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* Input Button */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`
          w-full h-10 px-3 py-2 text-left border rounded-md transition-all duration-200 
          flex items-center justify-between
          ${disabled 
            ? 'bg-gray-100 dark:bg-gray-700 cursor-not-allowed text-gray-400' 
            : 'bg-white dark:bg-gray-800 hover:border-gray-400 dark:hover:border-gray-500 cursor-pointer'
          }
          ${isOpen 
            ? 'border-blue-500 ring-2 ring-blue-100 dark:ring-blue-900/30' 
            : 'border-gray-300 dark:border-gray-600'
          }
          ${selectedDates.length > 0
            ? 'text-gray-900 dark:text-gray-100' 
            : 'text-gray-500 dark:text-gray-400'
          }
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
        `}
      >
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-400" />
          <span className="text-sm font-medium">
            {formatSelectedDates()}
          </span>
        </div>
        {selectedDates.length > 0 && !disabled && (
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation()
              onDatesChange([])
            }}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            ×
          </button>
        )}
      </button>

      {/* Dropdown */}
      {isOpen && !disabled && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl z-50 p-4">
          <DayPicker
            mode="range"
            selected={selectedDates}
            onSelect={handleSelect}
            locale={vi}
            animate
            showOutsideDays
            classNames={{
              root: 'rdp-custom',
              months: 'flex space-x-4',
              month_caption: 'font-semibold text-gray-900 dark:text-gray-100 mb-2',
              weekdays: 'text-gray-600 dark:text-gray-400',
              weekday: 'text-xs font-medium',
              day: 'p-2 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded transition-colors',
              day_button: 'w-full h-full',
              selected: 'bg-blue-500 text-white hover:bg-blue-600',
              today: 'font-bold text-blue-600 dark:text-blue-400',
              outside: 'text-gray-400 dark:text-gray-600',
              disabled: 'text-gray-300 dark:text-gray-700 cursor-not-allowed'
            }}
            numberOfMonths={2}
            footer={footer}
          />
        </div>
      )}
    </div>
  )
}