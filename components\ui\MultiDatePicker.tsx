'use client'

import * as React from 'react'
import { DayPicker, DateRange } from 'react-day-picker'
import { format } from 'date-fns'
import { vi } from 'date-fns/locale'
import { Calendar } from 'lucide-react'
import 'react-day-picker/style.css'

interface MultiDatePickerProps {
  selectedDates: Date[]
  onDatesChange: (dates: Date[]) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function MultiDatePicker({
  selectedDates,
  onDatesChange,
  placeholder = "Chọn khoảng thời gian",
  className = "",
  disabled = false
}: MultiDatePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false)
  const [dateRange, setDateRange] = React.useState<DateRange | undefined>(() => {
    if (selectedDates.length >= 2) {
      return { from: selectedDates[0], to: selectedDates[1] }
    } else if (selectedDates.length === 1) {
      return { from: selectedDates[0], to: undefined }
    }
    return undefined
  })
  const containerRef = React.useRef<HTMLDivElement>(null)
  const dropdownRef = React.useRef<HTMLDivElement>(null)
  const [dropdownPosition, setDropdownPosition] = React.useState<'bottom' | 'top'>('bottom')
  const [dropdownAlign, setDropdownAlign] = React.useState<'left' | 'right'>('left')

  // Update dateRange when selectedDates prop changes
  React.useEffect(() => {
    if (selectedDates.length >= 2) {
      setDateRange({ from: selectedDates[0], to: selectedDates[1] })
    } else if (selectedDates.length === 1) {
      setDateRange({ from: selectedDates[0], to: undefined })
    } else {
      setDateRange(undefined)
    }
  }, [selectedDates])

  // Calculate dropdown position
  const calculatePosition = React.useCallback(() => {
    if (isOpen && containerRef.current && dropdownRef.current) {
      const container = containerRef.current.getBoundingClientRect()
      const windowHeight = window.innerHeight
      const windowWidth = window.innerWidth

      // Vertical positioning
      const spaceBelow = windowHeight - container.bottom
      const spaceAbove = container.top
      const dropdownHeight = 400 // Approximate height of DayPicker

      if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
        setDropdownPosition('top')
      } else {
        setDropdownPosition('bottom')
      }

      // Horizontal positioning
      const dropdownWidth = Math.min(350, windowWidth - 20) // Max width with some padding

      if (container.left + dropdownWidth > windowWidth - 10) {
        setDropdownAlign('right')
      } else {
        setDropdownAlign('left')
      }
    }
  }, [isOpen])

  React.useEffect(() => {
    calculatePosition()
  }, [isOpen, calculatePosition])

  // Recalculate position on window resize/scroll
  React.useEffect(() => {
    if (isOpen) {
      const handleResize = () => calculatePosition()
      const handleScroll = () => calculatePosition()

      window.addEventListener('resize', handleResize)
      window.addEventListener('scroll', handleScroll, true)

      return () => {
        window.removeEventListener('resize', handleResize)
        window.removeEventListener('scroll', handleScroll, true)
      }
    }
  }, [isOpen, calculatePosition])

  // Close dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  const handleSelect = (range: DateRange | undefined) => {
    setDateRange(range)
    if (range?.from && range?.to) {
      onDatesChange([range.from, range.to])
    } else if (range?.from) {
      onDatesChange([range.from])
    } else {
      onDatesChange([])
    }
  }

  const formatSelectedDates = () => {
    if (!dateRange?.from) {
      return placeholder
    } else if (!dateRange.to) {
      return format(dateRange.from, 'dd/MM/yyyy', { locale: vi })
    } else {
      return `${format(dateRange.from, 'dd/MM/yyyy')} - ${format(dateRange.to, 'dd/MM/yyyy')}`
    }
  }

  const footer = (
    <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
      <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
        {dateRange?.from
          ? dateRange.to
            ? 'Đã chọn khoảng thời gian'
            : 'Chọn ngày kết thúc'
          : 'Vui lòng chọn ngày bắt đầu'}
      </p>
    </div>
  )

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* Input Button */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`
          w-full h-10 px-3 py-2 text-left border rounded-md transition-all duration-200 
          flex items-center justify-between
          ${disabled 
            ? 'bg-gray-100 dark:bg-gray-700 cursor-not-allowed text-gray-400' 
            : 'bg-white dark:bg-gray-800 hover:border-gray-400 dark:hover:border-gray-500 cursor-pointer'
          }
          ${isOpen 
            ? 'border-blue-500 ring-2 ring-blue-100 dark:ring-blue-900/30' 
            : 'border-gray-300 dark:border-gray-600'
          }
          ${dateRange?.from
            ? 'text-gray-900 dark:text-gray-100' 
            : 'text-gray-500 dark:text-gray-400'
          }
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
        `}
      >
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-400" />
          <span className="text-sm font-medium">
            {formatSelectedDates()}
          </span>
        </div>
        {dateRange?.from && !disabled && (
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation()
              setDateRange(undefined)
              onDatesChange([])
            }}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            ×
          </button>
        )}
      </button>

      {/* Dropdown */}
      {isOpen && !disabled && (
        <div
          ref={dropdownRef}
          className={`
            absolute z-50 
            bg-white dark:bg-gray-800 
            border border-gray-200 dark:border-gray-700 
            rounded-lg shadow-xl 
            p-4
            ${dropdownPosition === 'top' ? 'bottom-full mb-2' : 'top-full mt-2'}
            ${dropdownAlign === 'right' ? 'right-0' : 'left-0'}
            w-full sm:w-auto
            max-w-[calc(100vw-2rem)] sm:max-w-[350px]
            overflow-auto
          `}
          style={{
            maxHeight: dropdownPosition === 'top' 
              ? 'calc(100vh - 100px)' 
              : 'calc(100vh - 100px)',
            overflowY: 'auto'
          }}
        >
          <DayPicker
            mode="range"
            selected={dateRange}
            onSelect={handleSelect}
            locale={vi}
            className='rdp-custom'
            showOutsideDays
            numberOfMonths={1}
            footer={footer}
            modifiersStyles={{
              selected: {
                backgroundColor: 'rgb(59 130 246)',
                color: 'white',
              },
              range_start: {
                backgroundColor: 'rgb(59 130 246)',
                color: 'white',
              },
              range_end: {
                backgroundColor: 'rgb(59 130 246)',
                color: 'white',
              },
              range_middle: {
                backgroundColor: 'rgb(219 234 254)',
                color: 'rgb(30 58 138)',
              }
            }}
          />
        </div>
      )}
    </div>
  )
}