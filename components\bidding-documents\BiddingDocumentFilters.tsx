'use client'

import { useState, useEffect, useRef } from 'react'
import { Search, Calendar } from 'lucide-react'
import DatePicker from 'react-datepicker'
import 'react-datepicker/dist/react-datepicker.css'
import { format } from 'date-fns'
import { vi } from 'date-fns/locale'
import { BiddingDocumentStatus } from '@/types/biddingDocument'
import type { BiddingDocumentFilter } from '@/types/biddingDocument'
import { useDebounce } from '@/hooks/useDebounce'

interface BiddingDocumentFiltersProps {
  onFilterChange: (filters: Partial<BiddingDocumentFilter>) => void
  onSearch: (value: string) => void
  searchTerm: string
  filters?: Partial<BiddingDocumentFilter>
}

export function BiddingDocumentFilters({
  onFilterChange,
  onSearch,
  searchTerm,
  filters = {}
}: BiddingDocumentFiltersProps) {
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    filters.createdFrom ? new Date(filters.createdFrom) : null,
    filters.createdTo ? new Date(filters.createdTo) : null
  ])
  const [startDate, endDate] = dateRange
  const [customerName, setCustomerName] = useState(filters.customerName || '')
  const debouncedCustomerName = useDebounce(customerName, 500)
  const isFirstRender = useRef(true)
  
  const statusOptions = [
    { value: '', label: 'All Status' },
    { value: BiddingDocumentStatus.PENDING, label: 'Pending' },
    { value: BiddingDocumentStatus.IN_PROGRESS, label: 'In Progress' },
    { value: BiddingDocumentStatus.COMPLETED, label: 'Completed' },
  ]
  
  const handleDateRangeChange = (dates: [Date | null, Date | null]) => {
    const [start, end] = dates
    setDateRange(dates)
    
    if (start && end) {
      // Format dates as YYYY-MM-DD for the API
      onFilterChange({
        createdFrom: format(start, 'yyyy-MM-dd'),
        createdTo: format(end, 'yyyy-MM-dd')
      })
    } else if (start && !end) {
      // Only start date selected
      onFilterChange({
        createdFrom: format(start, 'yyyy-MM-dd'),
        createdTo: undefined
      })
    } else {
      // No dates selected
      onFilterChange({
        createdFrom: undefined,
        createdTo: undefined
      })
    }
  }

  const clearDateRange = () => {
    setDateRange([null, null])
    onFilterChange({
      createdFrom: undefined,
      createdTo: undefined
    })
  }
  
  useEffect(() => {
    // Skip the first render to avoid calling onFilterChange immediately
    if (isFirstRender.current) {
      isFirstRender.current = false
      return
    }
    
    onFilterChange({ customerName: debouncedCustomerName || undefined })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedCustomerName])
  
  const handleCustomerNameChange = (value: string) => {
    setCustomerName(value)
  }

  return (
    <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Tìm kiếm theo tên, mã hồ sơ..."
            value={searchTerm}
            onChange={(e) => onSearch(e.target.value)}
            className="w-full h-10 pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
          />
        </div>

        {/* Status Filter */}
        <select
          value={filters.status?.[0] || ''}
          onChange={(e) => {
            const value = e.target.value
            if (value) {
              onFilterChange({ status: [value as BiddingDocumentStatus] })
            } else {
              onFilterChange({ status: undefined })
            }
          }}
          className="w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
        >
          <option value="">Lọc theo trạng thái</option>
          {statusOptions.slice(1).map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>

        {/* Customer Name */}
        <input
          type="text"
          placeholder="Tên khách hàng"
          value={customerName}
          onChange={(e) => handleCustomerNameChange(e.target.value)}
          className="w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
        />

        {/* Enhanced Date Range Picker */}
        <div className="relative md:col-span-2 lg:col-span-1">
          <div className="relative group">
            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-blue-500 w-4 h-4 pointer-events-none z-10 transition-colors duration-200" />
            <DatePicker
              selectsRange
              startDate={startDate}
              endDate={endDate}
              onChange={handleDateRangeChange}
              placeholderText="Chọn khoảng thời gian"
              dateFormat="dd/MM/yyyy"
              locale={vi}
              className="w-full h-10 pl-10 pr-8 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-gray-100 cursor-pointer transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500 shadow-sm"
              wrapperClassName="w-full"
              popperClassName="react-datepicker-popper z-50"
              showPopperArrow={false}
              isClearable={false}
              monthsShown={2}
              calendarClassName="custom-calendar shadow-lg border-0"
              dayClassName={(date) => 
                "hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors duration-150 rounded-md"
              }
              weekDayClassName={() =>
                "text-gray-500 dark:text-gray-400 font-medium text-xs"
              }
            />
            {(startDate || endDate) && (
              <button
                type="button"
                onClick={clearDateRange}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                title="Xóa bộ lọc thời gian"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
          
          {/* Date Range Display */}
          {(startDate || endDate) && (
            <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              {startDate && endDate ? (
                <span className="inline-flex items-center gap-1">
                  <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                  {format(startDate, 'dd/MM/yyyy')} - {format(endDate, 'dd/MM/yyyy')}
                </span>
              ) : startDate ? (
                <span className="inline-flex items-center gap-1">
                  <span className="w-2 h-2 bg-orange-400 rounded-full"></span>
                  Từ: {format(startDate, 'dd/MM/yyyy')}
                </span>
              ) : null}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}