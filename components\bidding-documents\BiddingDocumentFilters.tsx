'use client'

import { useState, useEffect, useRef } from 'react'
import { Search } from 'lucide-react'
import { format } from 'date-fns'
import { BiddingDocumentStatus } from '@/types/biddingDocument'
import type { BiddingDocumentFilter } from '@/types/biddingDocument'
import { useDebounce } from '@/hooks/useDebounce'
import { MultiDatePicker } from '@/components/ui/MultiDatePicker'

interface BiddingDocumentFiltersProps {
  onFilterChange: (filters: Partial<BiddingDocumentFilter>) => void
  onSearch: (value: string) => void
  searchTerm: string
  filters?: Partial<BiddingDocumentFilter>
}

export function BiddingDocumentFilters({
  onFilterChange,
  onSearch,
  searchTerm,
  filters = {}
}: BiddingDocumentFiltersProps) {
  const [selectedDates, setSelectedDates] = useState<Date[]>(() => {
    const dates: Date[] = []
    if (filters.createdFrom) dates.push(new Date(filters.createdFrom))
    if (filters.createdTo && filters.createdTo !== filters.createdFrom) {
      dates.push(new Date(filters.createdTo))
    }
    return dates
  })
  const [customerName, setCustomerName] = useState(filters.customerName || '')
  const debouncedCustomerName = useDebounce(customerName, 500)
  const isFirstRender = useRef(true)
  
  const statusOptions = [
    { value: '', label: 'All Status' },
    { value: BiddingDocumentStatus.PENDING, label: 'Pending' },
    { value: BiddingDocumentStatus.IN_PROGRESS, label: 'In Progress' },
    { value: BiddingDocumentStatus.COMPLETED, label: 'Completed' },
  ]
  
  const handleDatesChange = (dates: Date[]) => {
    setSelectedDates(dates)
    
    if (dates.length === 0) {
      // No dates selected
      onFilterChange({
        createdFrom: undefined,
        createdTo: undefined
      })
    } else if (dates.length === 1) {
      // Single date selected - filter for that specific day
      onFilterChange({
        createdFrom: format(dates[0], 'yyyy-MM-dd'),
        createdTo: format(dates[0], 'yyyy-MM-dd')
      })
    } else if (dates.length === 2) {
      // Two dates selected - use as date range
      const sortedDates = [...dates].sort((a, b) => a.getTime() - b.getTime())
      onFilterChange({
        createdFrom: format(sortedDates[0], 'yyyy-MM-dd'),
        createdTo: format(sortedDates[1], 'yyyy-MM-dd')
      })
    } else {
      // Multiple dates selected - use the earliest and latest as range
      const sortedDates = [...dates].sort((a, b) => a.getTime() - b.getTime())
      onFilterChange({
        createdFrom: format(sortedDates[0], 'yyyy-MM-dd'),
        createdTo: format(sortedDates[sortedDates.length - 1], 'yyyy-MM-dd')
      })
    }
  }

  
  useEffect(() => {
    // Skip the first render to avoid calling onFilterChange immediately
    if (isFirstRender.current) {
      isFirstRender.current = false
      return
    }
    
    onFilterChange({ customerName: debouncedCustomerName || undefined })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedCustomerName])
  
  const handleCustomerNameChange = (value: string) => {
    setCustomerName(value)
  }

  return (
    <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Tìm kiếm theo tên, mã hồ sơ..."
            value={searchTerm}
            onChange={(e) => onSearch(e.target.value.trim())}
            className="w-full h-10 pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
          />
        </div>

        {/* Status Filter */}
        <select
          value={filters.status?.[0] || ''}
          onChange={(e) => {
            const value = e.target.value
            if (value) {
              onFilterChange({ status: [value as BiddingDocumentStatus] })
            } else {
              onFilterChange({ status: undefined })
            }
          }}
          className="w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
        >
          <option value="">Lọc theo trạng thái</option>
          {statusOptions.slice(1).map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>

        {/* Customer Name */}
        <input
          type="text"
          placeholder="Tên khách hàng"
          value={customerName}
          onChange={(e) => handleCustomerNameChange(e.target.value.trim())}
          className="w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
        />

        {/* Multi Date Picker */}
        <div className="md:col-span-2 lg:col-span-1">
          <MultiDatePicker
            selectedDates={selectedDates}
            onDatesChange={handleDatesChange}
            placeholder="Chọn ngày lọc"
            className="w-full"
          />
        </div>
      </div>
    </div>
  )
}