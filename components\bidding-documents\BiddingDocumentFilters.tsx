'use client'

import { useState, useEffect, useRef } from 'react'
import { Search, Calendar } from 'lucide-react'
import { BiddingDocumentStatus } from '@/types/biddingDocument'
import type { BiddingDocumentFilter } from '@/types/biddingDocument'
import { useDebounce } from '@/hooks/useDebounce'

interface BiddingDocumentFiltersProps {
  onFilterChange: (filters: Partial<BiddingDocumentFilter>) => void
  onSearch: (value: string) => void
  searchTerm: string
  filters?: Partial<BiddingDocumentFilter>
}

export function BiddingDocumentFilters({
  onFilterChange,
  onSearch,
  searchTerm,
  filters = {}
}: BiddingDocumentFiltersProps) {
  const [dateFrom, setDateFrom] = useState(filters.createdFrom || '')
  const [dateTo, setDateTo] = useState(filters.createdTo || '')
  const [customerName, setCustomerName] = useState(filters.customerName || '')
  const debouncedCustomerName = useDebounce(customerName, 500)
  const isFirstRender = useRef(true)
  
  const statusOptions = [
    { value: '', label: 'All Status' },
    { value: BiddingDocumentStatus.PENDING, label: 'Pending' },
    { value: BiddingDocumentStatus.IN_PROGRESS, label: 'In Progress' },
    { value: BiddingDocumentStatus.COMPLETED, label: 'Completed' },
  ]
  
  const handleDateFromChange = (value: string) => {
    setDateFrom(value)
    if (value && dateTo && value > dateTo) {
      // Auto-adjust dateTo if dateFrom is after dateTo
      setDateTo(value)
      onFilterChange({ createdFrom: value, createdTo: value })
    } else {
      onFilterChange({ createdFrom: value || undefined })
    }
  }
  
  const handleDateToChange = (value: string) => {
    setDateTo(value)
    if (value && dateFrom && value < dateFrom) {
      // Auto-adjust dateFrom if dateTo is before dateFrom
      setDateFrom(value)
      onFilterChange({ createdFrom: value, createdTo: value })
    } else {
      onFilterChange({ createdTo: value || undefined })
    }
  }
  
  useEffect(() => {
    // Skip the first render to avoid calling onFilterChange immediately
    if (isFirstRender.current) {
      isFirstRender.current = false
      return
    }
    
    onFilterChange({ customerName: debouncedCustomerName || undefined })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedCustomerName])
  
  const handleCustomerNameChange = (value: string) => {
    setCustomerName(value)
  }

  return (
    <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Tìm kiếm theo tên, mã hồ sơ..."
            value={searchTerm}
            onChange={(e) => onSearch(e.target.value)}
            className="w-full h-10 pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
          />
        </div>

        {/* Status Filter */}
        <select
          value={filters.status?.[0] || ''}
          onChange={(e) => {
            const value = e.target.value
            if (value) {
              onFilterChange({ status: [value as BiddingDocumentStatus] })
            } else {
              onFilterChange({ status: undefined })
            }
          }}
          className="w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
        >
          <option value="">Lọc theo trạng thái</option>
          {statusOptions.slice(1).map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>

        {/* Customer Name */}
        <input
          type="text"
          placeholder="Tên khách hàng"
          value={customerName}
          onChange={(e) => handleCustomerNameChange(e.target.value)}
          className="w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
        />

        {/* Date Range */}
        <div className="flex flex-col sm:flex-row gap-2 md:col-span-2 lg:col-span-1">
          <div className="relative flex-1">
            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 pointer-events-none" />
            <input
              type="date"
              value={dateFrom}
              onChange={(e) => handleDateFromChange(e.target.value)}
              max={dateTo || undefined}
              className="w-full h-10 pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
              title="Từ ngày"
            />
          </div>
          <div className="relative flex-1">
            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 pointer-events-none" />
            <input
              type="date"
              value={dateTo}
              onChange={(e) => handleDateToChange(e.target.value)}
              min={dateFrom || undefined}
              className="w-full h-10 pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
              title="Đến ngày"
            />
          </div>
        </div>
      </div>
    </div>
  )
}