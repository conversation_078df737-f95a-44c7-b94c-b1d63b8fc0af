import { test, expect } from '@playwright/test'

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login')
  })

  test('displays login page correctly', async ({ page }) => {
    // Check page title
    await expect(page).toHaveTitle(/Đăng nhập|Login/i)

    // Check form elements
    await expect(page.getByLabel(/email/i)).toBeVisible()
    await expect(page.getByLabel(/password|mật khẩu/i)).toBeVisible()
    await expect(page.getByRole('button', { name: /đăng nhập|login/i })).toBeVisible()
    
    // Check forgot password link if exists
    const forgotPasswordLink = page.getByText(/quên mật khẩu|forgot password/i)
    if (await forgotPasswordLink.count() > 0) {
      await expect(forgotPasswordLink).toBeVisible()
    }
  })

  test('shows validation errors for empty form submission', async ({ page }) => {
    // Click submit without filling form
    await page.getByRole('button', { name: /đăng nhập|login/i }).click()

    // Check for validation messages
    await expect(page.getByText(/email.*required|email.*bắt buộc/i)).toBeVisible()
    await expect(page.getByText(/password.*required|mật khẩu.*bắt buộc/i)).toBeVisible()
  })

  test('shows error for invalid email format', async ({ page }) => {
    // Enter invalid email
    await page.getByLabel(/email/i).fill('invalid-email')
    await page.getByLabel(/password|mật khẩu/i).fill('password123')
    await page.getByRole('button', { name: /đăng nhập|login/i }).click()

    // Check for email validation error
    await expect(page.getByText(/invalid.*email|email.*không hợp lệ/i)).toBeVisible()
  })

  test('successfully logs in with valid credentials', async ({ page }) => {
    // Fill in login form
    await page.getByLabel(/email/i).fill('<EMAIL>')
    await page.getByLabel(/password|mật khẩu/i).fill('password123')
    
    // Submit form
    await page.getByRole('button', { name: /đăng nhập|login/i }).click()

    // Wait for navigation to dashboard
    await page.waitForURL('**/dashboard')
    
    // Verify dashboard is loaded
    await expect(page).toHaveURL(/\/dashboard/)
    await expect(page.getByRole('heading', { name: /dashboard|bảng điều khiển/i })).toBeVisible()
  })

  test('shows error message for invalid credentials', async ({ page }) => {
    // Fill in wrong credentials
    await page.getByLabel(/email/i).fill('<EMAIL>')
    await page.getByLabel(/password|mật khẩu/i).fill('wrongpassword')
    
    // Submit form
    await page.getByRole('button', { name: /đăng nhập|login/i }).click()

    // Check for error message
    await expect(page.getByText(/invalid.*credentials|sai.*tài khoản|sai.*mật khẩu/i)).toBeVisible()
    
    // Should remain on login page
    await expect(page).toHaveURL(/\/login/)
  })

  test('password field masks input', async ({ page }) => {
    const passwordInput = page.getByLabel(/password|mật khẩu/i)
    
    // Type password
    await passwordInput.fill('mypassword')
    
    // Check that input type is password
    await expect(passwordInput).toHaveAttribute('type', 'password')
  })

  test('can toggle password visibility if feature exists', async ({ page }) => {
    const passwordInput = page.getByLabel(/password|mật khẩu/i)
    const toggleButton = page.getByRole('button', { name: /show.*password|hiện.*mật khẩu/i })
    
    // Skip test if toggle button doesn't exist
    if (await toggleButton.count() === 0) {
      test.skip()
      return
    }

    // Initially password should be hidden
    await expect(passwordInput).toHaveAttribute('type', 'password')
    
    // Click toggle to show password
    await toggleButton.click()
    await expect(passwordInput).toHaveAttribute('type', 'text')
    
    // Click again to hide password
    await toggleButton.click()
    await expect(passwordInput).toHaveAttribute('type', 'password')
  })

  test('redirects to login when accessing protected route without auth', async ({ page }) => {
    // Try to access dashboard directly
    await page.goto('/dashboard')
    
    // Should redirect to login
    await expect(page).toHaveURL(/\/login/)
  })

  test('logout functionality works', async ({ page, context }) => {
    // First login
    await page.getByLabel(/email/i).fill('<EMAIL>')
    await page.getByLabel(/password|mật khẩu/i).fill('password123')
    await page.getByRole('button', { name: /đăng nhập|login/i }).click()
    
    // Wait for dashboard
    await page.waitForURL('**/dashboard')
    
    // Find and click logout button/link
    const userMenu = page.getByRole('button', { name: /admin|user|profile/i })
    if (await userMenu.count() > 0) {
      await userMenu.click()
    }
    
    await page.getByRole('button', { name: /logout|đăng xuất/i }).click()
    
    // Should redirect to login page
    await expect(page).toHaveURL(/\/login/)
    
    // Try to access dashboard again
    await page.goto('/dashboard')
    
    // Should redirect back to login (not authenticated)
    await expect(page).toHaveURL(/\/login/)
  })

  test('remembers user after page refresh', async ({ page }) => {
    // Login
    await page.getByLabel(/email/i).fill('<EMAIL>')
    await page.getByLabel(/password|mật khẩu/i).fill('password123')
    await page.getByRole('button', { name: /đăng nhập|login/i }).click()
    
    // Wait for dashboard
    await page.waitForURL('**/dashboard')
    
    // Refresh page
    await page.reload()
    
    // Should still be on dashboard
    await expect(page).toHaveURL(/\/dashboard/)
    await expect(page.getByRole('heading', { name: /dashboard|bảng điều khiển/i })).toBeVisible()
  })
})