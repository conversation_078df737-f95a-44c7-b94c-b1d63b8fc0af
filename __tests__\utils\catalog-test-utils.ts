import type { 
  Catalog, 
  Equipment, 
  CatalogsResponse, 
  EquipmentsResponse,
  CreateCatalogRequest,
  UpdateCatalogRequest,
  CheckCodeResponse,
  CreateEquipmentRequest,
  UpdateEquipmentRequest
} from '@/types/catalog'

// Mock catalog data
export const createMockCatalog = (overrides: Partial<Catalog> = {}): Catalog => ({
  id: 'catalog-1',
  catalogCode: 'CAT001',
  catalogName: 'Thiết bị y tế',
  description: '<PERSON>h mục thiết bị y tế chung',
  status: 'ACTIVE',
  equipmentCount: 5,
  createdBy: 'user-1',
  creator: {
    id: 'user-1',
    name: 'Admin User',
    email: '<EMAIL>',
    username: 'admin',
    role: 'ADMIN',
    status: 'ACTIVE',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  ...overrides
})

// Mock equipment data
export const createMockEquipment = (overrides: Partial<Equipment> = {}): Equipment => ({
  id: 'equipment-1',
  equipmentCode: 'EQ001',
  name: 'Máy X-Quang',
  description: 'Máy X-Quang kỹ thuật số',
  model: 'XR-2000',
  manufacturer: 'GE Healthcare',
  unit: 'Cái',
  price: 500000000,
  specifications: {
    power: '220V',
    weight: '500kg'
  },
  catalogId: 'catalog-1',
  catalog: createMockCatalog(),
  status: 'ACTIVE',
  createdBy: 'user-1',
  creator: {
    id: 'user-1',
    name: 'Admin User',
    email: '<EMAIL>',
    username: 'admin',
    role: 'ADMIN',
    status: 'ACTIVE',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  ...overrides
})

// Mock API responses
export const createMockCatalogsResponse = (
  overrides: Partial<CatalogsResponse> = {}
): CatalogsResponse => ({
  catalogs: [
    createMockCatalog({ id: 'catalog-1', catalogCode: 'CAT001', catalogName: 'Thiết bị y tế' }),
    createMockCatalog({ id: 'catalog-2', catalogCode: 'CAT002', catalogName: 'Dụng cụ phẫu thuật' }),
    createMockCatalog({ id: 'catalog-3', catalogCode: 'CAT003', catalogName: 'Thuốc', status: 'INACTIVE' })
  ],
  total: 3,
  page: 1,
  totalPages: 1,
  ...overrides
})

export const createMockEquipmentsResponse = (
  overrides: Partial<EquipmentsResponse> = {}
): EquipmentsResponse => ({
  equipments: [
    createMockEquipment({ id: 'eq-1', equipmentCode: 'EQ001', name: 'Máy X-Quang' }),
    createMockEquipment({ id: 'eq-2', equipmentCode: 'EQ002', name: 'Máy siêu âm' }),
    createMockEquipment({ id: 'eq-3', equipmentCode: 'EQ003', name: 'Máy CT Scanner' })
  ],
  total: 3,
  page: 1,
  totalPages: 1,
  ...overrides
})

// Mock request data
export const createMockCreateCatalogRequest = (
  overrides: Partial<CreateCatalogRequest> = {}
): CreateCatalogRequest => ({
  catalogCode: 'CAT004',
  catalogName: 'Vật tư y tế',
  description: 'Danh mục vật tư y tế tiêu hao',
  ...overrides
})

export const createMockUpdateCatalogRequest = (
  overrides: Partial<UpdateCatalogRequest> = {}
): UpdateCatalogRequest => ({
  catalogName: 'Thiết bị y tế - Cập nhật',
  description: 'Mô tả đã được cập nhật',
  status: 'ACTIVE',
  ...overrides
})

export const createMockCheckCodeResponse = (
  overrides: Partial<CheckCodeResponse> = {}
): CheckCodeResponse => ({
  exists: false,
  ...overrides
})

export const createMockCreateEquipmentRequest = (
  overrides: Partial<CreateEquipmentRequest> = {}
): CreateEquipmentRequest => ({
  equipmentCode: 'EQ004',
  name: 'Máy thở',
  description: 'Máy thở chức năng cao',
  model: 'V-3000',
  manufacturer: 'Drager',
  unit: 'Cái',
  price: 800000000,
  specifications: {
    modes: ['Volume Control', 'Pressure Control', 'SIMV'],
    display: '15 inch touchscreen'
  },
  catalogId: 'catalog-1',
  ...overrides
})

export const createMockUpdateEquipmentRequest = (
  overrides: Partial<UpdateEquipmentRequest> = {}
): UpdateEquipmentRequest => ({
  name: 'Máy thở - Updated',
  description: 'Mô tả đã cập nhật',
  price: 850000000,
  status: 'ACTIVE',
  ...overrides
})

// Mock fetch responses
export const createSuccessResponse = (data: any) => ({
  ok: true,
  status: 200,
  json: async () => data,
  text: async () => JSON.stringify(data),
  blob: async () => new Blob([JSON.stringify(data)], { type: 'application/json' }),
  headers: new Headers({ 'content-type': 'application/json' })
})

export const createErrorResponse = (status: number, message: string) => ({
  ok: false,
  status,
  json: async () => ({ error: message }),
  text: async () => JSON.stringify({ error: message }),
  headers: new Headers({ 'content-type': 'application/json' })
})

// Mock service instance for testing
export const createMockCatalogService = () => {
  return {
    getCatalogs: jest.fn(),
    getCatalogById: jest.fn(),
    createCatalog: jest.fn(),
    updateCatalog: jest.fn(),
    deleteCatalog: jest.fn(),
    checkCatalogCode: jest.fn(),
    getCatalogEquipments: jest.fn(),
    getEquipments: jest.fn(),
    getEquipmentById: jest.fn(),
    createEquipment: jest.fn(),
    updateEquipment: jest.fn(),
    deleteEquipment: jest.fn(),
    checkEquipmentCode: jest.fn(),
    bulkDeleteCatalogs: jest.fn(),
    exportCatalogs: jest.fn(),
    importCatalogs: jest.fn()
  }
}

// Test helpers
export const waitForNextUpdate = () => new Promise(resolve => setTimeout(resolve, 0))

export const mockFetchImplementation = (responses: Record<string, any>) => {
  return jest.fn((url: string, options?: RequestInit) => {
    const method = options?.method || 'GET'
    const key = `${method} ${url}`
    
    // Find matching response
    for (const [pattern, response] of Object.entries(responses)) {
      if (key.includes(pattern) || url.includes(pattern)) {
        if (typeof response === 'function') {
          return Promise.resolve(response(url, options))
        }
        return Promise.resolve(response)
      }
    }
    
    // Default 404 response
    return Promise.resolve(createErrorResponse(404, 'Not found'))
  })
}