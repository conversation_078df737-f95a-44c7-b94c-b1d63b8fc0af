'use client'

import { useState, useRef } from 'react'
import { Upload, File, X, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { useToast } from '@/hooks/useToast'
import { useUploadMedicalAttachment } from '@/hooks/queries/useMedicalRecords'
import type { MedicalRecordAttachment } from '@/types/medicalRecord'

interface MedicalRecordUploadProps {
  medicalRecordId?: string
  onUploadComplete: (attachment: MedicalRecordAttachment & { file?: File }) => void
  disabled?: boolean
}

export function MedicalRecordUpload({ 
  medicalRecordId, 
  onUploadComplete,
  disabled = false
}: MedicalRecordUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const toast = useToast()
  const uploadAttachmentMutation = useUploadMedicalAttachment()

  const isPdfFile = (file: File): boolean => {
    return file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')
  }

  const uploadToServer = async (file: File): Promise<MedicalRecordAttachment> => {
    if (!medicalRecordId) {
      throw new Error('Medical record ID is required for server upload')
    }

    return await uploadAttachmentMutation.mutateAsync({
      medicalRecordId,
      file
    })
  }

  const handleFiles = async (files: FileList | null) => {
    if (!files || files.length === 0) return

    const file = files[0]
    
    // Validate file type (only PDF)
    if (!isPdfFile(file)) {
      toast.error('Chỉ chấp nhận file PDF (.pdf)')
      return
    }
    
    // Validate file size (max 20MB for medical records)
    const maxSize = 20 * 1024 * 1024 // 20MB
    if (file.size > maxSize) {
      toast.error('File quá lớn. Kích thước tối đa là 20MB.')
      return
    }

    setIsUploading(true)

    try {
      let uploadedAttachment
      
      if (!medicalRecordId) {
        // Store the PDF file temporarily with the actual file object
        uploadedAttachment = {
          id: `temp-${Date.now()}-${Math.random()}`,
          fileName: file.name,
          fileUrl: URL.createObjectURL(file),
          fileSize: file.size,
          mimeType: file.type,
          uploadedAt: new Date().toISOString(),
          source: 'local' as const,
          file: file // Store the actual file object
        }
        toast.info(`"${file.name}" sẽ được upload sau khi lưu hồ sơ`)
      } else {
        // Upload directly if medicalRecordId exists
        uploadedAttachment = await uploadToServer(file)
        toast.success(`Đã upload "${file.name}" thành công`)
      }

      if (uploadedAttachment) {
        onUploadComplete(uploadedAttachment)
      }
      
      // Reset input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    } catch (error) {
      console.error('Upload error:', error)
      toast.error('Lỗi khi upload file. Vui lòng thử lại.')
    } finally {
      setIsUploading(false)
    }
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleFiles(event.target.files)
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files)
    }
  }

  return (
    <div>
      <input
        ref={fileInputRef}
        type="file"
        id="medical-record-upload"
        onChange={handleFileSelect}
        disabled={disabled || isUploading}
        className="hidden"
        accept=".pdf"
      />
      
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
        } ${disabled || isUploading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:border-gray-400'}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={() => !disabled && !isUploading && fileInputRef.current?.click()}
      >
        <Upload className="w-12 h-12 mx-auto mb-3 text-gray-400" />
        
        {isUploading ? (
          <div className="space-y-2">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600">Đang upload...</p>
          </div>
        ) : (
          <>
            <p className="text-gray-700 font-medium mb-1">
              Kéo thả file PDF vào đây hoặc click để chọn
            </p>
            <p className="text-sm text-gray-500 mb-3">
              Chỉ chấp nhận file PDF - Tối đa 20MB
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded p-3 text-sm text-blue-700">
              <AlertCircle className="w-4 h-4 inline-block mr-1" />
              File PDF sẽ được lưu trữ an toàn và bảo mật
            </div>
          </>
        )}
      </div>

      <div className="mt-3">
        <Button
          type="button"
          disabled={disabled || isUploading}
          className="w-full flex items-center justify-center gap-2"
          variant="outline"
          onClick={() => fileInputRef.current?.click()}
        >
          <Upload className="w-4 h-4" />
          {isUploading ? 'Đang upload...' : 'Chọn file PDF để upload'}
        </Button>
      </div>
    </div>
  )
}