'use client'

import { useState, useEffect } from 'react'
import { Dialog } from '@/components/ui/Dialog'
import { Button } from '@/components/ui/Button'
import { Select } from '@/components/ui/Select'
import { useToast } from '@/hooks/useToast'
import { 
  useCreateBiddingDocument, 
  useUpdateBiddingDocument 
} from '@/hooks/queries/useBiddingDocuments'
import { BiddingDocumentStatus } from '@/types/biddingDocument'
import { biddingDocumentService } from '@/services/biddingDocumentService'
import type { BiddingDocument } from '@/types/biddingDocument'

interface BiddingDocumentFormDialogProps {
  isOpen: boolean
  onClose: () => void
  mode: 'create' | 'edit'
  document?: BiddingDocument
  onSuccess?: () => void
}

export function BiddingDocumentFormDialog({
  isOpen,
  onClose,
  mode,
  document,
  onSuccess
}: BiddingDocumentFormDialogProps) {
  const toast = useToast()
  const createMutation = useCreateBiddingDocument()
  const updateMutation = useUpdateBiddingDocument()
  
  const [formData, setFormData] = useState({
    code: '',
    name: '',
    description: '',
    customerName: '',
    status: BiddingDocumentStatus.PENDING
  })
  const [isCheckingCode, setIsCheckingCode] = useState(false)
  const [codeError, setCodeError] = useState('')

  useEffect(() => {
    if (mode === 'edit' && document) {
      setFormData({
        code: document.code,
        name: document.name,
        description: document.description || '',
        customerName: document.customerName || '',
        status: document.status
      })
    } else {
      setFormData({
        code: '',
        name: '',
        description: '',
        customerName: '',
        status: BiddingDocumentStatus.PENDING
      })
    }
    setCodeError('')
  }, [mode, document])

  const handleCodeChange = async (code: string) => {
    setFormData(prev => ({ ...prev, code }))
    setCodeError('')
    
    if (code && mode === 'create') {
      setIsCheckingCode(true)
      try {
        const exists = await biddingDocumentService.checkCodeExists(code)
        if (exists) {
          setCodeError('Mã hồ sơ đã tồn tại')
        }
      } catch (error) {
        console.error('Error checking code:', error)
      } finally {
        setIsCheckingCode(false)
      }
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.code.trim() || !formData.name.trim()) {
      toast.error('Vui lòng nhập đầy đủ thông tin bắt buộc')
      return
    }

    if (codeError) {
      toast.error('Vui lòng kiểm tra lại mã hồ sơ')
      return
    }

    try {
      if (mode === 'create') {
        await createMutation.mutateAsync({
          code: formData.code.trim(),
          name: formData.name.trim(),
          description: formData.description.trim(),
          customerName: formData.customerName.trim(),
          status: formData.status
        })
        // Call onSuccess callback if provided
        onSuccess?.()
      } else if (document) {
        await updateMutation.mutateAsync({
          id: document.id,
          data: {
            name: formData.name.trim(),
            description: formData.description.trim(),
            customerName: formData.customerName.trim(),
            status: formData.status
          }
        })
        onClose()
      }
    } catch (error) {
      // Error is handled by mutation
    }
  }

  const statusOptions = [
    { value: BiddingDocumentStatus.PENDING, label: 'Chờ xử lý' },
    { value: BiddingDocumentStatus.IN_PROGRESS, label: 'Đang thực hiện' },
    { value: BiddingDocumentStatus.COMPLETED, label: 'Hoàn thành' },
  ]


  return (
    <Dialog
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? 'Thêm hồ sơ dự thầu' : 'Chỉnh sửa hồ sơ dự thầu'}
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Code */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Mã hồ sơ <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={formData.code}
            onChange={(e) => handleCodeChange(e.target.value)}
            disabled={mode === 'edit'}
            className="w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 disabled:bg-gray-100 dark:disabled:bg-gray-800"
            placeholder="Nhập mã hồ sơ"
          />
          {isCheckingCode && (
            <p className="mt-1 text-sm text-gray-500">Đang kiểm tra...</p>
          )}
          {codeError && (
            <p className="mt-1 text-sm text-red-600">{codeError}</p>
          )}
        </div>

        {/* Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Tên hồ sơ <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            className="w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
            placeholder="Nhập tên hồ sơ"
          />
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Mô tả
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
            placeholder="Nhập mô tả"
          />
        </div>

        {/* Customer Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Tên khách hàng
          </label>
          <input
            type="text"
            value={formData.customerName}
            onChange={(e) => setFormData(prev => ({ ...prev, customerName: e.target.value }))}
            className="w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
            placeholder="Nhập tên khách hàng"
          />
        </div>

        {/* Status */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Trạng thái
          </label>
          <Select
            value={formData.status}
            onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as BiddingDocumentStatus }))}
            selectSize="md"
          >
            {statusOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </Select>
        </div>


        {/* Actions */}
        <div className="flex justify-end gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
          >
            Hủy
          </Button>
          <Button
            type="submit"
            disabled={createMutation.isPending || updateMutation.isPending || isCheckingCode}
          >
            {createMutation.isPending || updateMutation.isPending ? 'Đang xử lý...' : mode === 'create' ? 'Tạo mới' : 'Cập nhật'}
          </Button>
        </div>
      </form>
    </Dialog>
  )
}