import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/db'
import { getServerSession } from '@/lib/auth-server'
import { BiddingDocumentStatus } from '@prisma/client'

const updateBiddingDocumentSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  customerName: z.string().optional(),
  status: z.nativeEnum(BiddingDocumentStatus).optional(),
  equipmentItems: z.array(z.object({
    equipmentId: z.string(),
    pageRange: z.object({
      from: z.number(),
      to: z.number(),
    }).optional(),
  })).optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    const biddingDocument = await prisma.biddingDocument.findUnique({
      where: { id },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
        attachments: {
          orderBy: {
            uploadedAt: 'desc',
          },
        },
      },
    })

    if (!biddingDocument) {
      return NextResponse.json(
        { error: 'Bidding document not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(biddingDocument)
  } catch (error) {
    console.error('Error fetching bidding document:', error)
    return NextResponse.json(
      { error: 'Failed to fetch bidding document' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()
    const validatedData = updateBiddingDocumentSchema.parse(body)

    const biddingDocument = await prisma.biddingDocument.update({
      where: { id },
      data: validatedData,
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
        attachments: {
          orderBy: {
            uploadedAt: 'desc',
          },
        },
      },
    })

    return NextResponse.json(biddingDocument)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.issues },
        { status: 400 }
      )
    }
    console.error('Error updating bidding document:', error)
    return NextResponse.json(
      { error: 'Failed to update bidding document' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    await prisma.biddingDocument.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Bidding document deleted successfully' })
  } catch (error) {
    console.error('Error deleting bidding document:', error)
    return NextResponse.json(
      { error: 'Failed to delete bidding document' },
      { status: 500 }
    )
  }
}