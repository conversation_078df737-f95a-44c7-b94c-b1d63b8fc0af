# Hướng dẫn sử dụng Tailwind CSS v4

## <PERSON><PERSON><PERSON> l<PERSON>
1. [Tổng quan](#tổng-quan)
2. [<PERSON><PERSON><PERSON> hình CSS-first](#cấu-hình-css-first)
3. [<PERSON><PERSON> thống màu sắc](#hệ-thống-màu-sắc)
4. [Dark Mode](#dark-mode)
5. [Styling Components](#styling-components)
6. [Utilities phổ biến](#utilities-phổ-biến)
7. [Best Practices](#best-practices)
8. [Migration từ v3](#migration-từ-v3)

## Tổng quan

Tailwind CSS v4 trong dự án này sử dụng cách tiếp cận **CSS-first configuration** thay vì file JavaScript config. Tất cả cấu hình theme được thực hiện trực tiếp trong CSS thông qua directive `@theme`.

### Cấu trúc file chính:
- `/src/app/globals.css` - File cấu hình theme chính
- `/postcss.config.mjs` - <PERSON><PERSON><PERSON> hình PostCSS với Tailwind plugin
- Không cần file `tailwind.config.js`!

### Dependencies:
```json
{
  "tailwindcss": "^4.1.11",
  "@tailwindcss/postcss": "^4"
}
```

## Cấu hình CSS-first

### 1. Import Tailwind
```css
@import "tailwindcss";
```

### 2. Định nghĩa variant
```css
@variant dark (&:where(.dark, .dark *));
```

### 3. Cấu hình theme với @theme
```css
@theme {
  /* Định nghĩa màu sắc */
  --color-primary-50: #faf5ff;
  --color-primary-100: #f3e8ff;
  --color-primary-500: #a855f7;
  --color-primary-600: #9333ea;
  --color-primary-700: #7e22ce;
  
  /* Có thể thêm spacing, font-size, v.v. */
}
```

## Hệ thống màu sắc

### Định nghĩa màu trong @theme

Dự án sử dụng hệ thống màu với các scale từ 50-900:

```css
@theme {
  /* Primary - Purple Tech Theme */
  --color-primary-50: #faf5ff;
  --color-primary-100: #f3e8ff;
  --color-primary-200: #e9d5ff;
  --color-primary-300: #d8b4fe;
  --color-primary-400: #c084fc;
  --color-primary-500: #a855f7;
  --color-primary-600: #9333ea;
  --color-primary-700: #7e22ce;
  --color-primary-800: #6b21a8;
  --color-primary-900: #581c87;
  
  /* Secondary - Gray */
  --color-secondary-50: #f9fafb;
  --color-secondary-600: #4b5563;
  
  /* Danger - Red */
  --color-danger-600: #dc2626;
  --color-danger-700: #b91c1c;
  
  /* Success - Green */
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  
  /* Warning - Yellow */
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  
  /* Accent - Pink/Fuchsia */
  --color-accent-500: #ec4899;
  --color-accent-600: #db2777;
}
```

### Sử dụng màu trong components

```tsx
// Sử dụng trực tiếp với Tailwind classes
<button className="bg-primary-600 hover:bg-primary-700">
  Click me
</button>

// Với border
<div className="border-secondary-300 dark:border-secondary-600">
  Content
</div>

// Với text
<p className="text-danger-600 dark:text-danger-400">
  Error message
</p>
```

### Dynamic Theme với CSS Variables

Để hỗ trợ chuyển đổi theme runtime:

```css
/* Light theme (mặc định) */
:root {
  --primary: 147 51 234; /* RGB values cho purple-600 */
  --primary-hover: 126 34 206; /* RGB values cho purple-700 */
  --secondary: 75 85 99;
  --danger: 220 38 38;
}

/* Dark theme */
.dark {
  --primary: 192 132 252; /* purple-400 */
  --primary-hover: 216 180 254; /* purple-300 */
  --secondary: 156 163 175;
  --danger: 248 113 113;
}

/* Custom color themes */
.theme-green {
  --primary: 34 197 94; /* green-600 */
  --primary-hover: 22 163 74; /* green-700 */
}
```

Sử dụng CSS variables:
```tsx
// Với RGB values
<div className="bg-[rgb(var(--primary))] hover:bg-[rgb(var(--primary-hover))]">
  Dynamic color
</div>

// Với opacity
<div className="bg-[rgb(var(--primary)/0.5)]">
  50% opacity
</div>
```

## Dark Mode

### Cấu hình Dark Mode

1. **Định nghĩa variant** trong globals.css:
```css
@variant dark (&:where(.dark, .dark *));
```

2. **CSS Variables cho dark mode**:
```css
.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
  
  /* Điều chỉnh màu cho dark mode */
  --primary: 192 132 252;
  --primary-hover: 216 180 254;
}
```

3. **Sử dụng trong components**:
```tsx
// Text colors
<p className="text-gray-900 dark:text-gray-100">Content</p>

// Background colors
<div className="bg-white dark:bg-gray-800">Card</div>

// Border colors
<input className="border-gray-300 dark:border-gray-600" />

// Hover states
<button className="hover:bg-gray-50 dark:hover:bg-gray-700">
  Button
</button>
```

### Dark Mode Effects

Thêm hiệu ứng đặc biệt cho dark mode:

```css
/* Purple glow effect cho buttons */
.dark .bg-primary-600:hover,
.dark .bg-primary-700:hover {
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.5);
}

/* Tech-style focus ring */
.dark {
  --tw-ring-offset-color: #1e1b4b;
}
```

## Styling Components

### 1. Form Inputs

**Tiêu chuẩn cho tất cả inputs:**
- Height: `h-10` (40px)
- Padding: `px-3 py-2`
- Border radius: `rounded-md`
- Focus state: `focus:ring-2 focus:ring-blue-500`

```tsx
// Text Input
<input
  type="text"
  className="
    w-full h-10 px-3 py-2 
    border border-gray-300 dark:border-gray-600 
    rounded-md 
    hover:border-gray-400 dark:hover:border-gray-500 
    focus:ring-2 focus:ring-blue-500 focus:border-blue-500 
    bg-white dark:bg-gray-800 
    text-gray-900 dark:text-gray-100 
    transition-colors
  "
/>

// With icon
<div className="relative">
  <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4" />
  <input className="pl-10 pr-3 py-2 ..." />
</div>
```

### 2. Buttons

**Primary Button:**
```tsx
<button className="
  flex items-center gap-2 
  px-4 py-2 
  bg-primary-600 text-white 
  rounded-md 
  hover:bg-primary-700 
  transition-colors
">
  <PlusIcon className="w-5 h-5" />
  Add New
</button>
```

**Secondary Button:**
```tsx
<button className="
  px-4 py-2 
  border border-gray-300 dark:border-gray-600 
  rounded-md 
  text-gray-700 dark:text-gray-300 
  hover:bg-gray-50 dark:hover:bg-gray-800
  transition-colors
">
  Cancel
</button>
```

**Using Button Component với CVA:**
```tsx
import { Button } from '@/components/ui/Button';

<Button variant="primary" size="md">
  Click me
</Button>

<Button variant="danger" size="sm" isLoading>
  Deleting...
</Button>
```

### 3. Cards & Containers

```tsx
// Card container
<div className="
  bg-white dark:bg-gray-800 
  rounded-lg 
  shadow 
  p-6
">
  <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
    Card Title
  </h2>
</div>

// With hover effect
<div className="
  bg-white dark:bg-gray-800 
  rounded-lg 
  shadow 
  hover:shadow-lg 
  transition-shadow 
  p-4
">
  Content
</div>
```

### 4. Tables

```tsx
// Table container
<div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
    {/* Header */}
    <thead className="bg-gray-50 dark:bg-gray-900">
      <tr>
        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
          Name
        </th>
      </tr>
    </thead>
    
    {/* Body */}
    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
      <tr className="hover:bg-gray-50 dark:hover:bg-gray-700">
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
          Data
        </td>
      </tr>
    </tbody>
  </table>
</div>
```

## Utilities phổ biến

### Spacing
```css
/* Padding */
p-6    /* Page padding */
p-4    /* Card padding */
px-4 py-2    /* Button padding */
px-3 py-2    /* Input padding */

/* Margin */
mb-6    /* Section margins */
gap-4   /* Grid/flex gaps */
```

### Typography
```css
/* Font sizes */
text-3xl    /* Page titles */
text-xl     /* Section titles */
text-base   /* Body text */
text-sm     /* Small text */

/* Font weight */
font-semibold    /* Headings */
font-medium      /* Subheadings */
font-normal      /* Body */

/* Text colors */
text-gray-900 dark:text-gray-100    /* Primary text */
text-gray-700 dark:text-gray-300    /* Secondary text */
text-gray-500 dark:text-gray-400    /* Muted text */
```

### Layout
```css
/* Flexbox */
flex items-center justify-between
flex items-center gap-2

/* Grid */
grid grid-cols-1 md:grid-cols-4 gap-4

/* Position */
relative
absolute top-1/2 -translate-y-1/2

/* Width/Height */
w-full
h-10    /* Standard input height */
min-w-full
```

### Effects
```css
/* Shadows */
shadow          /* Card shadow */
shadow-lg       /* Elevated shadow */
shadow-xl       /* Modal shadow */

/* Transitions */
transition-colors       /* Color transitions */
transition-shadow       /* Shadow transitions */
transition-all         /* All properties */

/* Animations */
animate-spin           /* Loading spinner */
animate-pulse         /* Skeleton loader */
```

## Best Practices

### 1. Luôn hỗ trợ Dark Mode
```tsx
// ✅ Đúng
<div className="bg-white dark:bg-gray-800">

// ❌ Sai
<div className="bg-white">
```

### 2. Sử dụng semantic colors
```tsx
// ✅ Đúng - Sử dụng primary/secondary/danger
<button className="bg-primary-600 hover:bg-primary-700">

// ❌ Sai - Hardcode màu cụ thể
<button className="bg-purple-600 hover:bg-purple-700">
```

### 3. Consistent spacing
```tsx
// ✅ Đúng - Sử dụng spacing scale nhất quán
<div className="p-4 mb-6">

// ❌ Sai - Arbitrary values
<div className="p-[17px] mb-[23px]">
```

### 4. Component composition
```tsx
// ✅ Đúng - Tạo reusable component
const Card = ({ children }) => (
  <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
    {children}
  </div>
);

// ❌ Sai - Copy paste classes everywhere
```

### 5. Sử dụng CVA cho variants
```tsx
// ✅ Đúng - CVA cho multiple variants
const buttonVariants = cva(
  'base-classes',
  {
    variants: {
      variant: {
        primary: 'primary-classes',
        secondary: 'secondary-classes'
      }
    }
  }
);

// ❌ Sai - Conditional classes phức tạp
className={`${isPrimary ? 'primary' : 'secondary'} ${isLarge ? 'large' : 'small'}`}
```

### 6. Focus states
```tsx
// ✅ Đúng - Luôn có focus state
<button className="focus:ring-2 focus:ring-primary-500">

// ❌ Sai - Không có focus indicator
<button className="hover:bg-gray-100">
```

## Migration từ v3

### Điểm khác biệt chính:

1. **Không có tailwind.config.js**
   - v3: Cấu hình trong JavaScript
   - v4: Cấu hình trong CSS với `@theme`

2. **PostCSS config**
   - v3: `tailwindcss: {}`
   - v4: `"@tailwindcss/postcss": {}`

3. **Theme tokens**
   - v3: `theme.extend.colors` trong JS
   - v4: `--color-*` trong `@theme`

4. **Custom utilities**
   - v3: `addUtilities` trong plugin
   - v4: Định nghĩa trực tiếp trong CSS

5. **Import method**
   - v3: `@tailwind base/components/utilities`
   - v4: `@import "tailwindcss"`

### Ví dụ migration:

**v3 (tailwind.config.js):**
```javascript
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          600: '#9333ea'
        }
      }
    }
  }
}
```

**v4 (globals.css):**
```css
@theme {
  --color-primary-600: #9333ea;
}
```

## Tips & Tricks

### 1. Sử dụng arbitrary values với CSS variables
```tsx
// Với opacity
<div className="bg-[rgb(var(--primary)/0.1)]">
  10% opacity background
</div>

// Custom spacing
<div className="p-[var(--custom-spacing)]">
  Custom padding
</div>
```

### 2. Combine với CSS modules nếu cần
```css
/* component.module.css */
.custom {
  @apply bg-primary-600 hover:bg-primary-700;
  /* Custom CSS */
}
```

### 3. Debug với DevTools
- Inspect element để xem generated classes
- Check CSS variables trong :root và .dark
- Sử dụng Tailwind DevTools extension

### 4. Performance tips
- Purge unused CSS trong production
- Sử dụng dynamic imports cho large components
- Lazy load heavy styling dependencies

## Resources

- [Tailwind CSS v4 Docs](https://tailwindcss.com/docs)
- [PostCSS Documentation](https://postcss.org/)
- [Class Variance Authority](https://cva.style/docs)
- Project files:
  - `/src/app/globals.css` - Main theme config
  - `/src/components/ui/` - Reusable components
  - `/CLAUDE.md` - Project conventions