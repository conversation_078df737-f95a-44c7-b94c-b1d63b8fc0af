'use client'

import { useState, useRef } from 'react'
import { Upload, File, X, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { useToast } from '@/hooks/useToast'
import { useUploadAttachment } from '@/hooks/queries/useBiddingDocuments'
import type { BiddingDocumentAttachment } from '@/types/biddingDocument'

interface BiddingDocumentUploadProps {
  biddingDocumentId?: string
  onUploadComplete: (attachment: BiddingDocumentAttachment) => void
  disabled?: boolean
}

export function BiddingDocumentUpload({ 
  biddingDocumentId, 
  onUploadComplete,
  disabled = false
}: BiddingDocumentUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const toast = useToast()
  const uploadAttachmentMutation = useUploadAttachment()

  const isWordFile = (file: File): boolean => {
    const wordTypes = [
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ]
    
    const wordExtensions = ['.doc', '.docx']
    
    return wordTypes.includes(file.type) || 
           wordExtensions.some(ext => file.name.toLowerCase().endsWith(ext))
  }

  const isPdfFile = (file: File): boolean => {
    return file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')
  }

  const uploadToGoogleDrive = async (file: File): Promise<BiddingDocumentAttachment> => {
    console.log(file)
    const formData = new FormData()
    console.log(formData)
    formData.append('file', file)

    const response = await fetch('/api/google-drive/upload', {
      method: 'POST',
      body: formData
    })

    if (!response.ok) {
      throw new Error('Google Drive upload failed')
    }

    const data = await response.json()

    return {
      id: `temp-${Date.now()}-${Math.random()}`,
      fileName: data.fileName || file.name,
      fileUrl: data.webViewLink || data.editUrl,
      fileSize: file.size,
      mimeType: data.mimeType,
      uploadedAt: new Date().toISOString(),
      source: 'google_drive',
      googleDriveId: data.fileId,
      googleDriveUrl: data.webViewLink || data.editUrl
    }
  }

  const uploadToServer = async (file: File): Promise<BiddingDocumentAttachment> => {
    if (!biddingDocumentId) {
      throw new Error('Bidding document ID is required for server upload')
    }

    return await uploadAttachmentMutation.mutateAsync({
      biddingDocumentId,
      file
    })
  }

  const handleFiles = async (files: FileList | null) => {
    if (!files || files.length === 0) return

    const file = files[0]
    
    // Validate file type (only Word and PDF)
    if (!isWordFile(file) && !isPdfFile(file)) {
      toast.error('Chỉ chấp nhận file Word (.doc, .docx) và PDF (.pdf)')
      return
    }
    
    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('File quá lớn. Kích thước tối đa là 10MB.')
      return
    }

    setIsUploading(true)

    try {
      let uploadedAttachment
      
      if (isWordFile(file)) {
        // Upload Word files to Google Drive
        uploadedAttachment = await uploadToGoogleDrive(file)
      
        toast.success(`Đã upload "${file.name}" lên Google Drive`)
      } else if (isPdfFile(file)) {
        // Upload PDF files to server
        if (!biddingDocumentId) {
          // Store the PDF file temporarily in the attachment list
          uploadedAttachment = {
            id: `temp-${Date.now()}-${Math.random()}`,
            fileName: file.name,
            fileUrl: URL.createObjectURL(file),
            fileSize: file.size,
            mimeType: file.type,
            uploadedAt: new Date().toISOString(),
            source: 'local' as const
          }
          toast.info(`"${file.name}" sẽ được upload sau khi lưu hồ sơ`)
        } else {
          uploadedAttachment = await uploadToServer(file)
          toast.success(`Đã upload "${file.name}" lên server`)
        }
      }

      if (uploadedAttachment) {
        onUploadComplete(uploadedAttachment)
      }
      
      // Reset input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    } catch (error) {
      console.error('Upload error:', error)
      toast.error('Lỗi khi upload file. Vui lòng thử lại.')
    } finally {
      setIsUploading(false)
    }
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleFiles(event.target.files)
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files)
    }
  }

  return (
    <div>
      <input
        ref={fileInputRef}
        type="file"
        id="bidding-document-upload"
        onChange={handleFileSelect}
        disabled={disabled || isUploading}
        className="hidden"
        accept=".pdf,.doc,.docx"
      />
      
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
        } ${disabled || isUploading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:border-gray-400'}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={() => !disabled && !isUploading && fileInputRef.current?.click()}
      >
        <Upload className="w-12 h-12 mx-auto mb-3 text-gray-400" />
        
        {isUploading ? (
          <div className="space-y-2">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600">Đang upload...</p>
          </div>
        ) : (
          <>
            <p className="text-gray-700 font-medium mb-1">
              Kéo thả file vào đây hoặc click để chọn
            </p>
            <p className="text-sm text-gray-500 mb-3">
              Chỉ chấp nhận: Word (.doc, .docx) và PDF (.pdf) - Tối đa 10MB
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded p-3 text-sm text-blue-700">
              <AlertCircle className="w-4 h-4 inline-block mr-1" />
              File Word sẽ được upload lên Google Drive, file PDF lên server
            </div>
          </>
        )}
      </div>

      <div className="mt-3">
        <Button
          type="button"
          disabled={disabled || isUploading}
          className="w-full flex items-center justify-center gap-2"
          variant="outline"
          onClick={() => fileInputRef.current?.click()}
        >
          <Upload className="w-4 h-4" />
          {isUploading ? 'Đang upload...' : 'Chọn file để upload'}
        </Button>
      </div>
    </div>
  )
}