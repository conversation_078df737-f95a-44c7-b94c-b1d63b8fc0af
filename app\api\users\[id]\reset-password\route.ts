import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { verifyAccessToken, hashPassword, AuditAction } from '@/lib/auth'
import { getTokenFromRequest } from '@/lib/auth-helpers'

const DEFAULT_PASSWORD = 'admin123'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const token = await getTokenFromRequest(request)

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const payload = await verifyAccessToken(token)
    if (!payload || payload.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const existingUser = await prisma.user.findUnique({
      where: { id },
    })

    if (!existingUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const passwordHash = await hashPassword(DEFAULT_PASSWORD)

    const user = await prisma.user.update({
      where: { id },
      data: {
        passwordHash,
        loginAttempts: 0,
        lockedUntil: null,
      },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        role: true,
        department: true,
        phone: true,
        avatar: true,
        status: true,
        emailVerified: true,
        twoFactorEnabled: true,
        createdAt: true,
        updatedAt: true,
      },
    })

    await prisma.auditLog.create({
      data: {
        userId: payload.userId,
        action: AuditAction.PASSWORD_RESET,
        details: {
          targetUserId: user.id,
          targetUsername: user.username,
          resetToDefault: true,
        },
      },
    })

    return NextResponse.json({ 
      message: 'Password reset successfully',
      user 
    })
  } catch (error) {
    console.error('Reset password error:', error)
    return NextResponse.json(
      { error: 'Failed to reset password' },
      { status: 500 }
    )
  }
}