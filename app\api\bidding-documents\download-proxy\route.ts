import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from '@/lib/auth-server';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the URL from request body
    const { url, fileName } = await request.json();
    
    if (!url) {
      return NextResponse.json({ error: 'URL is required' }, { status: 400 });
    }

    // Fetch the file from the external URL
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': '*/*',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    if (!response.ok) {
      console.error('Failed to fetch file:', response.status, response.statusText);
      return NextResponse.json(
        { error: `Failed to fetch file: ${response.statusText}` },
        { status: response.status }
      );
    }

    // Get the file content
    const blob = await response.blob();
    
    // Determine content type
    const contentType = response.headers.get('content-type') || 
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

    // Return the file as a response
    return new NextResponse(blob, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${fileName || 'download.xlsx'}"`,
        'Cache-Control': 'no-cache',
      },
    });

  } catch (error) {
    console.error('Proxy download error:', error);
    return NextResponse.json(
      { error: 'Failed to download file through proxy' },
      { status: 500 }
    );
  }
}