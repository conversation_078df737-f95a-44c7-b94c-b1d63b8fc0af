import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getServerSession } from '@/lib/auth-server'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const searchParams = request.nextUrl.searchParams
    const code = searchParams.get('code')

    if (!code) {
      return NextResponse.json(
        { error: 'Code parameter is required' },
        { status: 400 }
      )
    }

    const existingDocument = await prisma.biddingDocument.findUnique({
      where: { code },
    })

    return NextResponse.json({ exists: !!existingDocument })
  } catch (error) {
    console.error('Error checking bidding document code:', error)
    return NextResponse.json(
      { error: 'Failed to check bidding document code' },
      { status: 500 }
    )
  }
}