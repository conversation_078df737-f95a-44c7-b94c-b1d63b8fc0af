'use client';

import { useState, useEffect } from 'react';
import { FileText, AlertCircle } from 'lucide-react';
import { UnifiedDocumentUpload } from './UnifiedDocumentUpload';
import { GoogleDriveDocumentList } from './GoogleDriveDocumentList';
import { useToast } from '@/hooks/useToast';
import { useDeleteDocument } from '@/hooks/queries/useEquipment';
import type { EquipmentDocument } from '@/types/equipment';

interface GoogleDriveAttachmentsProps {
  equipmentId?: string;
  initialDocuments?: EquipmentDocument[];
  onDocumentsChange?: (documents: EquipmentDocument[]) => void;
  onDeletedDocumentsChange?: (deletedIds: string[]) => void;
  isEdit?: boolean;
}

export function GoogleDriveAttachments({
  equipmentId,
  initialDocuments = [],
  onDocumentsChange,
  onDeletedDocumentsChange,
  isEdit = false
}: GoogleDriveAttachmentsProps) {
  const [documents, setDocuments] = useState<EquipmentDocument[]>(initialDocuments);
  const [deletedDocumentIds, setDeletedDocumentIds] = useState<string[]>([]);
  const [isGoogleDriveConfigured, setIsGoogleDriveConfigured] = useState(true);
  const toast = useToast();

  useEffect(() => {
    checkGoogleDriveConfig();
  }, []);

  useEffect(() => {
    // Update documents when initialDocuments change (e.g., when equipment data is loaded)
    setDocuments(initialDocuments);
  }, [initialDocuments]);

  const checkGoogleDriveConfig = async () => {
    try {
      const response = await fetch('/api/auth/google/status');
      const data = await response.json();
      setIsGoogleDriveConfigured(data.authenticated);
    } catch (error) {
      console.error('Lỗi khi kiểm tra cấu hình Google Drive:', error);
      setIsGoogleDriveConfigured(false);
    }
  };


  const handleUploadComplete = async (document: EquipmentDocument) => {
    try {
      const updatedDocuments = [...documents, document];
      setDocuments(updatedDocuments);
      onDocumentsChange?.(updatedDocuments);
    } catch (error) {
      console.error('Lỗi khi xử lý tải lên:', error);
      toast.error('Lỗi khi xử lý file upload');
    }
  };

  const handleDelete = async (documentId: string) => {
    try {
      // Only update local state - actual deletion happens on form submit
      const updatedDocuments = documents.filter(doc => doc.id !== documentId);
      setDocuments(updatedDocuments);
      onDocumentsChange?.(updatedDocuments);
      
      // Track deleted document IDs if it's an existing document
      const deletedDoc = documents.find(doc => doc.id === documentId);
      if (deletedDoc && !deletedDoc.id.startsWith('temp-')) {
        const newDeletedIds = [...deletedDocumentIds, documentId];
        setDeletedDocumentIds(newDeletedIds);
        onDeletedDocumentsChange?.(newDeletedIds);
      }
      
      toast.info('Tài liệu sẽ được xóa khi bạn cập nhật thiết bị');
    } catch (error) {
      console.error('Lỗi khi xóa tài liệu:', error);
      toast.error('Lỗi khi xóa tài liệu');
    }
  };


  return (
    <div className="space-y-6">
      {/* Show warning if Google Drive is not configured */}
      {!isGoogleDriveConfigured && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <AlertCircle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
            <div className="text-sm text-yellow-800">
              <p className="font-medium mb-1">Google Drive chưa được cấu hình</p>
              <p>Vui lòng cấu hình Google Drive credentials trong file .env.local để sử dụng tính năng này.</p>
            </div>
          </div>
        </div>
      )}

      {/* Upload Actions */}
      {isGoogleDriveConfigured && (
        <UnifiedDocumentUpload
          equipmentId={equipmentId}
          onUploadComplete={handleUploadComplete}
          disabled={!isEdit && !equipmentId}
        />
      )}

      {/* Instructions for create mode */}
      {!isEdit && !equipmentId && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <AlertCircle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
            <div className="text-sm text-yellow-800">
              <p className="font-medium mb-1">Lưu ý:</p>
              <p>Tài liệu sẽ được liên kết với thiết bị sau khi tạo mới. File Word/Excel sẽ được upload lên Google Drive, các file khác sẽ được lưu trên server.</p>
            </div>
          </div>
        </div>
      )}

      {/* Document List */}
      <GoogleDriveDocumentList
        documents={documents}
        onDelete={handleDelete}
        canDelete={isEdit || !!equipmentId}
        canEdit={isEdit || !!equipmentId}
      />
    </div>
  );
}