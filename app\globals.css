@import "tailwindcss";

/* Define dark mode variant */
@variant dark (&:where(.dark, .dark *));

/* Theme configuration */
@theme {
  /* Primary - Blue for bidding system */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;
  
  /* Secondary - Gray */
  --color-secondary-50: #f9fafb;
  --color-secondary-100: #f3f4f6;
  --color-secondary-200: #e5e7eb;
  --color-secondary-300: #d1d5db;
  --color-secondary-400: #9ca3af;
  --color-secondary-500: #6b7280;
  --color-secondary-600: #4b5563;
  --color-secondary-700: #374151;
  --color-secondary-800: #1f2937;
  --color-secondary-900: #111827;
  
  /* Success - Green */
  --color-success-50: #f0fdf4;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  
  /* Danger - Red */
  --color-danger-50: #fef2f2;
  --color-danger-500: #ef4444;
  --color-danger-600: #dc2626;
  --color-danger-700: #b91c1c;
  
  /* Warning - Yellow */
  --color-warning-50: #fffbeb;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
}

/* React Datepicker Custom Styles */
.react-datepicker-wrapper {
  width: 100%;
}

.react-datepicker {
  font-family: inherit;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.react-datepicker__header {
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  padding-top: 0.5rem;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}

.react-datepicker__current-month {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.react-datepicker__day-names {
  display: flex;
  justify-content: space-around;
}

.react-datepicker__day-name {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  width: 2.25rem;
  line-height: 2.25rem;
}

.react-datepicker__day {
  color: #1f2937;
  width: 2.25rem;
  line-height: 2.25rem;
  margin: 0.125rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.react-datepicker__day:hover {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.react-datepicker__day--selected,
.react-datepicker__day--in-selecting-range,
.react-datepicker__day--in-range {
  background-color: #3b82f6;
  color: white;
  font-weight: 500;
}

.react-datepicker__day--selected:hover,
.react-datepicker__day--in-selecting-range:hover,
.react-datepicker__day--in-range:hover {
  background-color: #2563eb;
}

.react-datepicker__day--keyboard-selected {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.react-datepicker__day--range-start,
.react-datepicker__day--range-end {
  background-color: #1d4ed8;
  color: white;
  font-weight: 600;
}

.react-datepicker__day--disabled {
  color: #9ca3af;
  cursor: not-allowed;
}

.react-datepicker__day--disabled:hover {
  background-color: transparent;
}

.react-datepicker__month-container {
  padding: 0.5rem;
}

.react-datepicker__navigation {
  top: 0.75rem;
}

.react-datepicker__navigation-icon::before {
  border-color: #6b7280;
}

.react-datepicker__navigation:hover *::before {
  border-color: #1f2937;
}

/* Dark mode styles */
.dark .react-datepicker {
  background-color: #1f2937;
  border-color: #374151;
}

.dark .react-datepicker__header {
  background-color: #111827;
  border-bottom-color: #374151;
}

.dark .react-datepicker__current-month {
  color: #f3f4f6;
}

.dark .react-datepicker__day-name {
  color: #9ca3af;
}

.dark .react-datepicker__day {
  color: #f3f4f6;
}

.dark .react-datepicker__day:hover {
  background-color: #374151;
  color: #3b82f6;
}

.dark .react-datepicker__day--selected,
.dark .react-datepicker__day--in-selecting-range,
.dark .react-datepicker__day--in-range {
  background-color: #3b82f6;
  color: white;
}

.dark .react-datepicker__day--range-start,
.dark .react-datepicker__day--range-end {
  background-color: #2563eb;
}

.dark .react-datepicker__day--disabled {
  color: #4b5563;
}

.dark .react-datepicker__navigation-icon::before {
  border-color: #9ca3af;
}

.dark .react-datepicker__navigation:hover *::before {
  border-color: #f3f4f6;
}

/* CSS Variables for runtime theme switching */
:root {
  --primary: 37 99 235; /* RGB values for primary-600 */
  --primary-hover: 29 78 216; /* RGB values for primary-700 */
  --secondary: 75 85 99;
  --danger: 220 38 38;
  --success: 22 163 74;
  --warning: 217 119 6;
  
  /* Background and foreground */
  --background: 255 255 255;
  --foreground: 17 24 39;
}

/* Dark theme */
.dark {
  --primary: 96 165 250; /* primary-400 */
  --primary-hover: 147 197 253; /* primary-300 */
  --secondary: 156 163 175;
  --danger: 248 113 113;
  --success: 134 239 172;
  --warning: 251 191 36;
  
  --background: 17 24 39;
  --foreground: 243 244 246;
}

/* Base styles */
body {
  background-color: rgb(var(--background));
  color: rgb(var(--foreground));
}

/* Smooth transitions for theme switching */
* {
  @apply transition-colors duration-200;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-400 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500 dark:bg-gray-500;
}

/* Enhanced Date Picker Styles */
.react-datepicker-popper {
  z-index: 9999 !important;
}



/* Enhanced Date Range Picker Styles */
.enhanced-date-picker .react-datepicker {
  @apply border-0 bg-transparent shadow-none;
  font-family: inherit;
  width: 100%;
}

/* React Day Picker Styles for shadcn/ui */
.rdp {
  --rdp-cell-size: 36px;
  --rdp-accent-color: #3b82f6;
  --rdp-background-color: #dbeafe;
  --rdp-accent-color-dark: #2563eb;
  --rdp-background-color-dark: #1e3a8a;
  --rdp-outline: 2px solid transparent;
  --rdp-outline-selected: 2px solid currentColor;
}

.dark .rdp {
  --rdp-accent-color: #60a5fa;
  --rdp-background-color: #1e3a8a;
}

.enhanced-date-picker .react-datepicker__header {
  @apply bg-transparent border-0 pb-2;
}

.enhanced-date-picker .react-datepicker__current-month {
  @apply text-gray-900 dark:text-gray-100 font-bold text-lg mb-2;
}

.enhanced-date-picker .react-datepicker__navigation {
  @apply w-8 h-8 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors;
  border: none !important;
}

.enhanced-date-picker .react-datepicker__navigation-icon::before {
  @apply border-gray-500 dark:border-gray-400;
  border-width: 2px 2px 0 0;
}

.enhanced-date-picker .react-datepicker__navigation:hover .react-datepicker__navigation-icon::before {
  @apply border-gray-700 dark:border-gray-200;
}

.enhanced-date-picker .react-datepicker__month-container {
  @apply float-left;
  width: 280px;
}

.enhanced-date-picker .react-datepicker__month {
  @apply m-0;
}

.enhanced-date-picker .react-datepicker__day-names {
  @apply border-b border-gray-200 dark:border-gray-700 pb-2 mb-3;
}

.enhanced-date-picker .react-datepicker__day-name {
  @apply text-gray-600 dark:text-gray-400 font-semibold text-xs uppercase tracking-wide;
  width: 2.5rem;
  height: 2rem;
}

.enhanced-date-picker .react-datepicker__week {
  @apply mb-1;
}

.enhanced-date-picker .react-datepicker__day {
  @apply text-gray-700 dark:text-gray-300 font-medium transition-all duration-150 m-0.5;
  width: 2.25rem;
  height: 2.25rem;
  line-height: 2.25rem;
  border-radius: 0.5rem;
}

.enhanced-date-picker .react-datepicker__day:hover {
  @apply bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 transform scale-105;
}

.enhanced-date-picker .react-datepicker__day--today {
  @apply bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 font-bold ring-2 ring-blue-200 dark:ring-blue-800;
}

.enhanced-date-picker .react-datepicker__day--selected {
  @apply bg-blue-600 text-white shadow-lg ring-2 ring-blue-300 dark:ring-blue-500 transform scale-105;
}

.enhanced-date-picker .react-datepicker__day--selected:hover {
  @apply bg-blue-700;
}

.enhanced-date-picker .react-datepicker__day--in-selecting-range {
  @apply bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200;
}

.enhanced-date-picker .react-datepicker__day--in-range {
  @apply bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200;
}

.enhanced-date-picker .react-datepicker__day--range-start {
  @apply bg-blue-600 text-white shadow-lg ring-2 ring-blue-300 dark:ring-blue-500;
}

.enhanced-date-picker .react-datepicker__day--range-end {
  @apply bg-blue-600 text-white shadow-lg ring-2 ring-blue-300 dark:ring-blue-500;
}

.enhanced-date-picker .react-datepicker__day--disabled {
  @apply text-gray-400 dark:text-gray-600 cursor-not-allowed;
}

.enhanced-date-picker .react-datepicker__day--disabled:hover {
  @apply bg-transparent transform-none;
}

.enhanced-date-picker .react-datepicker__day--outside-month {
  @apply text-gray-400 dark:text-gray-600;
}