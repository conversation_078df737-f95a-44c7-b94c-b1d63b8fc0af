@import "tailwindcss";

/* Define dark mode variant */
@variant dark (&:where(.dark, .dark *));

/* Theme configuration */
@theme {
  /* Primary - Blue for bidding system */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;
  
  /* Secondary - Gray */
  --color-secondary-50: #f9fafb;
  --color-secondary-100: #f3f4f6;
  --color-secondary-200: #e5e7eb;
  --color-secondary-300: #d1d5db;
  --color-secondary-400: #9ca3af;
  --color-secondary-500: #6b7280;
  --color-secondary-600: #4b5563;
  --color-secondary-700: #374151;
  --color-secondary-800: #1f2937;
  --color-secondary-900: #111827;
  
  /* Success - Green */
  --color-success-50: #f0fdf4;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  
  /* Danger - Red */
  --color-danger-50: #fef2f2;
  --color-danger-500: #ef4444;
  --color-danger-600: #dc2626;
  --color-danger-700: #b91c1c;
  
  /* Warning - Yellow */
  --color-warning-50: #fffbeb;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
}



/* CSS Variables for runtime theme switching */
:root {
  --primary: 37 99 235; /* RGB values for primary-600 */
  --primary-hover: 29 78 216; /* RGB values for primary-700 */
  --secondary: 75 85 99;
  --danger: 220 38 38;
  --success: 22 163 74;
  --warning: 217 119 6;
  
  /* Background and foreground */
  --background: 255 255 255;
  --foreground: 17 24 39;
  
  /* React DayPicker default variables */
  --rdp-range_start-background: #3b82f6;
  --rdp-range_start-color: white;
  --rdp-range_end-background: #3b82f6;
  --rdp-range_end-color: white;
  --rdp-range_middle-background: #dbeafe;
  --rdp-range_middle-color: #1e40af;
  --rdp-disabled-opacity: 0.5;
  --rdp-outside-opacity: 0.5;
}

/* Dark theme */
.dark {
  --primary: 96 165 250; /* primary-400 */
  --primary-hover: 147 197 253; /* primary-300 */
  --secondary: 156 163 175;
  --danger: 248 113 113;
  --success: 134 239 172;
  --warning: 251 191 36;
  
  --background: 17 24 39;
  --foreground: 243 244 246;
  
  /* React DayPicker dark mode variables */
  --rdp-range_start-background: #2563eb;
  --rdp-range_start-color: white;
  --rdp-range_end-background: #2563eb;
  --rdp-range_end-color: white;
  --rdp-range_middle-background: #1e3a8a;
  --rdp-range_middle-color: #93c5fd;
}

/* Base styles */
body {
  background-color: rgb(var(--background));
  color: rgb(var(--foreground));
}

/* Smooth transitions for theme switching */
* {
  @apply transition-colors duration-200;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-400 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500 dark:bg-gray-500;
}


/* React Day Picker Custom Styles */
.rdp-custom {
  --rdp-cell-size: 40px;
  --rdp-accent-color: #3b82f6;
  --rdp-background-color: #dbeafe;
  --rdp-selected-bg: #3b82f6;
  --rdp-selected-hover: #2563eb;
  
  /* Range selection colors */
  --rdp-range_start-background: #3b82f6;
  --rdp-range_start-color: white;
  --rdp-range_end-background: #3b82f6;
  --rdp-range_end-color: white;
  --rdp-range_middle-background: #dbeafe;
  --rdp-range_middle-color: #1e40af;
  
  /* Other required variables */
  --rdp-day-font: inherit;
  --rdp-selected-border: 2px solid #3b82f6;
  --rdp-selected-color: white;
  --rdp-disabled-opacity: 0.5;
  --rdp-outside-opacity: 0.5;
  
  font-family: inherit;
  width: 100%;
  max-width: 320px;
  margin: 0 auto;
}

.dark .rdp-custom {
  --rdp-accent-color: #60a5fa;
  --rdp-background-color: #1e3a8a;
  --rdp-selected-bg: #2563eb;
  --rdp-selected-hover: #1d4ed8;
  
  /* Range selection colors for dark mode */
  --rdp-range_start-background: #2563eb;
  --rdp-range_start-color: white;
  --rdp-range_end-background: #2563eb;
  --rdp-range_end-color: white;
  --rdp-range_middle-background: #1e3a8a;
  --rdp-range_middle-color: #93c5fd;
  
  /* Other required variables for dark mode */
  --rdp-selected-border: 2px solid #2563eb;
  --rdp-selected-color: white;
}

/* Override default DayPicker styles */
.rdp-day_button:hover:not([disabled]) {
  @apply bg-blue-50 dark:bg-blue-900/20;
}

.rdp-selected .rdp-day_button {
  @apply bg-blue-500 text-white hover:bg-blue-600;
}

.rdp-range_middle .rdp-day_button {
  background-color: var(--rdp-range_middle-background);
  color: var(--rdp-range_middle-color);
}

.rdp-range_start .rdp-day_button,
.rdp-range_end .rdp-day_button {
  background-color: var(--rdp-range_start-background);
  color: var(--rdp-range_start-color);
  font-weight: 600;
}

.rdp-range_start .rdp-day_button:hover,
.rdp-range_end .rdp-day_button:hover {
  background-color: var(--rdp-selected-hover);
}

.rdp-today {
  @apply font-bold text-blue-600 dark:text-blue-400;
}

.rdp-disabled {
  @apply text-gray-300 dark:text-gray-700 cursor-not-allowed;
}

.rdp-outside {
  @apply text-gray-400 dark:text-gray-600;
}

.rdp-month_caption {
  @apply font-semibold text-gray-900 dark:text-gray-100 text-base mb-2;
}

.rdp-weekday {
  @apply text-gray-600 dark:text-gray-400 text-xs font-medium;
}

/* Ensure range selection styles are applied */
.rdp-day.rdp-range_start,
.rdp-day.rdp-range_end {
  position: relative;
}

.rdp-day.rdp-range_middle {
  background-color: var(--rdp-range_middle-background);
  color: var(--rdp-range_middle-color);
}

/* Fix for selected days within range */
.rdp-day.rdp-selected:not(.rdp-range_start):not(.rdp-range_end) {
  background-color: var(--rdp-range_middle-background);
  color: var(--rdp-range_middle-color);
}

