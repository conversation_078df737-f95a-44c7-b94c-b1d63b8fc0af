import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { verifyToken } from '@/lib/auth';
import * as XLSX from 'xlsx';

export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('token')?.value;

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const catalogId = searchParams.get('catalogId');
    const manufacturer = searchParams.get('manufacturer');
    const status = searchParams.get('status');

    // Build where clause
    const where: Record<string, string> = {};
    if (catalogId) where.catalogId = catalogId;
    if (manufacturer) where.manufacturer = manufacturer;
    if (status) where.status = status;

    // Fetch equipment data
    const equipments = await prisma.equipment.findMany({
      where,
      include: {
        catalog: true,
        creator: {
          select: {
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Transform data for Excel
    const exportData = equipments.map(eq => {
      // Format specifications
      let specString = '';
      if (eq.specifications && typeof eq.specifications === 'object') {
        const specs = eq.specifications as Record<string, string | number | boolean>;
        specString = Object.entries(specs)
          .map(([key, value]) => `${key}:${value}`)
          .join(';');
      }

      return {
        'Mã thiết bị': eq.equipmentCode,
        'Tên thiết bị': eq.name,
        'Mô tả': eq.description || '',
        'Model': eq.model || '',
        'Nhà sản xuất': eq.manufacturer || '',
        'Đơn vị': eq.unit || '',
        'Giá': eq.price || '',
        'Mã danh mục': eq.catalog?.catalogCode || '',
        'Tên danh mục': eq.catalog?.catalogName || '',
        'Thông số kỹ thuật': specString,
        'Trạng thái': eq.status === 'ACTIVE' ? 'Hoạt động' : 'Không hoạt động',
        'Người tạo': eq.creator?.name || '',
        'Ngày tạo': new Date(eq.createdAt).toLocaleDateString('vi-VN'),
        'Ngày cập nhật': new Date(eq.updatedAt).toLocaleDateString('vi-VN')
      };
    });

    // Create workbook
    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Thiết bị');

    // Set column widths
    worksheet['!cols'] = [
      { wch: 15 }, // Mã thiết bị
      { wch: 30 }, // Tên thiết bị
      { wch: 40 }, // Mô tả
      { wch: 15 }, // Model
      { wch: 20 }, // Nhà sản xuất
      { wch: 10 }, // Đơn vị
      { wch: 15 }, // Giá
      { wch: 15 }, // Mã danh mục
      { wch: 25 }, // Tên danh mục
      { wch: 50 }, // Thông số kỹ thuật
      { wch: 15 }, // Trạng thái
      { wch: 20 }, // Người tạo
      { wch: 15 }, // Ngày tạo
      { wch: 15 }  // Ngày cập nhật
    ];

    // Add header styling (optional - requires additional libraries for full styling)
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
    for (let C = range.s.c; C <= range.e.c; ++C) {
      const address = XLSX.utils.encode_col(C) + "1";
      if (!worksheet[address]) continue;
      worksheet[address].s = {
        font: { bold: true },
        fill: { fgColor: { rgb: "EFEFEF" } }
      };
    }

    // Convert to buffer
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    // Create filename with timestamp
    const timestamp = new Date().toISOString().split('T')[0].replace(/-/g, '');
    const filename = `DanhSachThietBi_${timestamp}.xlsx`;

    // Return file
    return new NextResponse(buffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="${filename}"`
      }
    });

  } catch (error) {
    console.error('Export equipment error:', error);
    return NextResponse.json(
      { error: 'Failed to export equipment' },
      { status: 500 }
    );
  }
}