'use client';

import { useState, useRef } from 'react';
import { Button } from '@/components/ui/Button';
import { Upload, FileText, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { equipmentService } from '@/services/equipmentService';
import type { GoogleDriveFile } from './GoogleDriveUpload';

interface UnifiedDocumentUploadProps {
  equipmentId?: string;
  onUploadComplete: (document: any) => void;
  disabled?: boolean;
}

export function UnifiedDocumentUpload({
  equipmentId,
  onUploadComplete,
  disabled = false
}: UnifiedDocumentUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const toast = useToast();

  const isWordOrExcel = (file: File): boolean => {
    const wordExcelTypes = [
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];
    
    const wordExcelExtensions = ['.doc', '.docx', '.xls', '.xlsx'];
    
    return wordExcelTypes.includes(file.type) || 
           wordExcelExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
  };

  const uploadToGoogleDrive = async (file: File): Promise<any> => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch('/api/google-drive/upload', {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      throw new Error('Tải lên Google Drive thất bại');
    }

    const data = await response.json();

    return {
      id: `temp-${Date.now()}-${Math.random()}`,
      equipmentId: equipmentId || '',
      fileName: data.fileName || file.name,
      fileUrl: data.webViewLink || data.editUrl,
      fileSize: file.size,
      mimeType: data.mimeType,
      uploadedAt: new Date().toISOString(),
      googleDriveId: data.fileId,
      googleDriveUrl: data.webViewLink || data.editUrl,
      googleDriveEmbedUrl: data.embedUrl,
      googleDriveIconUrl: data.iconUrl || ''
    };
  };

  const uploadToServer = async (file: File): Promise<any> => {
    if (!equipmentId) {
      throw new Error('Cần có ID thiết bị để tải lên máy chủ');
    }

    const result = await equipmentService.uploadDocument(equipmentId, file);
    
    if (!result.success || !result.document) {
      throw new Error(result.error || 'Tải lên thất bại');
    }
    
    return result.document;
  };

  const handleFiles = async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const file = files[0];
    
    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('File quá lớn. Kích thước tối đa là 10MB.');
      return;
    }

    setIsUploading(true);

    try {
      let uploadedDocument;
      
      if (isWordOrExcel(file)) {
        // Upload to Google Drive
        uploadedDocument = await uploadToGoogleDrive(file);
        toast.success(`Đã upload "${file.name}" lên Google Drive`);
      } else {
        // Upload to server
        if (!equipmentId) {
          toast.error('Vui lòng lưu thiết bị trước khi upload file khác Word/Excel');
          return;
        }
        uploadedDocument = await uploadToServer(file);
        toast.success(`Đã upload "${file.name}" lên server`);
      }

      onUploadComplete(uploadedDocument);
      
      // Reset input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Lỗi tải lên:', error);
      toast.error('Lỗi khi upload file. Vui lòng thử lại.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleFiles(event.target.files);
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  return (
    <div>
      <input
        ref={fileInputRef}
        type="file"
        id="unified-document-upload"
        onChange={handleFileSelect}
        disabled={disabled || isUploading}
        className="hidden"
        accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.zip,.rar"
      />
      
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
        } ${disabled || isUploading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:border-gray-400'}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={() => !disabled && !isUploading && fileInputRef.current?.click()}
      >
        <Upload className="w-12 h-12 mx-auto mb-3 text-gray-400" />
        
        {isUploading ? (
          <div className="space-y-2">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600">Đang tải lên...</p>
          </div>
        ) : (
          <>
            <p className="text-gray-700 font-medium mb-1">
              Kéo thả file vào đây hoặc click để chọn
            </p>
            <p className="text-sm text-gray-500 mb-3">
              Hỗ trợ: PDF, Word, Excel, Ảnh, ZIP (Tối đa 10MB)
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded p-3 text-sm text-blue-700">
              <AlertCircle className="w-4 h-4 inline-block mr-1" />
              Word/Excel sẽ được upload lên Google Drive, các file khác lên server
            </div>
          </>
        )}
      </div>

      <div className="mt-3">
        <Button
          type="button"
          disabled={disabled || isUploading}
          className="w-full flex items-center justify-center gap-2"
          variant="outline"
          onClick={() => fileInputRef.current?.click()}
        >
          <Upload className="w-4 h-4" />
          {isUploading ? 'Đang tải lên...' : 'Chọn file để upload'}
        </Button>
      </div>
    </div>
  );
}