import { BaseService } from './baseService'
import { tokenManager } from '@/lib/tokenManager'
import type {
  BiddingDocument,
  CreateBiddingDocumentRequest,
  UpdateBiddingDocumentRequest,
  BiddingDocumentFilter,
  BiddingDocumentListResponse,
  GenerateTechnicalResponseRequest,
  UpdateEvaluationRequest,
  UploadTenderDocumentRequest,
  BiddingDocumentAttachment
} from '@/types/biddingDocument'

export class BiddingDocumentService extends BaseService {
  private static instance: BiddingDocumentService

  private constructor() {
    super({ baseUrl: '', useLocalApi: true })
  }

  static getInstance(): BiddingDocumentService {
    if (!BiddingDocumentService.instance) {
      BiddingDocumentService.instance = new BiddingDocumentService()
    }
    return BiddingDocumentService.instance
  }

  // Get list of bidding documents with filters
  async getBiddingDocuments(
    filters?: BiddingDocumentFilter,
    signal?: AbortSignal
  ): Promise<BiddingDocumentListResponse> {
    const params = new URLSearchParams()
    
    if (filters?.search) params.append('search', filters.search)
    if (filters?.status?.length) params.append('status', filters.status.join(','))
    if (filters?.customerName) params.append('customerName', filters.customerName)
    if (filters?.createdFrom) params.append('createdFrom', filters.createdFrom)
    if (filters?.createdTo) params.append('createdTo', filters.createdTo)
    if (filters?.page) params.append('page', filters.page.toString())
    if (filters?.limit) params.append('limit', filters.limit.toString())

    return this.get<BiddingDocumentListResponse>(
      `/bidding-documents?${params.toString()}`,
      signal
    )
  }

  // Get single bidding document by ID
  async getBiddingDocument(id: string, signal?: AbortSignal): Promise<BiddingDocument> {
    return this.get<BiddingDocument>(`/bidding-documents/${id}`, signal)
  }

  // Create new bidding document
  async createBiddingDocument(data: CreateBiddingDocumentRequest): Promise<BiddingDocument> {
    return this.post<BiddingDocument>('/bidding-documents', data)
  }

  // Update bidding document
  async updateBiddingDocument(
    id: string,
    data: UpdateBiddingDocumentRequest
  ): Promise<BiddingDocument> {
    return this.put<BiddingDocument>(`/bidding-documents/${id}`, data)
  }

  // Delete bidding document
  async deleteBiddingDocument(id: string): Promise<void> {
    return this.delete(`/bidding-documents/${id}`)
  }

  // Upload tender document for equipment item
  async uploadTenderDocument(data: {
    biddingDocumentId: string
    equipmentItemId: string
    file: File
    pageRange?: { from: number; to: number }
  }): Promise<{ id: string; fileUrl: string }> {
    const formData = new FormData()
    formData.append('file', data.file)
    formData.append('equipmentItemId', data.equipmentItemId)
    if (data.pageRange) {
      formData.append('pageFrom', data.pageRange.from.toString())
      formData.append('pageTo', data.pageRange.to.toString())
    }

    // Get the access token from token manager
    const token = tokenManager.getAccessToken()
    if (!token) {
      throw new Error('No authentication token available')
    }

    const response = await fetch(
      `/api/bidding-documents/${data.biddingDocumentId}/tender-documents`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      }
    )

    if (!response.ok) {
      throw new Error('Failed to upload tender document')
    }

    return response.json()
  }

  // Delete tender document
  async deleteTenderDocument(
    biddingDocumentId: string,
    documentId: string
  ): Promise<void> {
    return this.delete(
      `/bidding-documents/${biddingDocumentId}/tender-documents/${documentId}`
    )
  }

  // Generate technical response document
  async generateTechnicalResponse(
    data: GenerateTechnicalResponseRequest
  ): Promise<{ fileUrl: string; fileName: string }> {
    return this.post<{ fileUrl: string; fileName: string }>(
      `/bidding-documents/${data.biddingDocumentId}/generate-response`,
      { format: data.format }
    )
  }

  // Update AI-generated technical response data
  async updateTechnicalResponseData(
    biddingDocumentId: string,
    data: Record<string, any>
  ): Promise<BiddingDocument> {
    return this.put<BiddingDocument>(
      `/bidding-documents/${biddingDocumentId}/technical-response`,
      data
    )
  }

  // Export technical response document
  async exportTechnicalResponse(
    biddingDocumentId: string,
    format: 'WORD' | 'PDF' | 'EXCEL'
  ): Promise<Blob> {
    // Get the access token from token manager
    const token = tokenManager.getAccessToken()
    if (!token) {
      throw new Error('No authentication token available')
    }

    const response = await fetch(
      `/api/bidding-documents/${biddingDocumentId}/export?format=${format}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    )

    if (!response.ok) {
      throw new Error('Failed to export technical response')
    }

    return response.blob()
  }

  // Update evaluation
  async updateEvaluation(data: UpdateEvaluationRequest): Promise<BiddingDocument> {
    return this.put<BiddingDocument>(
      `/bidding-documents/${data.biddingDocumentId}/evaluation`,
      { notes: data.notes }
    )
  }

  // Check if code exists
  async checkCodeExists(code: string): Promise<boolean> {
    try {
      const response = await this.get<{ exists: boolean }>(
        `/bidding-documents/check-code?code=${encodeURIComponent(code)}`
      )
      return response.exists
    } catch {
      return false
    }
  }

  // Add equipment item to bidding document
  async addEquipmentItem(
    biddingDocumentId: string,
    equipmentId: string,
    pageRange?: { from: number; to: number }
  ): Promise<BiddingDocument> {
    return this.post<BiddingDocument>(
      `/bidding-documents/${biddingDocumentId}/equipment-items`,
      { equipmentId, pageRange }
    )
  }

  // Remove equipment item from bidding document
  async removeEquipmentItem(
    biddingDocumentId: string,
    equipmentItemId: string
  ): Promise<void> {
    return this.delete(
      `/bidding-documents/${biddingDocumentId}/equipment-items/${equipmentItemId}`
    )
  }

  // Upload attachment for bidding document
  async uploadAttachment(
    biddingDocumentId: string,
    file: File
  ): Promise<BiddingDocumentAttachment> {
    const formData = new FormData()
    formData.append('file', file)

    // Get the access token from token manager
    const token = tokenManager.getAccessToken()
    if (!token) {
      throw new Error('No authentication token available')
    }

    const response = await fetch(
      `/api/bidding-documents/${biddingDocumentId}/attachments`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      }
    )

    if (!response.ok) {
      throw new Error('Failed to upload attachment')
    }

    return response.json()
  }

  // Save multiple attachments (for newly created documents)
  async saveAttachments(
    biddingDocumentId: string,
    attachments: BiddingDocumentAttachment[]
  ): Promise<void> {
    return this.post(
      `/bidding-documents/${biddingDocumentId}/attachments/batch`,
      { attachments }
    )
  }

  // Delete attachment from bidding document
  async deleteAttachment(
    biddingDocumentId: string,
    attachmentId: string
  ): Promise<void> {
    return this.delete(
      `/bidding-documents/${biddingDocumentId}/attachments/${attachmentId}`
    )
  }
}

export const biddingDocumentService = BiddingDocumentService.getInstance()