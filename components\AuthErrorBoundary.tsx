'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { authUtils } from '@/utils/authUtils'
import { Alert<PERSON>riangle, RefreshCw, LogIn } from 'lucide-react'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
}

export class AuthErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false, error: null, errorInfo: null }
  }

  static getDerivedStateFromError(error: Error): State {
    // Kiểm tra nếu là auth-related error
    if (
      error.message.includes('auth') || 
      error.message.includes('401') || 
      error.message.includes('403') ||
      error.message.includes('Unauthorized') ||
      error.message.includes('session')
    ) {
      // Clear cached auth data nếu có lỗi auth
      authUtils.clearCachedUser()
    }
    
    return { 
      hasError: true, 
      error,
      errorInfo: null
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Auth Error Boundary caught an error:', error, errorInfo)
    
    this.setState({
      errorInfo
    })
    
    // Log to monitoring service nếu có
    if (typeof window !== 'undefined' && (window as any).trackError) {
      (window as any).trackError('AuthErrorBoundary', error, errorInfo)
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: null, errorInfo: null })
    
    // Reload trang để retry
    if (typeof window !== 'undefined') {
      window.location.reload()
    }
  }

  handleLogin = () => {
    // Clear tất cả auth data và redirect về login
    authUtils.clearCachedUser()
    if (typeof window !== 'undefined') {
      window.location.href = '/login'
    }
  }

  render() {
    if (this.state.hasError) {
      // Nếu có custom fallback, sử dụng nó
      if (this.props.fallback) {
        return this.props.fallback
      }

      const isAuthError = this.state.error?.message && (
        this.state.error.message.includes('auth') ||
        this.state.error.message.includes('401') ||
        this.state.error.message.includes('403') ||
        this.state.error.message.includes('Unauthorized')
      )

      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 px-4">
          <div className="max-w-md w-full">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center">
              <div className="flex justify-center mb-4">
                <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
                  <AlertTriangle className="w-8 h-8 text-red-600 dark:text-red-400" />
                </div>
              </div>
              
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
                {isAuthError ? 'Lỗi Xác Thực' : 'Có Lỗi Xảy Ra'}
              </h2>
              
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                {isAuthError 
                  ? 'Phiên đăng nhập của bạn đã hết hạn hoặc không hợp lệ. Vui lòng đăng nhập lại.'
                  : 'Đã xảy ra lỗi không mong muốn. Vui lòng thử lại hoặc liên hệ quản trị viên.'
                }
              </p>

              <div className="space-y-3">
                {isAuthError ? (
                  <button
                    onClick={this.handleLogin}
                    className="w-full flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors"
                  >
                    <LogIn className="w-4 h-4" />
                    Đăng Nhập Lại
                  </button>
                ) : (
                  <button
                    onClick={this.handleRetry}
                    className="w-full flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors"
                  >
                    <RefreshCw className="w-4 h-4" />
                    Thử Lại
                  </button>
                )}
                
                <button
                  onClick={() => window.location.href = '/'}
                  className="w-full text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 px-4 py-2 transition-colors"
                >
                  Về Trang Chủ
                </button>
              </div>

              {/* Debug info in development */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="mt-6 text-left">
                  <summary className="text-sm text-gray-500 cursor-pointer hover:text-gray-700">
                    Chi tiết lỗi (Development)
                  </summary>
                  <div className="mt-2 p-3 bg-gray-100 dark:bg-gray-700 rounded text-xs font-mono text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                    {this.state.error.toString()}
                    {this.state.errorInfo?.componentStack}
                  </div>
                </details>
              )}
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

/**
 * Higher-order component để wrap components với AuthErrorBoundary
 */
export function withAuthErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) {
  return function WrappedComponent(props: P) {
    return (
      <AuthErrorBoundary fallback={fallback}>
        <Component {...props} />
      </AuthErrorBoundary>
    )
  }
}

/**
 * Hook để programmatically trigger error boundary
 */
export function useErrorHandler() {
  return (error: Error, errorInfo?: string) => {
    console.error('Manual error trigger:', error, errorInfo)
    
    // Throw error để trigger error boundary
    throw error
  }
}