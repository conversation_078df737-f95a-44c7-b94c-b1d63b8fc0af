'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Upload, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/useToast';

interface GoogleDriveUploadProps {
  onUploadComplete: (file: GoogleDriveFile) => void;
  disabled?: boolean;
  acceptedTypes?: string[];
}

export interface GoogleDriveFile {
  id: string;
  name: string;
  mimeType: string;
  iconUrl: string;
  url: string;
  embedUrl?: string;
}

export function GoogleDriveUpload({
  onUploadComplete,
  disabled = false,
  acceptedTypes = [
    '.doc',
    '.docx', 
    '.xls',
    '.xlsx',
    '.pdf',
    '.jpg',
    '.jpeg',
    '.png'
  ]
}: GoogleDriveUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const toast = useToast();

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    
    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('File quá lớn. Kích thước tối đa là 10MB.');
      return;
    }

    setIsUploading(true);

    try {
      // Create form data
      const formData = new FormData();
      formData.append('file', file);

      // Upload to Google Drive via API (no auth header needed with refresh token)
      const response = await fetch('/api/google-drive/upload', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error('Upload failed');
      }

      const data = await response.json();

      const googleDriveFile: GoogleDriveFile = {
        id: data.fileId,
        name: data.fileName || file.name,
        mimeType: data.mimeType,
        iconUrl: data.iconUrl || '',
        url: data.webViewLink || data.editUrl,
        embedUrl: data.embedUrl
      };

      onUploadComplete(googleDriveFile);
      toast.success('Upload file thành công!');
      
      // Reset input
      event.target.value = '';
    } catch (error) {
      console.error('Upload error:', error);
      toast.error('Lỗi khi upload file. Vui lòng thử lại.');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="relative">
      <input
        type="file"
        id="google-drive-upload"
        accept={acceptedTypes.join(',')}
        onChange={handleFileSelect}
        disabled={disabled || isUploading}
        className="hidden"
      />
      <Button
        type="button"
        disabled={disabled || isUploading}
        className="flex items-center gap-2"
        variant="outline"
        onClick={() => document.getElementById('google-drive-upload')?.click()}
      >
        <Upload className="w-4 h-4" />
        {isUploading ? 'Đang upload...' : 'Upload lên Google Drive'}
      </Button>
      
      {isUploading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-md">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
        </div>
      )}
    </div>
  );
}