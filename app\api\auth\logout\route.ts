import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { prisma } from '@/lib/db'
import { getClientIp, AuditAction } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const sessionId = cookieStore.get('session-id')?.value
    const clientIp = getClientIp(request)

    if (sessionId) {
      // Find the session to get userId for audit log
      const session = await prisma.session.findUnique({
        where: { id: sessionId },
        select: { userId: true },
      })

      if (session) {
        // Mark the session as logged out instead of deleting
        await prisma.session.update({
          where: { id: sessionId },
          data: {
            status: 'LOGGED_OUT',
            loggedOutAt: new Date(),
          },
        })

        // Create audit log
        await prisma.auditLog.create({
          data: {
            userId: session.userId,
            action: AuditAction.LOGOUT,
            details: { sessionId },
            ipAddress: clientIp,
          },
        })
      }
    }

    // Clear all auth cookies
    cookieStore.delete('auth-token')
    cookieStore.delete('refresh-token')
    cookieStore.delete('session-id')

    return NextResponse.json(
      { message: 'Logged out successfully' },
      {
        status: 200,
        headers: {
          // Clear cookies in the response as well
          'Set-Cookie': [
            'auth-token=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly',
            'refresh-token=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly',
            'session-id=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly',
          ].join(', '),
        },
      }
    )
  } catch (error) {
    console.error('Logout error:', error)
    // Still clear cookies even if there's an error
    const cookieStore = await cookies()
    cookieStore.delete('auth-token')
    cookieStore.delete('refresh-token')
    cookieStore.delete('session-id')

    return NextResponse.json(
      { message: 'Logged out with errors' },
      { status: 200 }
    )
  }
}