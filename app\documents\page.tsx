'use client';

import { useState, useCallback } from 'react';
import { Plus, FileText } from 'lucide-react';
import { MainLayout } from '@/components/layout/MainLayout';
import { DocumentFilters } from '@/components/documents/DocumentFilters';
import { DocumentTable } from '@/components/documents/DocumentTable';
import { DocumentUploadDialog } from '@/components/documents/DocumentUploadDialog';
import { Pagination } from '@/components/ui/Pagination';
import { useDocuments } from '@/hooks/queries/useDocuments';
import type { DocumentFilters as DocumentFiltersType, DocumentType } from '@/types/document';

export default function DocumentsPage() {
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [filters, setFilters] = useState<DocumentFiltersType>({
    page: 1,
    limit: 10,
    sortBy: 'uploadedAt',
    sortOrder: 'desc'
  });

  const { data, isLoading } = useDocuments(filters);

  const handleFiltersChange = useCallback((newFilters: {
    search?: string;
    documentType?: DocumentType | '';
    tags?: string[];
  }) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: 1 // Reset to first page when filters change
    }));
  }, []);

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handlePageSizeChange = (limit: number) => {
    setFilters(prev => ({ ...prev, limit, page: 1 }));
  };

  const handleSort = (sortBy: 'fileName' | 'uploadedAt', sortOrder: 'asc' | 'desc') => {
    setFilters(prev => ({ ...prev, sortBy, sortOrder }));
  };

  return (
    <MainLayout>
      <div className="p-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                Quản lý Tài liệu
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Quản lý các tài liệu, báo cáo và file đính kèm
              </p>
            </div>
            <button
              onClick={() => setUploadDialogOpen(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Thêm tài liệu
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="mb-6">
          <DocumentFilters 
            onFiltersChange={handleFiltersChange}
            initialFilters={filters}
          />
        </div>

        {/* Statistics */}
        {data && (
          <div className="mb-6 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-3">
                <FileText className="h-8 w-8 text-blue-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {data.total}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Tổng số tài liệu
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Table */}
        <DocumentTable 
          documents={data?.documents || []}
          isLoading={isLoading}
          onSort={handleSort}
          sortBy={filters.sortBy}
          sortOrder={filters.sortOrder}
        />

        {/* Pagination */}
        {data && data.totalPages > 1 && (
          <div className="mt-6">
            <Pagination
              currentPage={data.page}
              totalPages={data.totalPages}
              totalRecords={data.total}
              pageSize={filters.limit || 10}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
            />
          </div>
        )}

        {/* Upload Dialog */}
        <DocumentUploadDialog 
          isOpen={uploadDialogOpen}
          onClose={() => setUploadDialogOpen(false)}
        />
      </div>
    </MainLayout>
  );
}