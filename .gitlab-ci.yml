stages:
  - deploy-to-dev

deploy-to-dev-job:
  stage: deploy-to-dev
  before_script:
    - mkdir -p ~/.ssh
    - eval $(ssh-agent -s)
    - echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config
  script:
    - set -e
    - ssh-add <(echo "$CI_DEV_SSH_KEY")
    - |
      while read IP; do
        sleep 2
        ENCODED_DEV_ENV=$(echo "$DEV_ENV" | base64)
        ssh -nv $CI_DEV_USER@$IP "cd /home/<USER>/ && git pull origin development && echo \"$ENCODED_DEV_ENV\" | base64 --decode > .env.local && ./run.sh" 2>&1
        if [ $? -ne 0 ]; then
          exit 1
        fi
      done < $CI_DEV_IP_LIST

  tags: 
    - DEV
  only:
    - development