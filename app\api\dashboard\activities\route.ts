import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from '@/lib/auth-server'
import { prisma } from '@/lib/db'
import { BiddingDocumentStatus } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const activityType = searchParams.get('type') // 'created', 'updated', 'completed'
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')

    const skip = (page - 1) * limit

    // Build activity queries based on different events
    const whereConditions: any = {}
    const dateFilter: any = {}

    if (dateFrom) {
      dateFilter.gte = new Date(dateFrom)
    }
    if (dateTo) {
      const endDate = new Date(dateTo)
      endDate.setHours(23, 59, 59, 999)
      dateFilter.lte = endDate
    }

    let activities: any[] = []

    // Get recent bidding document activities
    const biddingDocActivities = await prisma.biddingDocument.findMany({
      where: {
        ...(Object.keys(dateFilter).length > 0 && {
          OR: [
            { createdAt: dateFilter },
            { updatedAt: dateFilter }
          ]
        }),
        ...(activityType === 'created' && { createdAt: dateFilter }),
        ...(activityType === 'updated' && { updatedAt: dateFilter }),
        ...(activityType === 'completed' && { 
          status: BiddingDocumentStatus.COMPLETED,
          updatedAt: dateFilter
        })
      },
      take: limit,
      skip,
      orderBy: [
        { updatedAt: 'desc' },
        { createdAt: 'desc' }
      ],
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            username: true,
            email: true
          }
        },
        _count: {
          select: {
            attachments: true
          }
        }
      }
    })

    // Transform bidding document activities
    const biddingActivities = biddingDocActivities.flatMap(doc => {
      const baseActivity = {
        id: `bidding-${doc.id}`,
        documentId: doc.id,
        documentCode: doc.code,
        documentName: doc.name,
        customerName: doc.customerName,
        status: doc.status,
        user: doc.creator,
        attachmentCount: doc._count.attachments
      }

      const activities = []

      // Determine activity type based on timestamps and status
      const isNewlyCreated = new Date(doc.createdAt).getTime() === new Date(doc.updatedAt).getTime()
      
      if (!activityType || activityType === 'created') {
        activities.push({
          ...baseActivity,
          type: 'BIDDING_CREATED' as const,
          title: 'Tạo hồ sơ dự thầu mới',
          description: `Đã tạo hồ sơ dự thầu "${doc.name}" cho khách hàng ${doc.customerName || 'chưa xác định'}`,
          timestamp: doc.createdAt.toISOString(),
          icon: 'file-plus'
        })
      }

      if (!isNewlyCreated && (!activityType || activityType === 'updated')) {
        activities.push({
          ...baseActivity,
          type: 'BIDDING_UPDATED' as const,
          title: 'Cập nhật hồ sơ dự thầu',
          description: `Đã cập nhật hồ sơ dự thầu "${doc.name}"`,
          timestamp: doc.updatedAt.toISOString(),
          icon: 'file-edit'
        })
      }

      if (doc.status === BiddingDocumentStatus.COMPLETED && (!activityType || activityType === 'completed')) {
        activities.push({
          ...baseActivity,
          type: 'BIDDING_COMPLETED' as const,
          title: 'Hoàn thành hồ sơ dự thầu',
          description: `Đã hoàn thành hồ sơ dự thầu "${doc.name}"`,
          timestamp: doc.updatedAt.toISOString(),
          icon: 'check-circle'
        })
      }

      return activities
    })

    // Get equipment activities for additional context
    if (!activityType || activityType === 'created') {
      const recentEquipment = await prisma.equipment.findMany({
        where: {
          ...(Object.keys(dateFilter).length > 0 && { createdAt: dateFilter })
        },
        take: Math.floor(limit / 4), // Limit equipment activities to 25% of total
        orderBy: { createdAt: 'desc' },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              username: true,
              email: true
            }
          },
          catalog: {
            select: {
              catalogName: true
            }
          }
        }
      })

      const equipmentActivities = recentEquipment.map(equipment => ({
        id: `equipment-${equipment.id}`,
        type: 'EQUIPMENT_CREATED' as const,
        title: 'Thêm thiết bị mới',
        description: `Đã thêm thiết bị "${equipment.name}" vào danh mục ${equipment.catalog?.catalogName || 'chưa phân loại'}`,
        timestamp: equipment.createdAt.toISOString(),
        icon: 'package',
        documentId: null,
        documentCode: null,
        documentName: null,
        customerName: null,
        status: null,
        user: equipment.creator,
        attachmentCount: 0,
        equipmentId: equipment.id,
        equipmentName: equipment.name,
        catalogName: equipment.catalog?.catalogName
      }))

      activities.push(...equipmentActivities)
    }

    activities = [...biddingActivities, ...activities]

    // Sort all activities by timestamp
    activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

    // Apply pagination to combined results
    const paginatedActivities = activities.slice(0, limit)

    // Get total count for pagination
    const totalBiddingDocs = await prisma.biddingDocument.count({
      where: {
        ...(Object.keys(dateFilter).length > 0 && {
          OR: [
            { createdAt: dateFilter },
            { updatedAt: dateFilter }
          ]
        }),
        ...(activityType === 'completed' && { status: BiddingDocumentStatus.COMPLETED })
      }
    })

    const totalEquipment = (!activityType || activityType === 'created') 
      ? await prisma.equipment.count({
          where: {
            ...(Object.keys(dateFilter).length > 0 && { createdAt: dateFilter })
          }
        })
      : 0

    const total = totalBiddingDocs + totalEquipment

    return NextResponse.json({
      activities: paginatedActivities,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      },
      filters: {
        activityType,
        dateFrom,
        dateTo
      }
    })
  } catch (error) {
    console.error('Error fetching dashboard activities:', error)
    
    let errorMessage = 'Failed to fetch activities'
    let errorDetails = 'Unknown error'
    
    if (error instanceof Error) {
      errorDetails = error.message
      if (error.message.includes('connect')) {
        errorMessage = 'Database connection error'
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Database query timeout'
      }
    }
    
    return NextResponse.json(
      { 
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? errorDetails : 'Internal server error'
      },
      { status: 500 }
    )
  }
}