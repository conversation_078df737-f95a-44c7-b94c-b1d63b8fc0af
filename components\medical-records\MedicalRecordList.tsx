'use client'

import { File, Download, Eye, Trash2, FileText } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import type { MedicalRecordAttachment } from '@/types/medicalRecord'

interface MedicalRecordListProps {
  attachments: MedicalRecordAttachment[]
  onDelete?: (attachmentId: string) => void
  canDelete?: boolean
  canView?: boolean
}

export function MedicalRecordList({
  attachments,
  onDelete,
  canDelete = false,
  canView = true
}: MedicalRecordListProps) {
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleView = (attachment: MedicalRecordAttachment) => {
    window.open(attachment.fileUrl, '_blank')
  }

  const handleDownload = async (attachment: MedicalRecordAttachment) => {
    try {
      const response = await fetch(attachment.fileUrl)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = attachment.fileName
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Download error:', error)
    }
  }

  if (attachments.length === 0) {
    return (
      <div className="text-center py-12 bg-gray-50 dark:bg-gray-900 rounded-lg">
        <FileText className="w-12 h-12 text-gray-400 mx-auto mb-3" />
        <p className="text-gray-500 dark:text-gray-400">Chưa có file PDF nào được tải lên</p>
        <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
          Tải lên file PDF để lưu trữ hồ sơ y tế
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-3">
      {attachments.map((attachment) => (
        <div
          key={attachment.id}
          className="flex items-center justify-between p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-sm transition-shadow"
        >
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center">
                <File className="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {attachment.fileName}
              </h4>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {formatFileSize(attachment.fileSize)} • 
                Tải lên {new Date(attachment.uploadedAt).toLocaleDateString('vi-VN')}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {canView && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleView(attachment)}
                  className="flex items-center gap-1"
                >
                  <Eye className="w-4 h-4" />
                  Xem
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDownload(attachment)}
                  className="flex items-center gap-1"
                >
                  <Download className="w-4 h-4" />
                  Tải xuống
                </Button>
              </>
            )}
            {canDelete && onDelete && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onDelete(attachment.id)}
                className="flex items-center gap-1 text-red-600 hover:text-red-700 border-red-300 hover:border-red-400"
              >
                <Trash2 className="w-4 h-4" />
                Xóa
              </Button>
            )}
          </div>
        </div>
      ))}
    </div>
  )
}