import { BaseService } from './baseService';
import type {
  Equipment,
  EquipmentDocument,
  CreateEquipmentDto,
  UpdateEquipmentDto,
  EquipmentFilter,
  EquipmentListResponse
} from '@/types/equipment';

export class EquipmentService extends BaseService {
  private static instance: EquipmentService;
  
  private constructor() {
    super({ useLocalApi: true });
  }
  
  static getInstance(): EquipmentService {
    if (!EquipmentService.instance) {
      EquipmentService.instance = new EquipmentService();
    }
    return EquipmentService.instance;
  }

  // Equipment CRUD operations
  async getEquipments(filter?: EquipmentFilter, signal?: AbortSignal): Promise<EquipmentListResponse> {
    const params = new URLSearchParams();
    
    if (filter) {
      if (filter.search) params.append('search', filter.search);
      if (filter.catalogId) params.append('catalogId', filter.catalogId);
      if (filter.manufacturer) params.append('manufacturer', filter.manufacturer);
      if (filter.status) params.append('status', filter.status);
      if (filter.page) params.append('page', filter.page.toString());
      if (filter.limit) params.append('limit', filter.limit.toString());
      if (filter.sortBy) params.append('sortBy', filter.sortBy);
      if (filter.sortOrder) params.append('sortOrder', filter.sortOrder);
    }

    const queryString = params.toString();
    const url = `/equipment${queryString ? `?${queryString}` : ''}`;
    
    return this.get<EquipmentListResponse>(url, signal);
  }

  async getEquipmentById(id: string, signal?: AbortSignal): Promise<Equipment> {
    return this.get<Equipment>(`/equipment/${id}`, signal);
  }

  async createEquipment(data: CreateEquipmentDto): Promise<Equipment> {
    return this.post<Equipment>('/equipment', data);
  }

  async updateEquipment(id: string, data: UpdateEquipmentDto): Promise<Equipment> {
    return this.put<Equipment>(`/equipment/${id}`, data);
  }

  async deleteEquipment(id: string): Promise<void> {
    return this.delete<void>(`/equipment/${id}`);
  }

  // Bulk operations
  async importEquipment(file: File): Promise<{ imported: number; failed: number; errors?: string[] }> {
    const formData = new FormData();
    formData.append('file', file);
    
    // Use fetch directly for multipart/form-data
    const response = await fetch(`${this.baseUrl}/equipment/import`, {
      method: 'POST',
      body: formData,
      credentials: 'include'
    });

    if (!response.ok) {
      throw new Error(`Import failed: ${response.statusText}`);
    }

    return response.json();
  }

  async exportEquipment(filter?: EquipmentFilter): Promise<Blob> {
    const params = new URLSearchParams();
    
    if (filter) {
      if (filter.catalogId) params.append('catalogId', filter.catalogId);
      if (filter.manufacturer) params.append('manufacturer', filter.manufacturer);
      if (filter.status) params.append('status', filter.status);
    }

    const queryString = params.toString();
    const url = `/equipment/export${queryString ? `?${queryString}` : ''}`;
    
    const response = await fetch(`${this.baseUrl}${url}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include'
    });

    if (!response.ok) {
      throw new Error(`Export failed: ${response.statusText}`);
    }

    return response.blob();
  }

  // Statistics
  async getEquipmentStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    byCatalog: Record<string, number>;
    byManufacturer: Record<string, number>;
  }> {
    return this.get<{
      total: number;
      active: number;
      inactive: number;
      byCatalog: Record<string, number>;
      byManufacturer: Record<string, number>;
    }>('/equipment/stats');
  }

  // Document management
  async uploadDocument(equipmentId: string, file: File): Promise<{ success: boolean; document?: EquipmentDocument; error?: string }> {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch(`${this.baseUrl}/equipment/${equipmentId}/documents`, {
      method: 'POST',
      body: formData,
      credentials: 'include'
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Document upload failed: ${error}`);
    }

    return response.json();
  }

  async deleteDocument(equipmentId: string, documentId: string): Promise<void> {
    return this.delete<void>(`/equipment/${equipmentId}/documents/${documentId}`);
  }

  async getDocuments(equipmentId: string, signal?: AbortSignal): Promise<EquipmentDocument[]> {
    return this.get<EquipmentDocument[]>(`/equipment/${equipmentId}/documents`, signal);
  }

  async saveDocuments(equipmentId: string, documents: Partial<EquipmentDocument>[]): Promise<EquipmentDocument[]> {
    return this.post<EquipmentDocument[]>(
      `/equipment/${equipmentId}/documents/batch`,
      { documents }
    );
  }

  async refreshGoogleDriveMetadata(fileIds: string[]): Promise<any[]> {
    const response = await this.post<{ success: boolean; files: any[] }>(
      `/google-drive/files/batch`,
      { fileIds }
    );
    
    return response.files;
  }
}

export const equipmentService = EquipmentService.getInstance();