#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Define upload directory structure
const uploadDirs = [
  'uploads',
  'uploads/bidding-documents',
  'uploads/equipment',
  'uploads/documents',
  'public/uploads',
  'public/uploads/bidding-documents',
  'public/uploads/equipment',
  'public/uploads/documents'
];

console.log('Ensuring upload directories exist...\n');

uploadDirs.forEach(dir => {
  const fullPath = path.join(process.cwd(), dir);
  
  try {
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
      console.log(`✅ Created: ${dir}`);
    } else {
      console.log(`✔️  Exists: ${dir}`);
    }
    
    // Create .gitkeep file to ensure directory is tracked
    const gitkeepPath = path.join(fullPath, '.gitkeep');
    if (!fs.existsSync(gitkeepPath)) {
      fs.writeFileSync(gitkeepPath, '');
    }
  } catch (error) {
    console.error(`❌ Failed to create ${dir}:`, error.message);
    process.exit(1);
  }
});

console.log('\n✨ All upload directories are ready!');