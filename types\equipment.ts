export interface EquipmentDocument {
  id: string;
  equipmentId: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
  mimeType: string;
  uploadedAt: string;
  uploadedBy?: string;
  uploader?: {
    id: string;
    name: string;
    email: string;
  };
  // Google Drive integration
  googleDriveId?: string;
  googleDriveUrl?: string;
  googleDriveEmbedUrl?: string;
  googleDriveIconUrl?: string;
}

export interface Equipment {
  id: string;
  equipmentCode: string;
  name: string;
  description?: string;
  model?: string;
  manufacturer?: string;
  unit?: string;
  catalogId: string;
  catalog?: {
    id: string;
    catalogCode: string;
    catalogName: string;
  };
  status: 'ACTIVE' | 'INACTIVE';
  price?: number;
  specifications?: Record<string, any>;
  documents?: EquipmentDocument[];
  createdBy?: string;
  creator?: {
    id: string;
    name: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateEquipmentDto {
  equipmentCode: string;
  name: string;
  description?: string;
  model?: string;
  manufacturer?: string;
  unit?: string;
  catalogId: string;
  status?: 'ACTIVE' | 'INACTIVE';
  price?: number;
  specifications?: Record<string, any>;
}

export interface UpdateEquipmentDto {
  equipmentCode?: string;
  name?: string;
  description?: string;
  model?: string;
  manufacturer?: string;
  unit?: string;
  catalogId?: string;
  status?: 'ACTIVE' | 'INACTIVE';
  price?: number;
  specifications?: Record<string, any>;
}

export interface EquipmentFilter {
  search?: string;
  catalogId?: string;
  manufacturer?: string;
  status?: 'ACTIVE' | 'INACTIVE';
  page?: number;
  limit?: number;
  sortBy?: 'equipmentCode' | 'name' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

export interface EquipmentListResponse {
  data: Equipment[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}