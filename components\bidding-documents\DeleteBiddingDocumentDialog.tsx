'use client'

import { Dialog } from '@/components/ui/Dialog'
import { But<PERSON> } from '@/components/ui/Button'
import { AlertTriangle } from 'lucide-react'
import { useDeleteBiddingDocument } from '@/hooks/queries/useBiddingDocuments'

interface DeleteBiddingDocumentDialogProps {
  documentId: string
  isOpen: boolean
  onClose: () => void
}

export function DeleteBiddingDocumentDialog({
  documentId,
  isOpen,
  onClose
}: DeleteBiddingDocumentDialogProps) {
  const deleteMutation = useDeleteBiddingDocument()

  const handleDelete = async () => {
    try {
      await deleteMutation.mutateAsync(documentId)
      onClose()
    } catch (error) {
      // Error is handled by mutation
    }
  }

  return (
    <Dialog
      isOpen={isOpen}
      onClose={onClose}
      title="Xác nhận xóa"
    >
      <div className="space-y-4">
        <div className="flex items-start gap-3">
          <AlertTriangle className="w-5 h-5 text-red-500 flex-shrink-0 mt-1" />
          <div>
            <p className="text-sm text-gray-700 dark:text-gray-300">
              Bạn có chắc chắn muốn xóa hồ sơ dự thầu này không?
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
              Hành động này không thể hoàn tác. Tất cả dữ liệu liên quan sẽ bị xóa vĩnh viễn.
            </p>
          </div>
        </div>

        <div className="flex justify-end gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={deleteMutation.isPending}
          >
            Hủy
          </Button>
          <Button
            type="button"
            variant="primary"
            onClick={handleDelete}
            disabled={deleteMutation.isPending}
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            {deleteMutation.isPending ? 'Đang xóa...' : 'Xóa'}
          </Button>
        </div>
      </div>
    </Dialog>
  )
}