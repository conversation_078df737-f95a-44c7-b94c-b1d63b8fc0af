import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { verifyAccessToken, AuditAction } from '@/lib/auth'
import { getTokenFromRequest } from '@/lib/auth-helpers'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const token = await getTokenFromRequest(request)

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const payload = await verifyAccessToken(token)
    if (!payload || payload.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    if (id === payload.userId) {
      return NextResponse.json(
        { error: 'Cannot change your own status' },
        { status: 400 }
      )
    }

    const existingUser = await prisma.user.findUnique({
      where: { id },
    })

    if (!existingUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const newStatus = existingUser.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE'

    const user = await prisma.user.update({
      where: { id },
      data: {
        status: newStatus,
      },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        role: true,
        department: true,
        phone: true,
        avatar: true,
        status: true,
        emailVerified: true,
        twoFactorEnabled: true,
        createdAt: true,
        updatedAt: true,
      },
    })

    await prisma.auditLog.create({
      data: {
        userId: payload.userId,
        action: AuditAction.USER_UPDATED,
        details: {
          targetUserId: user.id,
          targetUsername: user.username,
          statusChange: {
            from: existingUser.status,
            to: newStatus,
          },
        },
      },
    })

    return NextResponse.json({ 
      message: `User status changed to ${newStatus}`,
      user 
    })
  } catch (error) {
    console.error('Toggle status error:', error)
    return NextResponse.json(
      { error: 'Failed to toggle status' },
      { status: 500 }
    )
  }
}