'use client';

import { useState, useEffect } from 'react';
import { Search, Filter, X } from 'lucide-react';
import { useDebounce } from '@/hooks/useDebounce';
import { Select } from '@/components/ui/Select';
import { DOCUMENT_TYPES, getDocumentTypeLabel, type DocumentType } from '@/types/document';
import { useDocumentTags } from '@/hooks/queries/useDocuments';

interface DocumentFiltersProps {
  onFiltersChange: (filters: {
    search?: string;
    documentType?: DocumentType | '';
    tags?: string[];
  }) => void;
  initialFilters?: {
    search?: string;
    documentType?: DocumentType | '';
    tags?: string[];
  };
}

export function DocumentFilters({ onFiltersChange, initialFilters = {} }: DocumentFiltersProps) {
  const [search, setSearch] = useState(initialFilters.search || '');
  const [documentType, setDocumentType] = useState<DocumentType | ''>(initialFilters.documentType || '');
  const [selectedTags, setSelectedTags] = useState<string[]>(initialFilters.tags || []);
  
  const debouncedSearch = useDebounce(search, 500);
  const { data: availableTags = [] } = useDocumentTags();

  useEffect(() => {
    onFiltersChange({
      search: debouncedSearch,
      documentType,
      tags: selectedTags
    });
  }, [debouncedSearch, documentType, selectedTags, onFiltersChange]);

  const handleClearFilters = () => {
    setSearch('');
    setDocumentType('');
    setSelectedTags([]);
  };

  const handleTagToggle = (tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const hasActiveFilters = search || documentType || selectedTags.length > 0;

  return (
    <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 space-y-4">
      <div className="flex flex-wrap gap-4">
        {/* Search Input */}
        <div className="flex-1 min-w-[250px]">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Tìm kiếm theo tên file hoặc mô tả..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="w-full h-10 pl-10 pr-3 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
            />
          </div>
        </div>

        {/* Document Type Filter */}
        <div className="w-48">
          <Select
            value={documentType}
            onChange={(e) => setDocumentType(e.target.value as DocumentType | '')}
          >
            <option value="">Tất cả loại</option>
            {Object.values(DOCUMENT_TYPES).map(type => (
              <option key={type} value={type}>
                {getDocumentTypeLabel(type)}
              </option>
            ))}
          </Select>
        </div>

        {/* Clear Filters Button */}
        {hasActiveFilters && (
          <button
            onClick={handleClearFilters}
            className="h-10 px-4 flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <X className="h-4 w-4" />
            Xóa bộ lọc
          </button>
        )}
      </div>

      {/* Tags Filter */}
      {availableTags.length > 0 && (
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Tags:</span>
          </div>
          <div className="flex flex-wrap gap-2">
            {availableTags.map(tag => (
              <button
                key={tag}
                onClick={() => handleTagToggle(tag)}
                className={`px-3 py-1 text-sm rounded-full border transition-colors ${
                  selectedTags.includes(tag)
                    ? 'bg-blue-500 text-white border-blue-500'
                    : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:border-blue-500 hover:text-blue-500 dark:hover:text-blue-400'
                }`}
              >
                {tag}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="text-sm text-gray-600 dark:text-gray-400">
          Đang lọc: 
          {search && <span className="ml-2">Tìm kiếm: &quot;{search}&quot;</span>}
          {documentType && <span className="ml-2">Loại: {getDocumentTypeLabel(documentType)}</span>}
          {selectedTags.length > 0 && <span className="ml-2">Tags: {selectedTags.join(', ')}</span>}
        </div>
      )}
    </div>
  );
}