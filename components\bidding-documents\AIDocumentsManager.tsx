'use client'

import { useState } from 'react'
import { AIDocumentsList } from './AIDocumentsList'
import { AIGenerationPanel } from './AIGenerationPanel'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { FileSpreadsheet, Sparkles, List, Plus } from 'lucide-react'
import { Card } from '@/components/ui/card'
import { useBiddingDocuments } from '@/hooks/queries/useBiddingDocuments'
import { useToast } from '@/hooks/useToast'

export function AIDocumentsManager() {
  const [activeTab, setActiveTab] = useState<'list' | 'create'>('list')
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const toast = useToast()

  // Fetch all bidding documents to extract AI-generated ones
  const { data, isLoading, refetch } = useBiddingDocuments({
    page: 1,
    limit: 100, // Get all documents for now
  })

  // Filter AI-generated documents (those with Excel attachments)
  const aiDocuments = data?.items?.filter(doc => 
    doc.attachments?.some(att => 
      att.mimeType === 'application/vnd.google-apps.spreadsheet' || 
      att.mimeType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
  ) || []

  const handleGenerationComplete = () => {
    // Switch to list view and refresh
    setActiveTab('list')
    setRefreshTrigger(prev => prev + 1)
    refetch()
    toast.success('Hồ sơ AI đã được tạo thành công!')
  }

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-6 bg-gradient-to-br from-purple-50 to-pink-50 border-purple-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-600">Tổng hồ sơ AI</p>
              <p className="text-3xl font-bold text-purple-900">{aiDocuments.length}</p>
            </div>
            <FileSpreadsheet className="w-10 h-10 text-purple-400" />
          </div>
        </Card>
        
        <Card className="p-6 bg-gradient-to-br from-blue-50 to-cyan-50 border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-600">Đã tạo hôm nay</p>
              <p className="text-3xl font-bold text-blue-900">
                {aiDocuments.filter(doc => {
                  const today = new Date().toDateString()
                  return doc.createdAt && new Date(doc.createdAt).toDateString() === today
                }).length}
              </p>
            </div>
            <Plus className="w-10 h-10 text-blue-400" />
          </div>
        </Card>
        
        <Card className="p-6 bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-600">Tổng dung lượng</p>
              <p className="text-2xl font-bold text-green-900">
                {formatFileSize(
                  aiDocuments.reduce((total, doc) => {
                    const aiAttachments = doc.attachments?.filter(att => 
                      att.mimeType === 'application/vnd.google-apps.spreadsheet' || 
                      att.mimeType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    ) || []
                    return total + aiAttachments.reduce((sum, att) => sum + (att.fileSize || 0), 0)
                  }, 0)
                )}
              </p>
            </div>
            <List className="w-10 h-10 text-green-400" />
          </div>
        </Card>
      </div>

      {/* Main Tabs */}
      <Card className="shadow-lg">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'list' | 'create')}>
          <div className="border-b">
            <TabsList className="w-full justify-start h-auto p-0 bg-transparent">
              <TabsTrigger
                value="list"
                className="data-[state=active]:bg-transparent data-[state=active]:shadow-none border-b-2 border-transparent data-[state=active]:border-purple-600 rounded-none px-6 py-4 font-medium"
              >
                <List className="w-4 h-4 mr-2" />
                Danh sách hồ sơ AI
              </TabsTrigger>
              <TabsTrigger
                value="create"
                className="data-[state=active]:bg-transparent data-[state=active]:shadow-none border-b-2 border-transparent data-[state=active]:border-purple-600 rounded-none px-6 py-4 font-medium"
              >
                <Sparkles className="w-4 h-4 mr-2" />
                Tạo hồ sơ mới
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="list" className="p-6">
            <AIDocumentsList
              documents={aiDocuments}
              isLoading={isLoading}
              onRefresh={refetch}
              refreshTrigger={refreshTrigger}
            />
          </TabsContent>

          <TabsContent value="create" className="p-6">
            <AIGenerationPanel
              onGenerationComplete={handleGenerationComplete}
            />
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  )
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}