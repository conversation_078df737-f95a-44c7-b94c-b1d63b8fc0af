import { NextRequest, NextResponse } from 'next/server';
import { google } from 'googleapis';
import { googleDriveService } from '@/services/googleDriveService';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const redirect = searchParams.get('redirect') || '/equipment';
    const isPopup = searchParams.get('popup') === 'true';
    
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_REDIRECT_URI + (isPopup ? '?popup=true' : '')
    );

    const authUrl = oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: [
        'https://www.googleapis.com/auth/drive.file',
        'https://www.googleapis.com/auth/drive.readonly',
        'https://www.googleapis.com/auth/userinfo.email',
        'https://www.googleapis.com/auth/userinfo.profile'
      ],
      state: redirect,
      prompt: 'select_account' // Force account selection
    });
    
    return NextResponse.redirect(authUrl);
  } catch (error) {
    console.error('Error generating Google auth URL:', error);
    return NextResponse.redirect(
      new URL('/equipment?error=auth_failed', request.url)
    );
  }
}