'use client'

import React, { useState } from 'react'
import { SimpleSidebar } from './SimpleSidebar'
import { Menu } from 'lucide-react'
import { ErrorBoundary } from '@/components/ErrorBoundary'
import { AuthErrorBoundary } from '@/components/AuthErrorBoundary'

interface MainLayoutProps {
  children: React.ReactNode
}

export function MainLayout({ children }: MainLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <AuthErrorBoundary>
        <ErrorBoundary>
          <SimpleSidebar isOpen={sidebarOpen} onToggle={() => setSidebarOpen(!sidebarOpen)} />
        </ErrorBoundary>
      </AuthErrorBoundary>
      
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top bar */}
        <header className="bg-white dark:bg-gray-800 shadow-sm lg:hidden">
          <div className="flex items-center justify-between p-4">
            <button
              onClick={() => setSidebarOpen(true)}
              className="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
            >
              <Menu className="w-6 h-6" />
            </button>
            <span className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Smart Bidding System
            </span>
            <div className="w-6" />
          </div>
        </header>

        {/* Main content */}
        <main className="flex-1 overflow-y-auto">
          <ErrorBoundary>
            {children}
          </ErrorBoundary>
        </main>
      </div>
    </div>
  )
}