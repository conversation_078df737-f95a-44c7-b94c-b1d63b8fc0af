import { useState, useEffect, useCallback } from 'react'
import { authUtils } from '@/utils/authUtils'
import { authService } from '@/services/authService'
import type { User } from '@/types/auth'

/**
 * Hook tối ưu hóa cho authentication
 * Cung cấp immediate UI updates với cached data trong khi verify với server
 */
export function useOptimisticAuth() {
  // Initialize với cached user data
  const [optimisticUser, setOptimisticUser] = useState<User | null>(() => 
    authUtils.getCachedUser()
  )
  
  const [isVerifying, setIsVerifying] = useState(false)
  const [serverUser, setServerUser] = useState<User | null>(null)
  const [lastVerified, setLastVerified] = useState<Date | null>(null)

  // Verify với server trong background
  const verifyWithServer = useCallback(async () => {
    if (!authService.isAuthenticated()) {
      setOptimisticUser(null)
      setServerUser(null)
      authUtils.clearCachedUser()
      return
    }

    setIsVerifying(true)
    try {
      const user = await authService.getCurrentUser()
      setServerUser(user)
      setLastVerified(new Date())
      
      // Update cached user
      authUtils.setCachedUser(user)
      
      // Chỉ update optimistic user nếu có thay đổi meaningful
      if (authUtils.hasUserChanged(optimisticUser, user)) {
        setOptimisticUser(user)
      }
    } catch (error) {
      console.error('Server verification failed:', error)
      
      // Nếu là auth error, clear everything
      if ((error as any)?.status === 401 || (error as any)?.status === 403) {
        setOptimisticUser(null)
        setServerUser(null)
        authUtils.clearCachedUser()
      }
      // Với các lỗi khác, giữ nguyên optimistic user để UX tốt hơn
    } finally {
      setIsVerifying(false)
    }
  }, [optimisticUser])

  // Auto verify khi component mount
  useEffect(() => {
    verifyWithServer()
  }, [verifyWithServer])

  // DISABLED: Auto refresh mỗi 5 phút nếu có user
  useEffect(() => {
    if (!optimisticUser) return

    console.log('Automatic 5-minute verification is disabled')
    // DISABLED: No automatic verification interval
    // const interval = setInterval(() => {
    //   verifyWithServer()
    // }, 5 * 60 * 1000) // 5 minutes

    // return () => clearInterval(interval)
  }, [optimisticUser, verifyWithServer])

  return {
    user: optimisticUser,
    serverUser,
    isVerifying,
    isAuthenticated: !!optimisticUser,
    isLoading: !optimisticUser && isVerifying,
    lastVerified,
    refresh: verifyWithServer,
    
    // Helper methods
    getUserDisplayName: () => authUtils.getUserDisplayName(optimisticUser),
    getUserInitial: () => authUtils.getUserInitial(optimisticUser),
    
    // Check if data is stale (server and optimistic are different)
    isDataStale: optimisticUser && serverUser && 
      authUtils.hasUserChanged(optimisticUser, serverUser)
  }
}

/**
 * Hook để quản lý menu items với caching
 */
export function useOptimisticMenuItems(menuItems: any[], user: User | null) {
  const [cachedMenuItems, setCachedMenuItems] = useState<any[]>([])

  useEffect(() => {
    if (user) {
      const filtered = menuItems.filter(item => item.roles.includes(user.role))
      setCachedMenuItems(filtered)
    } else {
      // Fallback với cached user
      const cachedUser = authUtils.getCachedUser()
      if (cachedUser) {
        const filtered = menuItems.filter(item => item.roles.includes(cachedUser.role))
        setCachedMenuItems(filtered)
      } else {
        setCachedMenuItems([])
      }
    }
  }, [user, menuItems])

  return cachedMenuItems
}

/**
 * Hook để handle auth state changes
 */
export function useAuthStateHandler() {
  const [authState, setAuthState] = useState<'loading' | 'authenticated' | 'unauthenticated'>('loading')

  const handleAuthChange = useCallback((user: User | null, isLoading: boolean) => {
    if (isLoading) {
      setAuthState('loading')
    } else if (user) {
      setAuthState('authenticated')
    } else {
      setAuthState('unauthenticated')
    }
  }, [])

  return {
    authState,
    handleAuthChange,
    isLoading: authState === 'loading',
    isAuthenticated: authState === 'authenticated',
    isUnauthenticated: authState === 'unauthenticated'
  }
}