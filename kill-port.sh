#!/bin/bash

PORT=3001
echo "Killing process on port $PORT..."

TASKKILL="/c/Windows/System32/taskkill.exe"

if [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
    # <PERSON><PERSON><PERSON> danh sách PID duy nhất ở cột cuối cùng
    PIDS=$(netstat -ano | grep ":$PORT" | grep LISTENING | awk '{print $NF}' | tr -d '\r' | sort -u)

    if [ -n "$PIDS" ]; then
        echo "Found process PID(s): $PIDS"
        for PID in $PIDS; do
            "$TASKKILL" //PID "$PID" //F
            if [ $? -eq 0 ]; then
                echo "Killed PID $PID successfully!"
            else
                echo "Failed to kill PID $PID."
            fi
        done
    else
        echo "No process found on port $PORT"
    fi
else
    PIDS=$(lsof -ti:$PORT)
    if [ -n "$PIDS" ]; then
        echo "Found process PID(s): $PIDS"
        for PID in $PIDS; do
            kill -9 "$PID"
            if [ $? -eq 0 ]; then
                echo "Killed PID $PID successfully!"
            else
                echo "Failed to kill PID $PID."
            fi
        done
    else
        echo "No process found on port $PORT"
    fi
fi
