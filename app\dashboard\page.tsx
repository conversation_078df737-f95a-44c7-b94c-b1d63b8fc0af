'use client'

import React from 'react'
import { 
  G<PERSON>l, 
  FolderOpen, 
  Building2, 
  TrendingUp,
  Calendar,
  AlertCircle,
  ArrowUp,
  ArrowDown
} from 'lucide-react'
import { MainLayout } from '@/components/layout/MainLayout'
import { AuthGuard } from '@/components/AuthGuard'

export default function DashboardPage() {
  // Mock data - trong thực tế sẽ lấy từ API
  const stats = [
    {
      title: 'Tổng số gói thầu',
      value: '156',
      change: '+12%',
      trend: 'up',
      icon: Gavel,
      color: 'blue'
    },
    {
      title: 'Đang diễn ra',
      value: '24',
      change: '+5%',
      trend: 'up',
      icon: AlertCircle,
      color: 'yellow'
    },
    {
      title: 'Dự án',
      value: '89',
      change: '+8%',
      trend: 'up',
      icon: FolderOpen,
      color: 'green'
    },
    {
      title: 'Nhà thầu',
      value: '342',
      change: '-2%',
      trend: 'down',
      icon: Building2,
      color: 'purple'
    }
  ]

  const recentBiddings = [
    {
      id: 1,
      code: 'TB2024-001',
      title: '<PERSON><PERSON><PERSON> dựng cầu vượt <PERSON>',
      status: 'OPEN',
      value: '15.000.000.000',
      endDate: '2024-12-25'
    },
    {
      id: 2,
      code: 'TB2024-002',
      title: 'Mua sắm thiết bị y tế cho bệnh viện',
      status: 'EVALUATING',
      value: '8.500.000.000',
      endDate: '2024-12-20'
    },
    {
      id: 3,
      code: 'TB2024-003',
      title: 'Dịch vụ bảo trì hệ thống CNTT',
      status: 'PUBLISHED',
      value: '2.300.000.000',
      endDate: '2024-12-30'
    },
    {
      id: 4,
      code: 'TB2024-004',
      title: 'Tư vấn thiết kế quy hoạch đô thị',
      status: 'COMPLETED',
      value: '4.750.000.000',
      endDate: '2024-12-15'
    }
  ]

  const statusColors = {
    DRAFT: 'bg-gray-100 text-gray-700',
    PUBLISHED: 'bg-blue-100 text-blue-700',
    OPEN: 'bg-green-100 text-green-700',
    EVALUATING: 'bg-yellow-100 text-yellow-700',
    COMPLETED: 'bg-purple-100 text-purple-700',
    CANCELLED: 'bg-red-100 text-red-700'
  }

  const statusLabels = {
    DRAFT: 'Nháp',
    PUBLISHED: 'Đã công bố',
    OPEN: 'Đang mở',
    EVALUATING: 'Đang đánh giá',
    COMPLETED: 'Hoàn thành',
    CANCELLED: 'Đã hủy'
  }

  return (
    <AuthGuard>
      <MainLayout>
        <div className="p-6">
        {/* Page Header */}
        <div className="mb-6">
          <h1 className="text-3xl font-semibold text-gray-900 dark:text-gray-100">
            Tổng quan hệ thống
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Xin chào! Đây là tổng quan về hoạt động đấu thầu của bạn
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon
            const bgColor = {
              blue: 'bg-blue-500',
              yellow: 'bg-yellow-500',
              green: 'bg-green-500',
              purple: 'bg-purple-500'
            }[stat.color]

            return (
              <div
                key={index}
                className="bg-white dark:bg-gray-800 rounded-lg shadow p-6"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className={`${bgColor} p-3 rounded-lg text-white`}>
                    <Icon className="w-6 h-6" />
                  </div>
                  <div className={`flex items-center gap-1 text-sm ${
                    stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stat.trend === 'up' ? (
                      <ArrowUp className="w-4 h-4" />
                    ) : (
                      <ArrowDown className="w-4 h-4" />
                    )}
                    {stat.change}
                  </div>
                </div>
                <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {stat.value}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {stat.title}
                </div>
              </div>
            )
          })}
        </div>

        {/* Recent Biddings */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Gói thầu gần đây
              </h2>
              <a
                href="/biddings"
                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                Xem tất cả →
              </a>
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 dark:bg-gray-900">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Mã gói thầu
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Tên gói thầu
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Giá trị
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Trạng thái
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Ngày kết thúc
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {recentBiddings.map((bidding) => (
                  <tr
                    key={bidding.id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                      {bidding.code}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                      {bidding.title}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      {bidding.value} đ
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        statusColors[bidding.status as keyof typeof statusColors]
                      }`}>
                        {statusLabels[bidding.status as keyof typeof statusLabels]}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                      {new Date(bidding.endDate).toLocaleDateString('vi-VN')}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <a
            href="/biddings/new"
            className="block p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
          >
            <div className="flex items-center gap-4">
              <Gavel className="w-8 h-8 text-blue-600" />
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                  Tạo gói thầu mới
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Khởi tạo quy trình đấu thầu
                </p>
              </div>
            </div>
          </a>

          <a
            href="/projects/new"
            className="block p-6 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
          >
            <div className="flex items-center gap-4">
              <FolderOpen className="w-8 h-8 text-green-600" />
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                  Tạo dự án mới
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Thêm dự án cần đấu thầu
                </p>
              </div>
            </div>
          </a>

          <a
            href="/reports"
            className="block p-6 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors"
          >
            <div className="flex items-center gap-4">
              <TrendingUp className="w-8 h-8 text-purple-600" />
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                  Xem báo cáo
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Thống kê và phân tích
                </p>
              </div>
            </div>
          </a>
        </div>
      </div>
    </MainLayout>
    </AuthGuard>
  )
}