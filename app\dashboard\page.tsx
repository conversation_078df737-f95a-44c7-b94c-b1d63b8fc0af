'use client'

import React from 'react'
import { 
  Gavel, 
  Folder<PERSON>pen, 
  Building2, 
  AlertCircle,
  Loader2
} from 'lucide-react'
import { MainLayout } from '@/components/layout/MainLayout'
import { AuthGuard } from '@/components/AuthGuard'
import { useDashboardStats, useRecentBiddingDocuments, dashboardKeys } from '@/hooks/queries/useDashboard'
import { useQueryClient } from '@tanstack/react-query'

export default function DashboardPage() {
  // Fetch real data from API
  const queryClient = useQueryClient()
  const { data: dashboardStats, isLoading: isLoadingStats, error: statsError, refetch: refetchStats } = useDashboardStats()
  const { data: recentBiddings, isLoading: isLoadingBiddings, error: biddingsError, refetch: refetchBiddings } = useRecentBiddingDocuments()

  // Retry functions
  const retryStats = () => {
    queryClient.invalidateQueries({ queryKey: dashboardKeys.stats() })
    refetchStats()
  }

  const retryBiddings = () => {
    queryClient.invalidateQueries({ queryKey: dashboardKeys.recentBiddings() })
    refetchBiddings()
  }

  // Transform API data to match component structure
  const stats = dashboardStats ? [
    {
      title: 'Tổng số hồ sơ dự thầu',
      value: dashboardStats.totalBiddingDocuments.value.toString(),
      icon: Gavel,
      color: 'blue' as const
    },
    {
      title: 'Đang thực hiện',
      value: dashboardStats.activeBiddingDocuments.value.toString(),
      icon: AlertCircle,
      color: 'yellow' as const
    },
    {
      title: 'Thiết bị',
      value: dashboardStats.totalEquipment.value.toString(),
      icon: FolderOpen,
      color: 'green' as const
    },
    {
      title: 'Người dùng',
      value: dashboardStats.totalUsers.value.toString(),
      icon: Building2,
      color: 'purple' as const
    }
  ] : []

  // Use real bidding documents data
  const recentBiddingsList = recentBiddings || []

  const statusColors = {
    PENDING: 'bg-yellow-100 text-yellow-700',
    IN_PROGRESS: 'bg-blue-100 text-blue-700',
    COMPLETED: 'bg-green-100 text-green-700'
  }

  const statusLabels = {
    PENDING: 'Chờ xử lý',
    IN_PROGRESS: 'Đang thực hiện',
    COMPLETED: 'Hoàn thành'
  }

  return (
    <AuthGuard>
      <MainLayout>
        <div className="p-6">
        {/* Page Header */}
        <div className="mb-6">
          <h1 className="text-3xl font-semibold text-gray-900 dark:text-gray-100">
            Tổng quan hệ thống
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Xin chào! Đây là tổng quan về hoạt động đấu thầu của bạn
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {isLoadingStats ? (
            // Loading skeleton
            Array.from({ length: 4 }).map((_, index) => (
              <div
                key={index}
                className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 animate-pulse"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                  <div className="w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
                <div className="w-16 h-8 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                <div className="w-24 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            ))
          ) : statsError ? (
            <div className="col-span-full bg-red-50 dark:bg-red-900/20 rounded-lg p-6 text-center">
              <AlertCircle className="w-8 h-8 text-red-600 mx-auto mb-2" />
              <h3 className="text-lg font-medium text-red-800 dark:text-red-400 mb-2">
                Không thể tải thống kê dashboard
              </h3>
              <p className="text-red-600 dark:text-red-400 text-sm mb-4">
                {(statsError as any)?.message || 'Lỗi khi tải dữ liệu từ máy chủ'}
              </p>
              <button
                onClick={retryStats}
                disabled={isLoadingStats}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoadingStats ? 'Đang thử lại...' : 'Thử lại'}
              </button>
            </div>
          ) : (
            stats.map((stat, index) => {
              const Icon = stat.icon
              const bgColor = {
                blue: 'bg-blue-500',
                yellow: 'bg-yellow-500',
                green: 'bg-green-500',
                purple: 'bg-purple-500'
              }[stat.color]

              return (
                <div
                  key={index}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow p-6"
                >
                  <div className="flex items-center mb-4">
                    <div className={`${bgColor} p-3 rounded-lg text-white`}>
                      <Icon className="w-6 h-6" />
                    </div>
                  </div>
                  <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {stat.value}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {stat.title}
                  </div>
                </div>
              )
            })
          )}
        </div>

        {/* Recent Biddings */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Hồ sơ dự thầu gần đây
              </h2>
              <a
                href="/bidding-documents"
                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                Xem tất cả →
              </a>
            </div>
          </div>
          <div className="overflow-x-auto">
            {isLoadingBiddings ? (
              <div className="flex justify-center items-center p-8">
                <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
                <span className="ml-2 text-gray-600 dark:text-gray-400">Đang tải...</span>
              </div>
            ) : biddingsError ? (
              <div className="p-8 text-center">
                <AlertCircle className="w-8 h-8 text-red-600 mx-auto mb-2" />
                <h3 className="text-lg font-medium text-red-800 dark:text-red-400 mb-2">
                  Không thể tải danh sách hồ sơ dự thầu
                </h3>
                <p className="text-red-600 dark:text-red-400 text-sm mb-4">
                  {(biddingsError as any)?.message || 'Lỗi khi tải dữ liệu từ máy chủ'}
                </p>
                <button
                  onClick={retryBiddings}
                  disabled={isLoadingBiddings}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoadingBiddings ? 'Đang thử lại...' : 'Thử lại'}
                </button>
              </div>
            ) : recentBiddingsList.length === 0 ? (
              <div className="p-8 text-center">
                <FolderOpen className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-600 dark:text-gray-400">Chưa có hồ sơ dự thầu nào</p>
              </div>
            ) : (
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-gray-900">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Mã hồ sơ
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Tên hồ sơ
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Khách hàng
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Trạng thái
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Ngày tạo
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  {recentBiddingsList.map((bidding) => (
                    <tr
                      key={bidding.id}
                      className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                        {bidding.code}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                        {bidding.title}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {bidding.customerName}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          statusColors[bidding.status as keyof typeof statusColors]
                        }`}>
                          {statusLabels[bidding.status as keyof typeof statusLabels]}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {new Date(bidding.createdAt).toLocaleDateString('vi-VN')}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
    </AuthGuard>
  )
}