import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { verifyToken } from '@/lib/auth';
import * as XLSX from 'xlsx';

export async function POST(request: NextRequest) {
  try {
    const token = request.cookies.get('token')?.value;

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = await verifyToken(token);
    if (!payload || (payload.role !== 'ADMIN' && payload.role !== 'USER')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Read file content
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Parse Excel file
    const workbook = XLSX.read(buffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const jsonData = XLSX.utils.sheet_to_json(worksheet);

    let imported = 0;
    let failed = 0;
    const errors: string[] = [];

    for (const row of jsonData as any[]) {
      try {
        // Validate required fields
        if (!row['Mã thiết bị'] || !row['Tên thiết bị'] || !row['Mã danh mục']) {
          failed++;
          errors.push(`Dòng ${imported + failed}: Thiếu thông tin bắt buộc`);
          continue;
        }

        // Find catalog by code
        const catalog = await prisma.catalog.findFirst({
          where: { catalogCode: row['Mã danh mục'] }
        });

        if (!catalog) {
          failed++;
          errors.push(`Dòng ${imported + failed}: Không tìm thấy danh mục với mã ${row['Mã danh mục']}`);
          continue;
        }

        // Parse specifications
        let specifications: Record<string, any> = {};
        if (row['Thông số kỹ thuật']) {
          try {
            // Parse format: "key1:value1;key2:value2"
            const specString = row['Thông số kỹ thuật'];
            const specPairs = specString.split(';');
            for (const pair of specPairs) {
              const [key, value] = pair.split(':').map((s: string) => s.trim());
              if (key && value) {
                specifications[key] = value;
              }
            }
          } catch (e) {
            // If parsing fails, store as single string
            specifications = { 'Thông số': row['Thông số kỹ thuật'] };
          }
        }

        // Create equipment
        await prisma.equipment.upsert({
          where: { equipmentCode: row['Mã thiết bị'] },
          update: {
            name: row['Tên thiết bị'],
            description: row['Mô tả'] || null,
            model: row['Model'] || null,
            manufacturer: row['Nhà sản xuất'] || null,
            unit: row['Đơn vị'] || null,
            price: row['Giá'] ? parseFloat(row['Giá']) : null,
            specifications: specifications,
            catalogId: catalog.id,
            status: row['Trạng thái'] === 'Không hoạt động' ? 'INACTIVE' : 'ACTIVE',
            updatedAt: new Date()
          },
          create: {
            equipmentCode: row['Mã thiết bị'],
            name: row['Tên thiết bị'],
            description: row['Mô tả'] || null,
            model: row['Model'] || null,
            manufacturer: row['Nhà sản xuất'] || null,
            unit: row['Đơn vị'] || null,
            price: row['Giá'] ? parseFloat(row['Giá']) : null,
            specifications: specifications,
            catalogId: catalog.id,
            status: row['Trạng thái'] === 'Không hoạt động' ? 'INACTIVE' : 'ACTIVE',
            createdBy: payload.userId
          }
        });

        imported++;
      } catch (error) {
        failed++;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        errors.push(`Dòng ${imported + failed}: ${errorMessage}`);
      }
    }

    return NextResponse.json({
      imported,
      failed,
      errors: errors.length > 0 ? errors : undefined,
      message: `Đã import thành công ${imported} thiết bị${failed > 0 ? `, thất bại ${failed} thiết bị` : ''}`
    });

  } catch (error) {
    console.error('Import equipment error:', error);
    return NextResponse.json(
      { error: 'Failed to import equipment' },
      { status: 500 }
    );
  }
}

// Download template Excel
export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('token')?.value;

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Create template data
    const templateData = [
      {
        'Mã thiết bị': 'TB001',
        'Tên thiết bị': 'Máy X-quang kỹ thuật số',
        'Mô tả': 'Máy X-quang kỹ thuật số cao cấp',
        'Model': 'XR-2000',
        'Nhà sản xuất': 'Siemens',
        'Đơn vị': 'Máy',
        'Giá': '500000000',
        'Mã danh mục': 'CAT001',
        'Thông số kỹ thuật': 'Công suất:50kW;Điện áp:220V;Kích thước:2x1.5x2m',
        'Trạng thái': 'Hoạt động'
      },
      {
        'Mã thiết bị': 'TB002',
        'Tên thiết bị': 'Máy siêu âm',
        'Mô tả': 'Máy siêu âm 4D',
        'Model': 'US-4D',
        'Nhà sản xuất': 'GE Healthcare',
        'Đơn vị': 'Máy',
        'Giá': '300000000',
        'Mã danh mục': 'CAT002',
        'Thông số kỹ thuật': 'Tần số:2-15MHz;Độ sâu:30cm;Màn hình:21inch',
        'Trạng thái': 'Hoạt động'
      }
    ];

    // Create workbook
    const worksheet = XLSX.utils.json_to_sheet(templateData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Template');

    // Set column widths
    worksheet['!cols'] = [
      { wch: 15 }, // Mã thiết bị
      { wch: 30 }, // Tên thiết bị
      { wch: 40 }, // Mô tả
      { wch: 15 }, // Model
      { wch: 20 }, // Nhà sản xuất
      { wch: 10 }, // Đơn vị
      { wch: 15 }, // Giá
      { wch: 15 }, // Mã danh mục
      { wch: 50 }, // Thông số kỹ thuật
      { wch: 15 }  // Trạng thái
    ];

    // Convert to buffer
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    // Return file
    return new NextResponse(buffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="Template_Import_ThietBi.xlsx"'
      }
    });

  } catch (error) {
    console.error('Download template error:', error);
    return NextResponse.json(
      { error: 'Failed to download template' },
      { status: 500 }
    );
  }
}