import React from 'react'
import { render, screen, fireEvent, within } from '@testing-library/react'
import { CatalogTable } from '@/components/catalogs/CatalogTable'
import { createMockCatalog } from '../../../utils/catalog-test-utils'
import type { Catalog } from '@/types/catalog'

describe('CatalogTable Component', () => {
  const mockOnEdit = jest.fn()
  const mockOnDelete = jest.fn()
  const mockOnView = jest.fn()

  const defaultProps = {
    catalogs: [],
    onEdit: mockOnEdit,
    onDelete: mockOnDelete,
    onView: mockOnView,
    isLoading: false
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Rendering States', () => {
    it('should render loading state', () => {
      const { container } = render(<CatalogTable {...defaultProps} isLoading={true} />)
      
      const loadingElement = container.querySelector('.animate-pulse')
      expect(loadingElement).toBeInTheDocument()
    })

    it('should render empty state when no catalogs', () => {
      render(<CatalogTable {...defaultProps} catalogs={[]} />)
      
      expect(screen.getByText('Không có danh mục nào')).toBeInTheDocument()
      expect(screen.getByText('Bắt đầu bằng cách tạo danh mục mới')).toBeInTheDocument()
    })

    it('should render table with catalogs', () => {
      const catalogs = [
        createMockCatalog({ id: '1', catalogCode: 'CAT001', catalogName: 'Thiết bị y tế' }),
        createMockCatalog({ id: '2', catalogCode: 'CAT002', catalogName: 'Dụng cụ phẫu thuật' })
      ]
      
      render(<CatalogTable {...defaultProps} catalogs={catalogs} />)
      
      // Check table headers
      expect(screen.getByText('Mã danh mục')).toBeInTheDocument()
      expect(screen.getByText('Tên danh mục')).toBeInTheDocument()
      expect(screen.getByText('Mô tả')).toBeInTheDocument()
      expect(screen.getByText('Số thiết bị')).toBeInTheDocument()
      expect(screen.getByText('Trạng thái')).toBeInTheDocument()
      expect(screen.getByText('Ngày tạo')).toBeInTheDocument()
      expect(screen.getByText('Thao tác')).toBeInTheDocument()
      
      // Check catalog data
      expect(screen.getByText('CAT001')).toBeInTheDocument()
      expect(screen.getByText('CAT002')).toBeInTheDocument()
      expect(screen.getByText('Thiết bị y tế')).toBeInTheDocument()
      expect(screen.getByText('Dụng cụ phẫu thuật')).toBeInTheDocument()
    })
  })

  describe('Catalog Display', () => {
    it('should display catalog information correctly', () => {
      const catalog = createMockCatalog({
        id: '1',
        catalogCode: 'CAT001',
        catalogName: 'Thiết bị y tế',
        description: 'Mô tả danh mục',
        equipmentCount: 10,
        status: 'ACTIVE'
      })
      
      render(<CatalogTable {...defaultProps} catalogs={[catalog]} />)
      
      const row = screen.getByRole('row', { name: /CAT001/i })
      
      expect(within(row).getByText('CAT001')).toBeInTheDocument()
      expect(within(row).getByText('Thiết bị y tế')).toBeInTheDocument()
      expect(within(row).getByText('Mô tả danh mục')).toBeInTheDocument()
      expect(within(row).getByText('10')).toBeInTheDocument()
      expect(within(row).getByText('Hoạt động')).toBeInTheDocument()
    })

    it('should display inactive status correctly', () => {
      const catalog = createMockCatalog({
        id: '1',
        status: 'INACTIVE'
      })
      
      render(<CatalogTable {...defaultProps} catalogs={[catalog]} />)
      
      const statusBadge = screen.getByText('Không hoạt động')
      expect(statusBadge).toBeInTheDocument()
    })

    it('should display active status correctly', () => {
      const catalog = createMockCatalog({
        id: '1',
        status: 'ACTIVE'
      })
      
      render(<CatalogTable {...defaultProps} catalogs={[catalog]} />)
      
      const statusBadge = screen.getByText('Hoạt động')
      expect(statusBadge).toBeInTheDocument()
    })

    it('should handle missing description gracefully', () => {
      const catalog = createMockCatalog({
        id: '1',
        description: undefined
      })
      
      render(<CatalogTable {...defaultProps} catalogs={[catalog]} />)
      
      // Description should show '-' when undefined
      expect(screen.getByText('-')).toBeInTheDocument()
    })

    it('should display zero equipment count', () => {
      const catalog = createMockCatalog({
        id: '1',
        equipmentCount: 0
      })
      
      render(<CatalogTable {...defaultProps} catalogs={[catalog]} />)
      
      expect(screen.getByText('0')).toBeInTheDocument()
    })
  })

  describe('Actions', () => {
    const catalog = createMockCatalog({ id: '1', catalogCode: 'CAT001' })

    it('should call onView when view button is clicked', () => {
      render(<CatalogTable {...defaultProps} catalogs={[catalog]} />)
      
      const viewButton = screen.getByTitle('Xem chi tiết')
      fireEvent.click(viewButton)
      
      expect(mockOnView).toHaveBeenCalledWith(catalog)
      expect(mockOnView).toHaveBeenCalledTimes(1)
    })

    it('should call onEdit when edit button is clicked', () => {
      render(<CatalogTable {...defaultProps} catalogs={[catalog]} />)
      
      const editButton = screen.getByTitle('Chỉnh sửa')
      fireEvent.click(editButton)
      
      expect(mockOnEdit).toHaveBeenCalledWith(catalog)
      expect(mockOnEdit).toHaveBeenCalledTimes(1)
    })

    it('should call onDelete when delete button is clicked', () => {
      render(<CatalogTable {...defaultProps} catalogs={[catalog]} />)
      
      const deleteButton = screen.getByTitle('Xóa')
      fireEvent.click(deleteButton)
      
      expect(mockOnDelete).toHaveBeenCalledWith(catalog)
      expect(mockOnDelete).toHaveBeenCalledTimes(1)
    })

    it('should not render view button when onView is not provided', () => {
      const { onView, ...propsWithoutView } = defaultProps
      render(<CatalogTable {...propsWithoutView} catalogs={[catalog]} />)
      
      expect(screen.queryByTitle('Xem chi tiết')).not.toBeInTheDocument()
    })

  })

  describe('Multiple Catalogs', () => {
    it('should render multiple catalogs correctly', () => {
      const catalogs = [
        createMockCatalog({ id: '1', catalogCode: 'CAT001', catalogName: 'Danh mục 1' }),
        createMockCatalog({ id: '2', catalogCode: 'CAT002', catalogName: 'Danh mục 2' }),
        createMockCatalog({ id: '3', catalogCode: 'CAT003', catalogName: 'Danh mục 3' })
      ]
      
      render(<CatalogTable {...defaultProps} catalogs={catalogs} />)
      
      expect(screen.getByText('CAT001')).toBeInTheDocument()
      expect(screen.getByText('CAT002')).toBeInTheDocument()
      expect(screen.getByText('CAT003')).toBeInTheDocument()
      
      const rows = screen.getAllByRole('row')
      // Header row + 3 data rows
      expect(rows).toHaveLength(4)
    })

    it('should handle mixed status catalogs', () => {
      const catalogs = [
        createMockCatalog({ id: '1', status: 'ACTIVE' }),
        createMockCatalog({ id: '2', status: 'INACTIVE' }),
        createMockCatalog({ id: '3', status: 'ACTIVE' })
      ]
      
      render(<CatalogTable {...defaultProps} catalogs={catalogs} />)
      
      const activeStatuses = screen.getAllByText('Hoạt động')
      const inactiveStatuses = screen.getAllByText('Không hoạt động')
      
      expect(activeStatuses).toHaveLength(2)
      expect(inactiveStatuses).toHaveLength(1)
    })
  })

  describe('Responsive Design', () => {
    it('should have responsive table classes', () => {
      const catalog = createMockCatalog({ id: '1' })
      const { container } = render(<CatalogTable {...defaultProps} catalogs={[catalog]} />)
      
      const table = container.querySelector('table')
      expect(table).toHaveClass('min-w-full')
    })

    it('should have scrollable container', () => {
      const catalog = createMockCatalog({ id: '1' })
      const { container } = render(<CatalogTable {...defaultProps} catalogs={[catalog]} />)
      
      const scrollContainer = container.querySelector('.overflow-x-auto')
      expect(scrollContainer).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('should have title attributes on action buttons', () => {
      const catalog = createMockCatalog({ id: '1' })
      render(<CatalogTable {...defaultProps} catalogs={[catalog]} />)
      
      expect(screen.getByTitle('Xem chi tiết')).toBeInTheDocument()
      expect(screen.getByTitle('Chỉnh sửa')).toBeInTheDocument()
      expect(screen.getByTitle('Xóa')).toBeInTheDocument()
    })
  })

  describe('Edge Cases', () => {
    it('should handle catalog with very long name', () => {
      const longName = 'A'.repeat(100)
      const catalog = createMockCatalog({
        id: '1',
        catalogName: longName
      })
      
      render(<CatalogTable {...defaultProps} catalogs={[catalog]} />)
      
      expect(screen.getByText(longName)).toBeInTheDocument()
    })

    it('should handle catalog with special characters', () => {
      const catalog = createMockCatalog({
        id: '1',
        catalogCode: 'CAT<>001',
        catalogName: 'Danh mục & thiết bị'
      })
      
      render(<CatalogTable {...defaultProps} catalogs={[catalog]} />)
      
      expect(screen.getByText('CAT<>001')).toBeInTheDocument()
      expect(screen.getByText('Danh mục & thiết bị')).toBeInTheDocument()
    })

    it('should handle very large equipment count', () => {
      const catalog = createMockCatalog({
        id: '1',
        equipmentCount: 999999
      })
      
      render(<CatalogTable {...defaultProps} catalogs={[catalog]} />)
      
      expect(screen.getByText('999999')).toBeInTheDocument()
    })
  })
})