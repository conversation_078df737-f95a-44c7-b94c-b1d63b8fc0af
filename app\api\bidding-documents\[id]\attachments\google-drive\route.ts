import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from '@/lib/auth-server'
import { prisma } from '@/lib/db'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()
    const { fileId, fileName, mimeType, fileUrl } = body

    if (!fileId || !fileName || !mimeType || !fileUrl) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ]
    
    if (!allowedTypes.includes(mimeType)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only PDF and Word files are allowed.' },
        { status: 400 }
      )
    }

    // Create database record for Google Drive file
    const attachment = await prisma.biddingDocumentAttachment.create({
      data: {
        biddingDocumentId: id,
        fileName,
        fileUrl: '', // We'll use googleDriveUrl instead
        fileSize: 0, // Google Drive doesn't provide size easily
        mimeType,
        source: 'google_drive',
        googleDriveId: fileId,
        googleDriveUrl: fileUrl,
        uploadedBy: session.user?.id || 'system'
      }
    })

    return NextResponse.json(attachment)
  } catch (error) {
    console.error('Error linking Google Drive file:', error)
    return NextResponse.json(
      { error: 'Failed to link Google Drive file' },
      { status: 500 }
    )
  }
}