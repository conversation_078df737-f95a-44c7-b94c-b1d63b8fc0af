import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/hooks/useToast'
import { biddingDocumentService } from '@/services/biddingDocumentService'
import type {
  BiddingDocument,
  CreateBiddingDocumentRequest,
  UpdateBiddingDocumentRequest,
  BiddingDocumentFilter,
  GenerateTechnicalResponseRequest,
  UpdateEvaluationRequest,
  BiddingDocumentAttachment
} from '@/types/biddingDocument'

// Query keys factory
export const biddingDocumentKeys = {
  all: ['biddingDocuments'] as const,
  lists: () => [...biddingDocumentKeys.all, 'list'] as const,
  list: (filters?: BiddingDocumentFilter) => [...biddingDocumentKeys.lists(), filters] as const,
  details: () => [...biddingDocumentKeys.all, 'detail'] as const,
  detail: (id: string) => [...biddingDocumentKeys.details(), id] as const,
}

// Get list of bidding documents
export function useBiddingDocuments(filters?: BiddingDocumentFilter) {
  return useQuery({
    queryKey: biddingDocumentKeys.list(filters),
    queryFn: ({ signal }) => biddingDocumentService.getBiddingDocuments(filters, signal),
    staleTime: 0, // Always fetch fresh data
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  })
}

// Get single bidding document
export function useBiddingDocument(id: string, enabled = true) {
  return useQuery({
    queryKey: biddingDocumentKeys.detail(id),
    queryFn: ({ signal }) => biddingDocumentService.getBiddingDocument(id, signal),
    enabled: enabled && !!id,
    staleTime: 5 * 1000, // Consider data stale after 5 seconds
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  })
}

// Create bidding document
export function useCreateBiddingDocument() {
  const queryClient = useQueryClient()
  const toast = useToast()

  return useMutation({
    mutationFn: (data: CreateBiddingDocumentRequest) => 
      biddingDocumentService.createBiddingDocument(data),
    onMutate: async (newDocument) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: biddingDocumentKeys.lists() })
      
      // Snapshot the previous value
      const previousDocuments = queryClient.getQueryData(biddingDocumentKeys.lists())
      
      // Return a context object with the snapshotted value
      return { previousDocuments }
    },
    onSuccess: (createdDocument) => {
      // Invalidate and refetch all lists to ensure fresh data
      queryClient.invalidateQueries({ queryKey: biddingDocumentKeys.lists() })
      
      // Also set the specific document in cache
      queryClient.setQueryData(
        biddingDocumentKeys.detail(createdDocument.id),
        createdDocument
      )
    },
    onError: (error: Error, newDocument, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousDocuments) {
        queryClient.setQueryData(biddingDocumentKeys.lists(), context.previousDocuments)
      }
      toast.error(error.message || 'Không thể tạo hồ sơ dự thầu')
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: biddingDocumentKeys.lists() })
    },
  })
}

// Update bidding document
export function useUpdateBiddingDocument() {
  const queryClient = useQueryClient()
  const toast = useToast()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateBiddingDocumentRequest }) =>
      biddingDocumentService.updateBiddingDocument(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: biddingDocumentKeys.detail(variables.id) })
      queryClient.invalidateQueries({ queryKey: biddingDocumentKeys.lists() })
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Không thể cập nhật hồ sơ dự thầu')
    },
  })
}

// Delete bidding document
export function useDeleteBiddingDocument() {
  const queryClient = useQueryClient()
  const toast = useToast()

  return useMutation({
    mutationFn: (id: string) => biddingDocumentService.deleteBiddingDocument(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: biddingDocumentKeys.lists() })
      toast.success('Xóa hồ sơ dự thầu thành công')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Không thể xóa hồ sơ dự thầu')
    },
  })
}

// Upload tender document
export function useUploadTenderDocument() {
  const queryClient = useQueryClient()
  const toast = useToast()

  return useMutation({
    mutationFn: (data: {
      biddingDocumentId: string
      equipmentItemId: string
      file: File
      pageRange?: { from: number; to: number }
    }) => biddingDocumentService.uploadTenderDocument(data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: biddingDocumentKeys.detail(variables.biddingDocumentId) 
      })
      toast.success('Tải lên tài liệu thành công')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Không thể tải lên tài liệu')
    },
  })
}

// Generate technical response
export function useGenerateTechnicalResponse() {
  const queryClient = useQueryClient()
  const toast = useToast()

  return useMutation({
    mutationFn: (data: GenerateTechnicalResponseRequest) =>
      biddingDocumentService.generateTechnicalResponse(data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: biddingDocumentKeys.detail(variables.biddingDocumentId) 
      })
      toast.success('Tạo tài liệu đáp ứng kỹ thuật thành công')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Không thể tạo tài liệu đáp ứng kỹ thuật')
    },
  })
}

// Update technical response data
export function useUpdateTechnicalResponseData() {
  const queryClient = useQueryClient()
  const toast = useToast()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Record<string, any> }) =>
      biddingDocumentService.updateTechnicalResponseData(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: biddingDocumentKeys.detail(variables.id) })
      toast.success('Cập nhật dữ liệu kỹ thuật thành công')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Không thể cập nhật dữ liệu kỹ thuật')
    },
  })
}

// Export technical response
export function useExportTechnicalResponse() {
  const toast = useToast()

  return useMutation({
    mutationFn: ({ id, format }: { id: string; format: 'WORD' | 'PDF' | 'EXCEL' }) =>
      biddingDocumentService.exportTechnicalResponse(id, format),
    onSuccess: (blob, variables) => {
      // Create download link
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `technical-response.${variables.format.toLowerCase()}`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      
      toast.success('Xuất tài liệu thành công')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Không thể xuất tài liệu')
    },
  })
}

// Update evaluation
export function useUpdateEvaluation() {
  const queryClient = useQueryClient()
  const toast = useToast()

  return useMutation({
    mutationFn: (data: UpdateEvaluationRequest) =>
      biddingDocumentService.updateEvaluation(data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: biddingDocumentKeys.detail(variables.biddingDocumentId) 
      })
      toast.success('Cập nhật đánh giá thành công')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Không thể cập nhật đánh giá')
    },
  })
}

// Add equipment item
export function useAddEquipmentItem() {
  const queryClient = useQueryClient()
  const toast = useToast()

  return useMutation({
    mutationFn: ({ 
      biddingDocumentId, 
      equipmentId, 
      pageRange 
    }: { 
      biddingDocumentId: string
      equipmentId: string
      pageRange?: { from: number; to: number }
    }) => biddingDocumentService.addEquipmentItem(biddingDocumentId, equipmentId, pageRange),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: biddingDocumentKeys.detail(variables.biddingDocumentId) 
      })
      toast.success('Thêm thiết bị thành công')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Không thể thêm thiết bị')
    },
  })
}

// Remove equipment item
export function useRemoveEquipmentItem() {
  const queryClient = useQueryClient()
  const toast = useToast()

  return useMutation({
    mutationFn: ({ 
      biddingDocumentId, 
      equipmentItemId 
    }: { 
      biddingDocumentId: string
      equipmentItemId: string
    }) => biddingDocumentService.removeEquipmentItem(biddingDocumentId, equipmentItemId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: biddingDocumentKeys.detail(variables.biddingDocumentId) 
      })
      toast.success('Xóa thiết bị thành công')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Không thể xóa thiết bị')
    },
  })
}

// Upload attachment
export function useUploadAttachment() {
  const queryClient = useQueryClient()
  const toast = useToast()

  return useMutation({
    mutationFn: ({ 
      biddingDocumentId, 
      file 
    }: { 
      biddingDocumentId: string
      file: File
    }) => biddingDocumentService.uploadAttachment(biddingDocumentId, file),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: biddingDocumentKeys.detail(variables.biddingDocumentId) 
      })
      toast.success('Tải lên tài liệu thành công')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Không thể tải lên tài liệu')
    },
  })
}

// Save attachments batch
export function useSaveAttachments() {
  const queryClient = useQueryClient()
  const toast = useToast()

  return useMutation({
    mutationFn: ({ 
      biddingDocumentId, 
      attachments 
    }: { 
      biddingDocumentId: string
      attachments: BiddingDocumentAttachment[]
    }) => biddingDocumentService.saveAttachments(biddingDocumentId, attachments),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: biddingDocumentKeys.detail(variables.biddingDocumentId) 
      })
      toast.success('Lưu tài liệu thành công')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Không thể lưu tài liệu')
    },
  })
}

// Delete attachment
export function useDeleteAttachment() {
  const queryClient = useQueryClient()
  const toast = useToast()

  return useMutation({
    mutationFn: ({ 
      biddingDocumentId, 
      attachmentId 
    }: { 
      biddingDocumentId: string
      attachmentId: string
    }) => biddingDocumentService.deleteAttachment(biddingDocumentId, attachmentId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: biddingDocumentKeys.detail(variables.biddingDocumentId) 
      })
      toast.success('Xóa tài liệu thành công')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Không thể xóa tài liệu')
    },
  })
}

// Delete tender document
export function useDeleteTenderDocument() {
  const queryClient = useQueryClient()
  const toast = useToast()

  return useMutation({
    mutationFn: ({ 
      biddingDocumentId, 
      documentId 
    }: { 
      biddingDocumentId: string
      documentId: string
    }) => biddingDocumentService.deleteTenderDocument(biddingDocumentId, documentId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: biddingDocumentKeys.detail(variables.biddingDocumentId) 
      })
      toast.success('Xóa tài liệu thầu thành công')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Không thể xóa tài liệu thầu')
    },
  })
}

// Check code exists
export function useCheckCodeExists() {
  return useMutation({
    mutationFn: (code: string) => biddingDocumentService.checkCodeExists(code),
  })
}