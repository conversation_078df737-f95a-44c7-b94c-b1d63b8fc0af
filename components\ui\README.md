# UI Components

Th<PERSON> mục này chứa các UI components có thể tái sử dụng trong toàn bộ ứng dụng.

## Pagination Component

Component pagination có thể tái sử dụng với đầy đủ tính năng theo design.

### Props

```typescript
interface PaginationProps {
  currentPage: number;           // Trang hiện tại
  totalPages: number;           // Tổng số trang
  totalRecords: number;         // Tổng số bản ghi
  pageSize: number;             // Số bản ghi mỗi trang
  pageSizeOptions?: number[];   // Các tùy chọn số bản ghi mỗi trang (mặc định: [10, 20, 50, 100])
  onPageChange: (page: number) => void;           // Callback khi chuyển trang
  onPageSizeChange: (pageSize: number) => void;   // Callback khi thay đổi số bản ghi mỗi trang
  className?: string;           // CSS class tùy chỉnh
}
```

### Cách sử dụng

```tsx
import Pagination from '@/components/ui/Pagination';

const MyComponent = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const totalRecords = 915;
  const totalPages = Math.ceil(totalRecords / pageSize);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Gọi API để lấy dữ liệu cho trang mới
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset về trang đầu
    // Gọi API để lấy dữ liệu với page size mới
  };

  return (
    <div>
      {/* Nội dung bảng hoặc danh sách */}
      
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalRecords={totalRecords}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        className="mt-4"
      />
    </div>
  );
};
```

### Tính năng

- Hiển thị tổng số bản ghi
- Dropdown để chọn số bản ghi mỗi trang
- Điều hướng trang với nút Previous/Next
- Hiển thị số trang với ellipsis (...) khi có nhiều trang
- Responsive design
- Hỗ trợ keyboard navigation
- Tự động tính toán số trang dựa trên tổng số bản ghi và page size

### Styling

Component sử dụng Tailwind CSS và có thể tùy chỉnh thông qua prop `className`. Các class mặc định:

- Container: `flex items-center justify-between`
- Page size dropdown: `border border-gray-300 rounded-md`
- Page buttons: `rounded-full hover:bg-gray-100`
- Active page: `bg-gray-200 text-gray-700`
- Disabled buttons: `opacity-50 cursor-not-allowed`

## Tích hợp với các trang hiện có

### Users Page
Đã được tích hợp vào `UserTable` component với đầy đủ tính năng:
- Hiển thị tổng số users
- Thay đổi page size
- Điều hướng trang
- Tích hợp với filters và sorting

### Schools Page
Đã được tích hợp vào `SchoolTable` component với đầy đủ tính năng:
- Hiển thị tổng số schools
- Thay đổi page size
- Điều hướng trang
- Tích hợp với bulk actions và selection
- Hỗ trợ sorting và filtering

### Cách tích hợp cho trang mới

1. Import component Pagination:
```tsx
import Pagination from '@/components/ui/Pagination';
```

2. Thêm props cho table component:
```tsx
interface MyTableProps {
  // ... existing props
  totalRecords?: number;
  pageSize?: number;
  onPageSizeChange?: (pageSize: number) => void;
}
```

3. Thêm Pagination component vào cuối table:
```tsx
{totalPages > 1 && (
  <div className="px-6 py-4 border-t border-gray-200">
    <Pagination
      currentPage={currentPage}
      totalPages={totalPages}
      totalRecords={totalRecords}
      pageSize={pageSize}
      onPageChange={onPageChange}
      onPageSizeChange={onPageSizeChange || (() => {})}
    />
  </div>
)}
```

4. Cập nhật page component để truyền props:
```tsx
<MyTable
  // ... existing props
  totalRecords={total}
  pageSize={filters.limit || 10}
  onPageSizeChange={(newPageSize) => {
    setFilters(prev => ({ ...prev, limit: newPageSize, page: 1 }));
  }}
/>
``` 