export type UserRole = 'ADMIN' | 'USER'
export type UserStatus = 'ACTIVE' | 'INACTIVE'

export interface User {
  id: string
  username: string
  email: string
  name: string
  role: UserRole
  department?: string | null
  phone?: string | null
  avatar?: string | null
  status: UserStatus
  emailVerified: boolean
  twoFactorEnabled: boolean
  lastLoginAt?: string | null
  createdAt: string
  updatedAt: string
}