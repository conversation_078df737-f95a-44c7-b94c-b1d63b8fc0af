import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { googleDriveServiceNoLogin } from '@/services/googleDriveServiceNoLogin';

export async function GET() {
  try {
    const cookieStore = await cookies();
    
    // Check for OAuth tokens in cookies
    const accessToken = cookieStore.get('google_access_token')?.value;
    const userEmail = cookieStore.get('google_user_email')?.value;
    
    if (accessToken) {
      // User is authenticated via OAuth
      return NextResponse.json({
        authenticated: true,
        needsAuth: false,
        mode: 'OAuth',
        userEmail: userEmail || undefined,
        canEdit: true // User can edit their own files
      });
    }
    
    // Check server-side refresh token authentication
    const serverAuthenticated = await googleDriveServiceNoLogin.isAuthenticated();
    
    if (serverAuthenticated) {
      // Server authenticated, but user might need to auth for editing
      return NextResponse.json({
        authenticated: true,
        needsAuth: false,
        mode: 'Auto-authenticated with refresh token',
        canEdit: false // Can only view with service account
      });
    }
    
    return NextResponse.json({
      authenticated: false,
      needsAuth: true,
      mode: 'Not configured'
    });
  } catch (error) {
    console.error('Error checking Google auth status:', error);
    return NextResponse.json({
      authenticated: false,
      needsAuth: true
    });
  }
}