'use client'

import { MainLayout } from '@/components/layout/MainLayout'
import { AIDocumentsManager } from '@/components/bidding-documents/AIDocumentsManager'
import { ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { useRouter } from 'next/navigation'

export default function AIDocumentsPage() {
  const router = useRouter()

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="py-6">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    Quản lý hồ sơ AI
                  </h1>
                  <p className="mt-1 text-sm text-gray-600">
                    Tạo và quản lý hồ sơ dự thầu được tạo bằng AI
                  </p>
                </div>
                <Button
                  variant="outline"
                  onClick={() => router.push('/bidding-documents')}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Quay lại
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <AIDocumentsManager />
        </div>
      </div>
    </MainLayout>
  )
}