'use client';

import { useParams, useRouter } from 'next/navigation';
import { useEquipment } from '@/hooks/queries/useEquipment';
import { EquipmentForm } from '@/components/equipment/EquipmentForm';
import { Button } from '@/components/ui/Button';
import { MainLayout } from '@/components/layout/MainLayout';
import { ArrowLeft, Edit3, Info, ChevronRight } from 'lucide-react';

export default function EditEquipmentPage() {
  const params = useParams();
  const router = useRouter();
  const equipmentId = params.id as string;

  const { data: equipment, isLoading, error } = useEquipment(equipmentId);

  if (isLoading) {
    return (
      <MainLayout>
        <div className="p-6">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error || !equipment) {
    return (
      <MainLayout>
        <div className="p-6">
          <div className="text-center text-red-600">
            <PERSON>hông tìm thấy thiết bị hoặc có lỗi xảy ra
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50">
        {/* Enhanced Header */}
        <div className="bg-white border-b border-gray-200 shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="py-6">
              {/* Breadcrumb */}
              <nav className="flex items-center space-x-2 text-sm mb-4">
                <button
                  onClick={() => router.push('/equipment')}
                  className="text-blue-600 hover:text-blue-700 font-medium"
                >
                  Quản lý thiết bị
                </button>
                <ChevronRight className="w-4 h-4 text-gray-400" />
                <button
                  onClick={() => router.push(`/equipment/${equipmentId}`)}
                  className="text-blue-600 hover:text-blue-700 font-medium"
                >
                  {equipment.name}
                </button>
                <ChevronRight className="w-4 h-4 text-gray-400" />
                <span className="text-gray-500">Chỉnh sửa</span>
              </nav>

              {/* Header Content */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg">
                    <Edit3 className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">Chỉnh sửa thiết bị</h1>
                    <p className="text-gray-600 mt-1">
                      Cập nhật thông tin cho: <span className="font-medium">{equipment.name}</span>
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3 mt-4 sm:mt-0">
                  <Button
                    variant="outline"
                    onClick={() => router.push('/equipment')}
                    className="flex items-center gap-2 bg-white"
                  >
                    <ArrowLeft className="w-4 h-4" />
                    Quay lại danh sách
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => router.push(`/equipment/${equipmentId}`)}
                    className="flex items-center gap-2 bg-white"
                  >
                    <Info className="w-4 h-4" />
                    Xem chi tiết
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Form Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <EquipmentForm 
            equipment={equipment}
            onSuccess={() => router.push(`/equipment/${equipmentId}`)}
          />
        </div>
      </div>
    </MainLayout>
  );
}