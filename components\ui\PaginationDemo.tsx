'use client';

import React, { useState } from 'react';
import Pagination from './Pagination';

export const PaginationDemo: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(2);
  const [pageSize, setPageSize] = useState(10);
  const totalRecords = 915;
  const totalPages = Math.ceil(totalRecords / pageSize);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    console.log('Chuyển đến trang:', page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset về trang đầu khi thay đổi page size
    console.log('Thay đổi số bản ghi mỗi trang:', newPageSize);
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow">
      <h2 className="text-lg font-semibold mb-4">Demo Pagination Component</h2>
      
      <div className="mb-4 p-4 bg-gray-50 rounded">
        <p className="text-sm text-gray-600">
          Trang hiện tại: {currentPage} / {totalPages}
        </p>
        <p className="text-sm text-gray-600">
          Số bản ghi mỗi trang: {pageSize}
        </p>
      </div>

      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalRecords={totalRecords}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        className="border-t pt-4"
      />
    </div>
  );
};

export default PaginationDemo; 