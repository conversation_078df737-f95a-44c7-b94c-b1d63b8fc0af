import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from '@/lib/auth-server'
import { prisma } from '@/lib/db'
import { writeFile, mkdir } from 'fs/promises'
import path from 'path'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    const attachments = await prisma.biddingDocumentAttachment.findMany({
      where: { biddingDocumentId: id },
      orderBy: { uploadedAt: 'desc' }
    })

    return NextResponse.json(attachments)
  } catch (error) {
    console.error('Error fetching attachments:', error)
    return NextResponse.json(
      { error: 'Failed to fetch attachments' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const startTime = Date.now()
  let errorDetails: any = {}

  try {
    console.log('[ATTACHMENT_UPLOAD] Starting file upload process', {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      workingDirectory: process.cwd(),
    })

    // Authentication check
    const session = await getServerSession(request)
    if (!session) {
      console.warn('[ATTACHMENT_UPLOAD] Authentication failed - no session')
      return NextResponse.json({ 
        error: 'Unauthorized',
        details: process.env.NODE_ENV === 'development' ? 'No valid session found' : undefined 
      }, { status: 401 })
    }

    const { id } = await params
    console.log('[ATTACHMENT_UPLOAD] Processing upload for bidding document:', id)

    // Verify bidding document exists
    let biddingDocument: any
    try {
      biddingDocument = await prisma.biddingDocument.findUnique({
        where: { id },
        select: { id: true, code: true, name: true }
      })
      
      if (!biddingDocument) {
        console.warn('[ATTACHMENT_UPLOAD] Bidding document not found:', id)
        return NextResponse.json({
          error: 'Bidding document not found',
          biddingDocumentId: id
        }, { status: 404 })
      }
    } catch (dbError: any) {
      console.error('[ATTACHMENT_UPLOAD] Database error checking bidding document:', dbError)
      errorDetails.dbCheckError = {
        operation: 'findUnique',
        biddingDocumentId: id,
        error: dbError.message,
        code: dbError.code,
      }
      throw new Error(`Database error verifying bidding document: ${dbError.message}`)
    }

    // Parse form data with error handling
    let formData: FormData
    let file: File | null = null
    
    try {
      formData = await request.formData()
      file = formData.get('file') as File
      
      console.log('[ATTACHMENT_UPLOAD] Form data parsed', {
        hasFile: !!file,
        fileName: file?.name,
        fileSize: file?.size,
        fileType: file?.type,
      })
    } catch (parseError: any) {
      console.error('[ATTACHMENT_UPLOAD] Failed to parse form data:', parseError)
      return NextResponse.json({
        error: 'Failed to parse form data',
        details: process.env.NODE_ENV === 'development' ? parseError.message : undefined
      }, { status: 400 })
    }

    if (!file) {
      console.warn('[ATTACHMENT_UPLOAD] No file provided in form data')
      return NextResponse.json({ 
        error: 'No file uploaded',
        details: 'Form data must include a file field'
      }, { status: 400 })
    }

    // File validation
    if (!file.type.includes('pdf')) {
      console.warn('[ATTACHMENT_UPLOAD] Invalid file type:', file.type)
      return NextResponse.json({
        error: 'Only PDF files are allowed for server upload. Word files should be uploaded to Google Drive.',
        fileType: file.type,
        allowedTypes: ['application/pdf']
      }, { status: 400 })
    }

    // File size validation (10MB)
    const maxSize = 10 * 1024 * 1024
    if (file.size > maxSize) {
      console.warn('[ATTACHMENT_UPLOAD] File size too large:', {
        fileSize: file.size,
        maxSize,
        fileName: file.name
      })
      return NextResponse.json({
        error: 'File size exceeds 10MB limit',
        fileSize: file.size,
        maxSize,
        fileName: file.name
      }, { status: 400 })
    }

    // Create upload directory with comprehensive error handling
    const uploadDir = path.join(process.cwd(), 'uploads', 'bidding-documents', id)
    console.log('[ATTACHMENT_UPLOAD] Creating upload directory:', uploadDir)
    
    try {
      await mkdir(uploadDir, { recursive: true })
      console.log('[ATTACHMENT_UPLOAD] Upload directory created successfully')
    } catch (dirError: any) {
      console.error('[ATTACHMENT_UPLOAD] Failed to create upload directory:', dirError)
      errorDetails.dirError = {
        operation: 'mkdir',
        path: uploadDir,
        error: dirError.message,
        code: dirError.code,
        errno: dirError.errno,
      }
      
      // Check for permission errors
      if (dirError.code === 'EACCES') {
        return NextResponse.json({
          error: 'Permission denied creating upload directory',
          details: process.env.NODE_ENV === 'development' ? `Cannot create directory: ${uploadDir}` : 'File system permission error',
          path: process.env.NODE_ENV === 'development' ? uploadDir : undefined
        }, { status: 500 })
      }
      
      if (dirError.code === 'ENOSPC') {
        return NextResponse.json({
          error: 'No space left on device',
          details: 'Server storage is full'
        }, { status: 507 })
      }
      
      throw new Error(`Failed to create upload directory: ${dirError.message}`)
    }

    // Generate unique filename
    const timestamp = Date.now()
    const sanitizedFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_')
    const fileName = `${timestamp}-${sanitizedFileName}`
    const filePath = path.join(uploadDir, fileName)
    
    console.log('[ATTACHMENT_UPLOAD] Generated file path:', {
      originalName: file.name,
      sanitizedName: sanitizedFileName,
      finalFileName: fileName,
      fullPath: filePath
    })

    // Save file with comprehensive error handling
    try {
      const buffer = Buffer.from(await file.arrayBuffer())
      await writeFile(filePath, buffer)
      console.log('[ATTACHMENT_UPLOAD] File written successfully', {
        path: filePath,
        size: buffer.length
      })
    } catch (writeError: any) {
      console.error('[ATTACHMENT_UPLOAD] Failed to write file:', writeError)
      errorDetails.writeError = {
        operation: 'writeFile',
        path: filePath,
        error: writeError.message,
        code: writeError.code,
        errno: writeError.errno,
      }
      
      // Check for specific file system errors
      if (writeError.code === 'EACCES') {
        return NextResponse.json({
          error: 'Permission denied writing file',
          details: process.env.NODE_ENV === 'development' ? `Cannot write to: ${filePath}` : 'File system permission error',
        }, { status: 500 })
      }
      
      if (writeError.code === 'ENOSPC') {
        return NextResponse.json({
          error: 'No space left on device',
          details: 'Server storage is full'
        }, { status: 507 })
      }
      
      if (writeError.code === 'EMFILE' || writeError.code === 'ENFILE') {
        return NextResponse.json({
          error: 'Too many open files',
          details: 'Server is handling too many files simultaneously'
        }, { status: 503 })
      }
      
      throw new Error(`Failed to write file: ${writeError.message}`)
    }

    // Create database record with error handling
    console.log('[ATTACHMENT_UPLOAD] Creating database record')
    let attachment: any
    try {
      attachment = await prisma.biddingDocumentAttachment.create({
        data: {
          biddingDocumentId: id,
          fileName: file.name,
          fileUrl: `/uploads/bidding-documents/${id}/${fileName}`,
          fileSize: file.size,
          mimeType: file.type,
          source: 'local',
          uploadedBy: session.user?.id || session.userId || 'system'
        }
      })
      console.log('[ATTACHMENT_UPLOAD] Database record created:', attachment.id)
    } catch (dbError: any) {
      console.error('[ATTACHMENT_UPLOAD] Database error creating attachment record:', dbError)
      errorDetails.dbCreateError = {
        operation: 'create attachment',
        error: dbError.message,
        code: dbError.code,
        biddingDocumentId: id,
      }
      
      // Clean up uploaded file if database operation fails
      try {
        const fs = await import('fs/promises')
        await fs.unlink(filePath)
        console.log('[ATTACHMENT_UPLOAD] Cleaned up uploaded file after database error')
      } catch (cleanupError) {
        console.warn('[ATTACHMENT_UPLOAD] Failed to clean up file after database error:', cleanupError)
      }
      
      throw new Error(`Database error creating attachment record: ${dbError.message}`)
    }

    const duration = Date.now() - startTime
    console.log('[ATTACHMENT_UPLOAD] Success', {
      attachmentId: attachment.id,
      fileName: attachment.fileName,
      fileSize: attachment.fileSize,
      biddingDocumentId: id,
      duration: `${duration}ms`,
    })

    return NextResponse.json(attachment, { status: 201 })
  } catch (error: any) {
    const duration = Date.now() - startTime
    
    // Comprehensive error logging
    const errorLog = {
      timestamp: new Date().toISOString(),
      error: error.message,
      stack: error.stack,
      duration: `${duration}ms`,
      environment: process.env.NODE_ENV,
      workingDirectory: process.cwd(),
      userAgent: request.headers.get('user-agent'),
      contentType: request.headers.get('content-type'),
      contentLength: request.headers.get('content-length'),
      ...errorDetails,
    }
    
    console.error('[ATTACHMENT_UPLOAD] Operation failed:', errorLog)

    // For production, don't expose sensitive error details
    const isProduction = process.env.NODE_ENV === 'production'
    return NextResponse.json({
      error: 'Failed to upload attachment',
      message: isProduction ? 'Internal server error' : error.message,
      details: isProduction ? undefined : errorDetails,
      timestamp: new Date().toISOString(),
      requestId: Math.random().toString(36).substring(7),
    }, { status: 500 })
  }
}