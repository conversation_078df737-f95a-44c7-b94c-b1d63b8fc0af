'use client';

import { useState, useEffect, useRef } from 'react';
import { ExternalLink, X, LogIn } from 'lucide-react';
import { useGoogleAuth } from '@/hooks/useGoogleAuth';

interface GoogleDriveViewerProps {
  file: {
    id: string;
    name: string;
    mimeType: string;
    embedUrl?: string;
    url: string;
  };
  onClose?: () => void;
  allowEdit?: boolean;
}

export function GoogleDriveViewer({ file, onClose, allowEdit = false }: GoogleDriveViewerProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [needsAuth, setNeedsAuth] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const loadTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const { isAuthenticated, isLoading: authLoading, signIn } = useGoogleAuth();

  useEffect(() => {
    // Reset states when file changes
    setIsLoading(true);
    setHasError(false);
    setNeedsAuth(false);
    
    // Skip authentication check - always proceed without auth
    // Set a timeout for loading
    loadTimeoutRef.current = setTimeout(() => {
      setIsLoading(false);
    }, 10000); // 10 seconds timeout

    return () => {
      if (loadTimeoutRef.current) {
        clearTimeout(loadTimeoutRef.current);
      }
    };
  }, [file.id, allowEdit, isAuthenticated, authLoading]); // Dependencies updated

  // Prevent body scroll when modal is open
  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  const openInDrive = () => {
    // Open in appropriate mode based on allowEdit
    let fullUrl = '';
    
    if (file.mimeType === 'application/vnd.google-apps.document' || 
        file.mimeType.includes('document') || 
        file.name.endsWith('.doc') || 
        file.name.endsWith('.docx')) {
      fullUrl = allowEdit 
        ? `https://docs.google.com/document/d/${file.id}/edit`
        : `https://docs.google.com/document/d/${file.id}/preview`;
    } else if (file.mimeType === 'application/vnd.google-apps.spreadsheet' || 
               file.mimeType.includes('spreadsheet') || 
               file.name.endsWith('.xls') || 
               file.name.endsWith('.xlsx')) {
      fullUrl = allowEdit 
        ? `https://docs.google.com/spreadsheets/d/${file.id}/edit`
        : `https://docs.google.com/spreadsheets/d/${file.id}/preview`;
    } else if (file.mimeType === 'application/vnd.google-apps.presentation') {
      fullUrl = allowEdit 
        ? `https://docs.google.com/presentation/d/${file.id}/edit`
        : `https://docs.google.com/presentation/d/${file.id}/preview`;
    } else {
      // For other file types, use the standard view URL
      fullUrl = `https://drive.google.com/file/d/${file.id}/view`;
    }
    
    window.open(fullUrl, '_blank');
  };

  const getEmbedUrl = () => {
    if (file.embedUrl) return file.embedUrl;
    
    
    // Generate embed URL based on mime type
    if (file.mimeType === 'application/vnd.google-apps.document' || 
        file.mimeType.includes('document') || 
        file.name.endsWith('.doc') || 
        file.name.endsWith('.docx')) {
      return allowEdit 
        ? `https://docs.google.com/document/d/${file.id}/edit?embedded=true`
        : `https://docs.google.com/document/d/${file.id}/preview`;
    } else if (file.mimeType === 'application/vnd.google-apps.spreadsheet' || 
               file.mimeType.includes('spreadsheet') || 
               file.name.endsWith('.xls') || 
               file.name.endsWith('.xlsx')) {
      return allowEdit 
        ? `https://docs.google.com/spreadsheets/d/${file.id}/edit?embedded=true`
        : `https://docs.google.com/spreadsheets/d/${file.id}/preview`;
    } else if (file.mimeType === 'application/vnd.google-apps.presentation') {
      return allowEdit 
        ? `https://docs.google.com/presentation/d/${file.id}/edit?embedded=true`
        : `https://docs.google.com/presentation/d/${file.id}/preview`;
    } else if (file.mimeType.startsWith('image/')) {
      return `https://drive.google.com/uc?id=${file.id}&export=view`;
    } else {
      return `https://drive.google.com/file/d/${file.id}/preview`;
    }
  };

  const handleIframeLoad = () => {
    if (loadTimeoutRef.current) {
      clearTimeout(loadTimeoutRef.current);
    }
    setIsLoading(false);
    setHasError(false);
  };

  const handleIframeError = (e: any) => {
    if (loadTimeoutRef.current) {
      clearTimeout(loadTimeoutRef.current);
    }
    setHasError(true);
    setIsLoading(false);
  };

  const handleClose = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onClose) {
      onClose();
    }
  };

  return (
    // Full screen overlay matching google-embed.html style
    <div 
      className="fixed inset-0 z-50 flex flex-col" 
      style={{ background: '#e8eaed' }}
      onClick={(e) => {
        // Only close if clicking the background
        if (e.target === e.currentTarget && onClose) {
          onClose();
        }
      }}
    >
      {/* Container */}
      <div className="flex flex-col h-full" onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className="bg-white border-b border-gray-300 px-5 py-3 flex items-center justify-between shadow-sm">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 text-2xl text-gray-600">
              <svg width="32" height="32" viewBox="0 0 48 48">
                <path fill="#4285F4" d="M38 10H30V6C30 4.9 29.1 4 28 4H10C8.9 4 8 4.9 8 6V34C8 35.1 8.9 36 10 36H38C39.1 36 40 35.1 40 34V12C40 10.9 39.1 10 38 10Z"/>
                <path fill="#FFC107" d="M38 10V20L48 10Z"/>
              </svg>
              <span className="font-normal">{allowEdit ? 'Drive Editor' : 'Drive Viewer'}</span>
            </div>
            <div className="text-lg font-medium text-gray-800">
              {file.name}
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <button
              onClick={openInDrive}
              className="px-6 py-2 border border-gray-300 rounded-md bg-white text-sm text-gray-600 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 flex items-center gap-2"
            >
              <ExternalLink className="w-4 h-4" />
              {allowEdit ? 'Chỉnh sửa trong Google Drive' : 'Xem trong Google Drive'}
            </button>
            
            {onClose && (
              <button
                onClick={handleClose}
                className="p-2 hover:bg-gray-100 rounded transition-colors"
              >
                <X className="w-5 h-5 text-gray-600" />
              </button>
            )}
          </div>
        </div>

        {/* Editor Container */}
        <div className="flex-1 relative flex p-5">
          {/* Editor Frame matching google-embed.html style */}
          <div className="flex-1 bg-white rounded-lg overflow-hidden relative" style={{ boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)' }}>
            
            {isLoading && !hasError && (
              <div className="absolute inset-0 bg-white flex items-center justify-center z-10">
                <div className="text-center">
                  <div className="w-12 h-12 border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
                  <div className="text-gray-600">Loading file...</div>
                </div>
              </div>
            )}
            
            {hasError && (
              <div className="absolute inset-0 bg-white flex items-center justify-center z-10">
                <div className="text-center">
                  <div className="text-red-500 mb-4">
                    <svg className="w-16 h-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <p className="text-gray-800 font-medium mb-2">Không thể tải file</p>
                  <p className="text-gray-600 text-sm mb-4">File có thể không được hỗ trợ để xem trực tiếp</p>
                  <button
                    onClick={openInDrive}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    Mở trong Google Drive
                  </button>
                </div>
              </div>
            )}
            
            <iframe
              ref={iframeRef}
              src={getEmbedUrl()}
              className={`w-full h-full border-0 ${hasError ? 'hidden' : ''}`}
              allowFullScreen
              onLoad={handleIframeLoad}
              onError={handleIframeError}
              allow={allowEdit 
                ? "accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; fullscreen" 
                : "accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"}
              sandbox={allowEdit
                ? "allow-scripts allow-same-origin allow-forms allow-popups allow-modals allow-downloads allow-presentation allow-top-navigation"
                : "allow-scripts allow-same-origin allow-popups"}
            />
          </div>
        </div>

        {/* Status Bar */}
        <div className="bg-white border-t border-gray-300 px-5 py-2 flex justify-between items-center text-sm text-gray-600">
          <div>
            {isLoading ? 'Loading file...' : hasError ? 'Lỗi khi tải file' : allowEdit ? 'Chế độ chỉnh sửa' : 'Chế độ xem (chỉ đọc)'}
          </div>
          <div className="text-xs">
            {!hasError && (allowEdit ? 'Bạn có thể chỉnh sửa trực tiếp trong cửa sổ này' : 'Chế độ chỉ đọc - Không thể chỉnh sửa')}
          </div>
        </div>
      </div>
    </div>
  );
}