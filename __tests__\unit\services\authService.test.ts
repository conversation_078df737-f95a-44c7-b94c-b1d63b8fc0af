import { authService } from '@/services/authService'
import Cookies from 'js-cookie'
import type { LoginRequest, LoginResponse, User } from '@/types/auth'

// Mock js-cookie
jest.mock('js-cookie', () => ({
  set: jest.fn(),
  get: jest.fn(),
  remove: jest.fn(),
}))

// Mock window.location.assign
const mockLocationAssign = jest.fn()

// Override location globally
delete (window as any).location
window.location = {
  href: '',
  origin: 'http://localhost',
  protocol: 'http:',
  host: 'localhost',
  hostname: 'localhost',
  port: '',
  pathname: '/',
  search: '',
  hash: '',
  assign: mockLocationAssign,
  replace: jest.fn(),
  reload: jest.fn(),
  toString: jest.fn(() => 'http://localhost/'),
} as any

describe('AuthService', () => {
  const mockUser: User = {
    id: 1,
    email: '<EMAIL>',
    name: 'Admin User',
    role: 'ADMIN',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  }

  const mockLoginResponse: LoginResponse = {
    token: 'mock-jwt-token',
    refreshToken: 'mock-refresh-token',
    user: mockUser,
  }

  const originalConsoleError = console.error

  beforeEach(() => {
    jest.clearAllMocks()
    global.fetch = jest.fn()
    mockLocationAssign.mockClear()
    // Suppress navigation errors from jsdom
    console.error = jest.fn((message) => {
      const messageStr = message?.toString ? message.toString() : String(message)
      if (messageStr.includes('Not implemented: navigation')) {
        return
      }
      originalConsoleError(message)
    })
  })

  afterEach(() => {
    console.error = originalConsoleError
  })

  describe('login', () => {
    it('successfully logs in with valid credentials', async () => {
      const credentials: LoginRequest = {
        email: '<EMAIL>',
        password: 'password123',
      }

      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockLoginResponse,
      })

      const result = await authService.login(credentials)

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/auth/login'),
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(credentials),
          credentials: 'include',
        })
      )

      expect(Cookies.set).toHaveBeenCalledWith('auth-token', 'mock-jwt-token', { expires: 7 })
      expect(Cookies.set).toHaveBeenCalledWith('refresh-token', 'mock-refresh-token', { expires: 30 })
      expect(result).toEqual(mockLoginResponse)
    })

    it('throws error with invalid credentials', async () => {
      const credentials: LoginRequest = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      }

      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        json: async () => ({ error: 'Invalid credentials' }),
      })

      await expect(authService.login(credentials)).rejects.toThrow('Invalid credentials')
    })

    it('handles network errors', async () => {
      const credentials: LoginRequest = {
        email: '<EMAIL>',
        password: 'password123',
      }

      ;(global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'))

      await expect(authService.login(credentials)).rejects.toThrow('Network error')
    })

    it('does not set cookies if tokens are not provided', async () => {
      const credentials: LoginRequest = {
        email: '<EMAIL>',
        password: 'password123',
      }

      const responseWithoutTokens = {
        user: mockUser,
      }

      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => responseWithoutTokens,
      })

      await authService.login(credentials)

      expect(Cookies.set).not.toHaveBeenCalled()
    })
  })

  describe('logout', () => {
    it('successfully logs out', async () => {
      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
      })

      await authService.logout()

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/auth/logout'),
        expect.objectContaining({
          method: 'POST',
          credentials: 'include',
        })
      )

      expect(Cookies.remove).toHaveBeenCalledWith('auth-token')
      expect(Cookies.remove).toHaveBeenCalledWith('refresh-token')
      expect(Cookies.remove).toHaveBeenCalledWith('session-id')
      // window.location.assign is called but jsdom doesn't properly mock it
    })

    it('clears cookies even if API call fails', async () => {
      ;(global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'))

      // logout will reject because of the network error, but still clears cookies in finally
      await authService.logout().catch(() => {})

      expect(Cookies.remove).toHaveBeenCalledWith('auth-token')
      expect(Cookies.remove).toHaveBeenCalledWith('refresh-token')
      expect(Cookies.remove).toHaveBeenCalledWith('session-id')
      // window.location.assign is called but jsdom doesn't properly mock it
    })
  })

  describe('getCurrentUser', () => {
    it('successfully fetches current user', async () => {
      const sessionResponse = {
        user: mockUser,
        session: {
          id: 'session-123',
          expiresAt: '2024-01-02T00:00:00Z',
          createdAt: '2024-01-01T00:00:00Z',
        },
      }

      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => sessionResponse,
      })

      const result = await authService.getCurrentUser()

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/auth/session'),
        expect.objectContaining({
          method: 'GET',
          credentials: 'include',
        })
      )

      expect(result).toEqual(mockUser)
    })

    it('throws error when session fetch fails', async () => {
      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
      })

      await expect(authService.getCurrentUser()).rejects.toThrow('Failed to get current user')
    })
  })

  describe('refreshToken', () => {
    it('successfully refreshes token', async () => {
      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockLoginResponse,
      })

      const result = await authService.refreshToken()

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/auth/refresh'),
        expect.objectContaining({
          method: 'POST',
          credentials: 'include',
        })
      )

      expect(Cookies.set).toHaveBeenCalledWith('auth-token', 'mock-jwt-token', { expires: 7 })
      expect(Cookies.set).toHaveBeenCalledWith('refresh-token', 'mock-refresh-token', { expires: 30 })
      expect(result).toEqual(mockLoginResponse)
    })

    it('throws error when refresh fails', async () => {
      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
      })

      await expect(authService.refreshToken()).rejects.toThrow('Failed to refresh token')
    })
  })

  describe('isAuthenticated', () => {
    it('returns true when auth token exists', () => {
      ;(Cookies.get as jest.Mock).mockReturnValueOnce('mock-token')

      const result = authService.isAuthenticated()

      expect(Cookies.get).toHaveBeenCalledWith('auth-token')
      expect(result).toBe(true)
    })

    it('returns false when auth token does not exist', () => {
      ;(Cookies.get as jest.Mock).mockReturnValueOnce(undefined)

      const result = authService.isAuthenticated()

      expect(Cookies.get).toHaveBeenCalledWith('auth-token')
      expect(result).toBe(false)
    })
  })

  describe('Singleton Pattern', () => {
    it('returns the same instance', () => {
      const instance1 = authService
      const instance2 = authService

      expect(instance1).toBe(instance2)
    })
  })
})