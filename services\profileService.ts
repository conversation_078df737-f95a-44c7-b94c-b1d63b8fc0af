import { BaseService } from './baseService'
import type { User } from '@/types/user'

export interface UpdateProfileInput {
  name?: string
  department?: string
  phone?: string
}

export interface ChangePasswordInput {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export class ProfileService extends BaseService {
  private static instance: ProfileService

  private constructor() {
    super()
  }

  static getInstance(): ProfileService {
    if (!ProfileService.instance) {
      ProfileService.instance = new ProfileService()
    }
    return ProfileService.instance
  }

  async getProfile(signal?: AbortSignal): Promise<User> {
    return this.get<User>('/users/profile', signal)
  }

  async updateProfile(data: UpdateProfileInput, signal?: AbortSignal): Promise<User> {
    return this.put<User>('/users/profile', data, { signal })
  }

  async changePassword(data: ChangePasswordInput, signal?: AbortSignal): Promise<{ success: boolean; message: string }> {
    return this.post<{ success: boolean; message: string }>('/users/change-password', data, { signal })
  }
}

export const profileService = ProfileService.getInstance()