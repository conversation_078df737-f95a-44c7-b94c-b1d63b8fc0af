'use client';

import { useState } from 'react';
import { FileTex<PERSON>, Eye, Edit3, Trash2, ExternalLink, Download } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { GoogleDriveViewer } from './GoogleDriveViewer';
import { useToast } from '@/hooks/useToast';
import type { EquipmentDocument } from '@/types/equipment';

interface GoogleDriveDocument extends EquipmentDocument {
  googleDriveId?: string;
  googleDriveUrl?: string;
  embedUrl?: string;
  _viewMode?: 'view' | 'edit';
}

interface GoogleDriveDocumentListProps {
  documents: GoogleDriveDocument[];
  onDelete?: (documentId: string) => void;
  canDelete?: boolean;
  canEdit?: boolean;
}

export function GoogleDriveDocumentList({
  documents,
  onDelete,
  canDelete = false,
  canEdit = true
}: GoogleDriveDocumentListProps) {
  const [selectedDocument, setSelectedDocument] = useState<GoogleDriveDocument | null>(null);
  const [showViewer, setShowViewer] = useState(false);
  const toast = useToast();

  const handleView = (e: React.MouseEvent, doc: GoogleDriveDocument, allowEdit: boolean = false) => {
    e.preventDefault();
    e.stopPropagation();
    setSelectedDocument({ ...doc, _viewMode: (allowEdit ? 'edit' : 'view') as 'edit' | 'view' });
    setShowViewer(true);
  };

  const handleEdit = (e: React.MouseEvent, doc: GoogleDriveDocument) => {
    e.preventDefault();
    e.stopPropagation();
    const editDoc = { ...doc, _viewMode: 'edit' as const };
    console.log('handleEdit - setting document with _viewMode:', editDoc._viewMode);
    setSelectedDocument(editDoc);
    setShowViewer(true);
  };

  const handleDownload = async (doc: GoogleDriveDocument) => {
    try {
      if (doc.googleDriveId) {
        // Check if it's a Google Workspace file that needs export
        const googleWorkspaceMimeTypes = [
          'application/vnd.google-apps.document',
          'application/vnd.google-apps.spreadsheet',
          'application/vnd.google-apps.presentation'
        ];
        
        if (doc.mimeType && googleWorkspaceMimeTypes.includes(doc.mimeType)) {
          // Use export endpoint for Google Workspace files
          toast.info('Đang chuẩn bị tải xuống tệp Google Workspace...');
          window.open(`/api/google-drive/export?fileId=${doc.googleDriveId}&mimeType=${encodeURIComponent(doc.mimeType)}`, '_blank');
        } else {
          // Try direct download for other files
          window.open(`/api/google-drive/export?fileId=${doc.googleDriveId}&mimeType=${encodeURIComponent(doc.mimeType || '')}`, '_blank');
        }
      } else {
        // Fallback to original download URL
        window.open(doc.fileUrl, '_blank');
      }
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Không thể tải xuống tệp. Vui lòng thử lại sau.');
    }
  };

  const getFileIcon = (doc: GoogleDriveDocument) => {
    const mimeType = doc.mimeType || '';
    
    if (mimeType.includes('document') || doc.fileName.endsWith('.doc') || doc.fileName.endsWith('.docx')) {
      return <FileText className="w-10 h-10 text-blue-500 flex-shrink-0" />;
    } else if (mimeType.includes('spreadsheet') || doc.fileName.endsWith('.xls') || doc.fileName.endsWith('.xlsx')) {
      return <FileText className="w-10 h-10 text-green-500 flex-shrink-0" />;
    } else if (mimeType.includes('pdf') || doc.fileName.endsWith('.pdf')) {
      return <FileText className="w-10 h-10 text-red-500 flex-shrink-0" />;
    } else if (mimeType.startsWith('image/')) {
      return <FileText className="w-10 h-10 text-purple-500 flex-shrink-0" />;
    } else {
      return <FileText className="w-10 h-10 text-gray-500 flex-shrink-0" />;
    }
  };

  if (documents.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <FileText className="w-12 h-12 mx-auto mb-3 text-gray-300" />
        <p>Chưa có tài liệu nào</p>
      </div>
    );
  }

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {documents.map((doc) => (
          <div
            key={doc.id}
            className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow bg-white"
          >
            <div className="flex items-start gap-3">
              {getFileIcon(doc)}
              
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-gray-900 truncate">
                  {doc.fileName}
                </h4>
                
                <div className="mt-1 text-sm text-gray-500 space-y-1">
                  <p>Kích thước: {(doc.fileSize / (1024 * 1024)).toFixed(2)} MB</p>
                  <p>Ngày tải: {new Date(doc.uploadedAt).toLocaleDateString('vi-VN')}</p>
                  {doc.googleDriveId ? (
                    <p className="text-green-600 flex items-center gap-1">
                      <svg className="w-4 h-4" viewBox="0 0 24 24">
                        <path fill="currentColor" d="M7.71 3.5L1.15 15l4.58 7.5h13.12L24 15l-6.56-11.5H7.71zm3.43 1.67h5.71L21.29 13H12.4l4.44-7.83zm-5.71 0h5.33l-4.44 7.83L2 13l5.43-9.83zM6.67 14h5.33l2.67 4.67H9.33L6.67 14z"/>
                      </svg>
                      Google Drive
                    </p>
                  ) : (
                    <p className="text-blue-600 flex items-center gap-1">
                      <FileText className="w-4 h-4" />
                      Server
                    </p>
                  )}
                </div>
                
                <div className="mt-3 flex flex-wrap gap-2">
                  <button
                    onClick={(e) => handleView(e, doc, false)}
                    className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-700 text-sm"
                  >
                    <Eye className="w-4 h-4" />
                    Xem
                  </button>
                  
                  {canEdit && doc.googleDriveId && (
                    <button
                      onClick={(e) => handleEdit(e, doc)}
                      className="inline-flex items-center gap-1 text-green-600 hover:text-green-700 text-sm"
                      title="Mở trong Google Drive để chỉnh sửa"
                    >
                      <Edit3 className="w-4 h-4" />
                      Chỉnh sửa
                    </button>
                  )}
                  
                  <button
                    onClick={() => handleDownload(doc)}
                    className="inline-flex items-center gap-1 text-gray-600 hover:text-gray-700 text-sm"
                  >
                    <Download className="w-4 h-4" />
                    Tải xuống
                  </button>
                  
                  {canDelete && onDelete && (
                    <button
                      onClick={() => onDelete(doc.id)}
                      className="inline-flex items-center gap-1 text-red-600 hover:text-red-700 text-sm"
                    >
                      <Trash2 className="w-4 h-4" />
                      Xóa
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Google Drive Viewer Modal */}
      {showViewer && selectedDocument && (
        <GoogleDriveViewer
          key={selectedDocument.id}
          file={{
            id: selectedDocument.googleDriveId || selectedDocument.id,
            name: selectedDocument.fileName,
            mimeType: selectedDocument.mimeType || 'application/octet-stream',
            url: selectedDocument.googleDriveUrl || selectedDocument.fileUrl
          }}
          onClose={() => {
            setShowViewer(false);
            setSelectedDocument(null);
          }}
          allowEdit={(() => {
            const isEdit = selectedDocument?._viewMode === 'edit';
            console.log('GoogleDriveViewer - allowEdit prop:', isEdit, 'viewMode:', selectedDocument?._viewMode);
            return isEdit;
          })()}
        />
      )}
    </>
  );
}