import React from 'react'
import { renderHook, waitFor, act } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import {
  useCatalogs,
  useCatalog,
  useCheckCatalogCode,
  useCatalogEquipments,
  useCreateCatalog,
  useUpdateCatalog,
  useDeleteCatalog,
  useBulkDeleteCatalogs,
  useExportCatalogs,
  useImportCatalogs,
  catalogKeys
} from '@/hooks/queries/useCatalogs'
import { catalogService } from '@/services/catalogService'
import {
  createMockCatalog,
  createMockCatalogsResponse,
  createMockCreateCatalogRequest,
  createMockUpdateCatalogRequest,
  createMockCheckCodeResponse,
  createMockEquipment
} from '../../utils/catalog-test-utils'

// Mock services
jest.mock('@/services/catalogService')
jest.mock('@/hooks/useToast', () => ({
  useToast: () => ({
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
    info: jest.fn()
  })
}))

describe('Catalog Hooks', () => {
  let queryClient: QueryClient
  let wrapper: React.FC<{ children: React.ReactNode }>

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false, gcTime: 0 },
        mutations: { retry: false }
      }
    })
    
    wrapper = ({ children }) => (
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    )
    
    // Mock window.URL functions
    global.URL.createObjectURL = jest.fn(() => 'blob:mock-url')
    global.URL.revokeObjectURL = jest.fn()
    
    jest.clearAllMocks()
  })

  afterEach(() => {
    queryClient.clear()
  })

  describe('useCatalogs', () => {
    it('should fetch catalogs without filters', async () => {
      const mockData = createMockCatalogsResponse()
      ;(catalogService.getCatalogs as jest.Mock).mockResolvedValueOnce(mockData)

      const { result } = renderHook(() => useCatalogs(), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(catalogService.getCatalogs).toHaveBeenCalledWith({}, expect.any(AbortSignal))
      expect(result.current.data).toEqual(mockData)
    })

    it('should fetch catalogs with filters', async () => {
      const mockData = createMockCatalogsResponse()
      const filters = {
        search: 'thiết bị',
        status: 'ACTIVE' as const,
        page: 2,
        limit: 20
      }
      ;(catalogService.getCatalogs as jest.Mock).mockResolvedValueOnce(mockData)

      const { result } = renderHook(() => useCatalogs(filters), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(catalogService.getCatalogs).toHaveBeenCalledWith(filters, expect.any(AbortSignal))
    })

    it('should not fetch when disabled', () => {
      const { result } = renderHook(() => useCatalogs({}, false), { wrapper })

      expect(catalogService.getCatalogs).not.toHaveBeenCalled()
      expect(result.current.isPending).toBe(true)
    })

    it('should handle fetch errors', async () => {
      const error = new Error('Network error')
      ;(catalogService.getCatalogs as jest.Mock).mockRejectedValueOnce(error)

      const { result } = renderHook(() => useCatalogs(), { wrapper })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toEqual(error)
    })

    it('should use correct query key', () => {
      const filters = { search: 'test', page: 1 }
      renderHook(() => useCatalogs(filters), { wrapper })

      const queryState = queryClient.getQueryState(catalogKeys.list(filters))
      expect(queryState).toBeDefined()
    })
  })

  describe('useCatalog', () => {
    it('should fetch catalog by id', async () => {
      const mockCatalog = createMockCatalog()
      ;(catalogService.getCatalogById as jest.Mock).mockResolvedValueOnce(mockCatalog)

      const { result } = renderHook(() => useCatalog('catalog-1'), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(catalogService.getCatalogById).toHaveBeenCalledWith('catalog-1', expect.any(AbortSignal))
      expect(result.current.data).toEqual(mockCatalog)
    })

    it('should not fetch when id is empty', () => {
      const { result } = renderHook(() => useCatalog(''), { wrapper })

      expect(catalogService.getCatalogById).not.toHaveBeenCalled()
      expect(result.current.isPending).toBe(true)
    })

    it('should not fetch when disabled', () => {
      const { result } = renderHook(() => useCatalog('catalog-1', false), { wrapper })

      expect(catalogService.getCatalogById).not.toHaveBeenCalled()
      expect(result.current.isPending).toBe(true)
    })
  })

  describe('useCheckCatalogCode', () => {
    it('should check catalog code when enabled', async () => {
      const mockResponse = createMockCheckCodeResponse({ exists: false })
      ;(catalogService.checkCatalogCode as jest.Mock).mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useCheckCatalogCode('CAT999', true), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(catalogService.checkCatalogCode).toHaveBeenCalledWith('CAT999')
      expect(result.current.data).toEqual(mockResponse)
    })

    it('should not check when code is empty', () => {
      const { result } = renderHook(() => useCheckCatalogCode('', true), { wrapper })

      expect(catalogService.checkCatalogCode).not.toHaveBeenCalled()
      expect(result.current.isPending).toBe(true)
    })

    it('should not check when disabled', () => {
      const { result } = renderHook(() => useCheckCatalogCode('CAT999', false), { wrapper })

      expect(catalogService.checkCatalogCode).not.toHaveBeenCalled()
      expect(result.current.isPending).toBe(true)
    })
  })

  describe('useCatalogEquipments', () => {
    it('should fetch catalog equipments', async () => {
      const mockEquipments = [
        createMockEquipment({ id: 'eq-1' }),
        createMockEquipment({ id: 'eq-2' })
      ]
      ;(catalogService.getCatalogEquipments as jest.Mock).mockResolvedValueOnce(mockEquipments)

      const { result } = renderHook(() => useCatalogEquipments('catalog-1'), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(catalogService.getCatalogEquipments).toHaveBeenCalledWith('catalog-1', expect.any(AbortSignal))
      expect(result.current.data).toEqual(mockEquipments)
    })
  })

  describe('useCreateCatalog', () => {
    it('should create catalog successfully', async () => {
      const request = createMockCreateCatalogRequest()
      const newCatalog = createMockCatalog(request)
      ;(catalogService.createCatalog as jest.Mock).mockResolvedValueOnce(newCatalog)

      const { result } = renderHook(() => useCreateCatalog(), { wrapper })

      await act(async () => {
        await result.current.mutateAsync(request)
      })

      expect(catalogService.createCatalog).toHaveBeenCalledWith(request)
      expect(result.current.isSuccess).toBe(true)
    })

    it('should invalidate queries after creation', async () => {
      const request = createMockCreateCatalogRequest()
      const newCatalog = createMockCatalog(request)
      ;(catalogService.createCatalog as jest.Mock).mockResolvedValueOnce(newCatalog)

      const invalidateSpy = jest.spyOn(queryClient, 'invalidateQueries')
      const { result } = renderHook(() => useCreateCatalog(), { wrapper })

      await act(async () => {
        await result.current.mutateAsync(request)
      })

      expect(invalidateSpy).toHaveBeenCalledWith({ queryKey: catalogKeys.lists() })
    })

    it('should handle creation errors', async () => {
      const error = new Error('Validation failed')
      ;(catalogService.createCatalog as jest.Mock).mockRejectedValueOnce(error)

      const { result } = renderHook(() => useCreateCatalog(), { wrapper })

      await act(async () => {
        try {
          await result.current.mutateAsync(createMockCreateCatalogRequest())
        } catch (e) {
          // Expected error
        }
      })

      expect(result.current.isError).toBe(true)
      expect(result.current.error).toEqual(error)
    })
  })

  describe('useUpdateCatalog', () => {
    it('should update catalog successfully', async () => {
      const request = createMockUpdateCatalogRequest()
      const updatedCatalog = createMockCatalog({ ...request, id: 'catalog-1' })
      ;(catalogService.updateCatalog as jest.Mock).mockResolvedValueOnce(updatedCatalog)

      const { result } = renderHook(() => useUpdateCatalog(), { wrapper })

      await act(async () => {
        await result.current.mutateAsync({ id: 'catalog-1', data: request })
      })

      expect(catalogService.updateCatalog).toHaveBeenCalledWith('catalog-1', request)
      expect(result.current.isSuccess).toBe(true)
    })

    it('should invalidate queries after update', async () => {
      const request = createMockUpdateCatalogRequest()
      const updatedCatalog = createMockCatalog({ ...request, id: 'catalog-1' })
      ;(catalogService.updateCatalog as jest.Mock).mockResolvedValueOnce(updatedCatalog)

      const invalidateSpy = jest.spyOn(queryClient, 'invalidateQueries')
      const { result } = renderHook(() => useUpdateCatalog(), { wrapper })

      await act(async () => {
        await result.current.mutateAsync({ id: 'catalog-1', data: request })
      })

      expect(invalidateSpy).toHaveBeenCalledWith({ queryKey: catalogKeys.lists() })
      expect(invalidateSpy).toHaveBeenCalledWith({ queryKey: catalogKeys.detail('catalog-1') })
    })
  })

  describe('useDeleteCatalog', () => {
    it('should delete catalog without force', async () => {
      ;(catalogService.deleteCatalog as jest.Mock).mockResolvedValueOnce(undefined)

      const { result } = renderHook(() => useDeleteCatalog(), { wrapper })

      await act(async () => {
        await result.current.mutateAsync({ id: 'catalog-1' })
      })

      expect(catalogService.deleteCatalog).toHaveBeenCalledWith('catalog-1', false)
      expect(result.current.isSuccess).toBe(true)
    })

    it('should delete catalog with force', async () => {
      ;(catalogService.deleteCatalog as jest.Mock).mockResolvedValueOnce(undefined)

      const { result } = renderHook(() => useDeleteCatalog(), { wrapper })

      await act(async () => {
        await result.current.mutateAsync({ id: 'catalog-1', force: true })
      })

      expect(catalogService.deleteCatalog).toHaveBeenCalledWith('catalog-1', true)
      expect(result.current.isSuccess).toBe(true)
    })

    it('should invalidate queries after deletion', async () => {
      ;(catalogService.deleteCatalog as jest.Mock).mockResolvedValueOnce(undefined)

      const invalidateSpy = jest.spyOn(queryClient, 'invalidateQueries')
      const { result } = renderHook(() => useDeleteCatalog(), { wrapper })

      await act(async () => {
        await result.current.mutateAsync({ id: 'catalog-1' })
      })

      expect(invalidateSpy).toHaveBeenCalledWith({ queryKey: catalogKeys.lists() })
    })
  })

  describe('useBulkDeleteCatalogs', () => {
    it('should delete multiple catalogs', async () => {
      const mockResponse = { success: 3, failed: 0 }
      ;(catalogService.bulkDeleteCatalogs as jest.Mock).mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useBulkDeleteCatalogs(), { wrapper })
      const ids = ['catalog-1', 'catalog-2', 'catalog-3']

      await act(async () => {
        await result.current.mutateAsync(ids)
      })

      expect(catalogService.bulkDeleteCatalogs).toHaveBeenCalledWith(ids)
      expect(result.current.isSuccess).toBe(true)
    })

    it('should handle partial failures', async () => {
      const mockResponse = { success: 2, failed: 1 }
      ;(catalogService.bulkDeleteCatalogs as jest.Mock).mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useBulkDeleteCatalogs(), { wrapper })

      await act(async () => {
        const response = await result.current.mutateAsync(['cat-1', 'cat-2', 'cat-3'])
        expect(response.success).toBe(2)
        expect(response.failed).toBe(1)
      })
    })
  })

  describe('useExportCatalogs', () => {
    it('should export catalogs to file', async () => {
      const mockBlob = new Blob(['mock data'], { type: 'application/xlsx' })
      ;(catalogService.exportCatalogs as jest.Mock).mockResolvedValueOnce(mockBlob)

      // Mock document methods
      const createElementSpy = jest.spyOn(document, 'createElement')
      const appendChildSpy = jest.spyOn(document.body, 'appendChild').mockImplementation()
      const removeChildSpy = jest.spyOn(document.body, 'removeChild').mockImplementation()

      const mockAnchor = document.createElement('a')
      const clickSpy = jest.spyOn(mockAnchor, 'click').mockImplementation()
      createElementSpy.mockReturnValueOnce(mockAnchor)

      const { result } = renderHook(() => useExportCatalogs(), { wrapper })

      await act(async () => {
        await result.current.mutateAsync()
      })

      expect(catalogService.exportCatalogs).toHaveBeenCalledWith({})
      expect(global.URL.createObjectURL).toHaveBeenCalledWith(mockBlob)
      expect(clickSpy).toHaveBeenCalled()
      expect(global.URL.revokeObjectURL).toHaveBeenCalledWith('blob:mock-url')
      
      // Cleanup
      createElementSpy.mockRestore()
      appendChildSpy.mockRestore()
      removeChildSpy.mockRestore()
    })

    it('should export with filters', async () => {
      const mockBlob = new Blob(['mock data'])
      ;(catalogService.exportCatalogs as jest.Mock).mockResolvedValueOnce(mockBlob)

      const filters = { search: 'thiết bị', status: 'ACTIVE' as const }
      const { result } = renderHook(() => useExportCatalogs(), { wrapper })

      await act(async () => {
        await result.current.mutateAsync(filters)
      })

      expect(catalogService.exportCatalogs).toHaveBeenCalledWith(filters)
    })
  })

  describe('useImportCatalogs', () => {
    it('should import catalogs from file', async () => {
      const mockResponse = { success: 5, failed: 0 }
      ;(catalogService.importCatalogs as jest.Mock).mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useImportCatalogs(), { wrapper })
      const file = new File(['mock data'], 'catalogs.xlsx')

      await act(async () => {
        await result.current.mutateAsync(file)
      })

      expect(catalogService.importCatalogs).toHaveBeenCalledWith(file)
      expect(result.current.isSuccess).toBe(true)
    })

    it('should invalidate queries after import', async () => {
      const mockResponse = { success: 5, failed: 0 }
      ;(catalogService.importCatalogs as jest.Mock).mockResolvedValueOnce(mockResponse)

      const invalidateSpy = jest.spyOn(queryClient, 'invalidateQueries')
      const { result } = renderHook(() => useImportCatalogs(), { wrapper })

      await act(async () => {
        await result.current.mutateAsync(new File([''], 'test.xlsx'))
      })

      expect(invalidateSpy).toHaveBeenCalledWith({ queryKey: catalogKeys.lists() })
    })

    it('should handle import with errors', async () => {
      const mockResponse = { 
        success: 3, 
        failed: 2, 
        errors: ['Row 3: Invalid', 'Row 5: Duplicate'] 
      }
      ;(catalogService.importCatalogs as jest.Mock).mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useImportCatalogs(), { wrapper })

      await act(async () => {
        const response = await result.current.mutateAsync(new File([''], 'test.xlsx'))
        expect(response.failed).toBe(2)
        expect(response.errors).toHaveLength(2)
      })
    })
  })
})