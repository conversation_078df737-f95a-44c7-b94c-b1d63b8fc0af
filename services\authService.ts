import type { LoginRequest, LoginResponse, User } from '@/types/auth'
import { tokenManager } from '@/lib/tokenManager'

interface SessionResponse {
  user: User
  session: {
    id: string
    expiresAt: string
    createdAt: string
  }
}

class AuthService {
  private static instance: AuthService
  private baseUrl: string

  private constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002'
  }

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService()
    }
    return AuthService.instance
  }

  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await fetch(`${this.baseUrl}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
      credentials: 'include', // Include cookies
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Login failed')
    }

    const data = await response.json()
    
    // Store access token in memory only
    if (data.accessToken) {
      tokenManager.setAccessToken(data.accessToken, data.expiresIn || 600)
    }
    
    return data
  }

  async logout(): Promise<void> {
    try {
      await fetch(`${this.baseUrl}/api/auth/logout`, {
        method: 'POST',
        credentials: 'include',
      })
    } finally {
      // Clear access token from memory
      tokenManager.clear()
      if (typeof window !== 'undefined' && window.location) {
        window.location.assign('/login')
      }
    }
  }

  async getCurrentUser(): Promise<User> {
    const token = tokenManager.getAccessToken()
    if (!token) {
      throw new Error('No access token available')
    }

    const response = await fetch(`${this.baseUrl}/api/auth/session`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      credentials: 'include',
    })

    if (!response.ok) {
      // Include status code in error for better handling
      const error = new Error(`Failed to get current user: ${response.status}`)
      ;(error as any).status = response.status
      throw error
    }

    const data: SessionResponse = await response.json()
    return data.user
  }

  async refreshToken(): Promise<LoginResponse> {
    const response = await fetch(`${this.baseUrl}/api/auth/refresh`, {
      method: 'POST',
      credentials: 'include',
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: 'Failed to refresh token' }))
      console.error('Token refresh failed:', error)
      
      // Handle specific error cases
      if (error.error === 'No refresh token found' || error.error === 'Session not found') {
        // Clear token from memory since the session is invalid
        tokenManager.clear()
      }
      
      throw new Error(error.error || `Failed to refresh token: ${response.status}`)
    }

    const data = await response.json()
    
    // Store new access token in memory
    if (data.accessToken) {
      tokenManager.setAccessToken(data.accessToken, data.expiresIn || 600)
    }
    
    return data
  }

  isAuthenticated(): boolean {
    // Check if we have a valid access token in memory
    const token = tokenManager.getAccessToken()
    return !!token
  }

  getAccessToken(): string | null {
    return tokenManager.getAccessToken()
  }

  isTokenExpired(): boolean {
    return tokenManager.isExpired()
  }
}

export const authService = AuthService.getInstance()