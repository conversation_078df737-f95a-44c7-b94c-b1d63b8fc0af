import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/db'
import { getServerSession } from '@/lib/auth-server'
import { BiddingDocumentStatus } from '@prisma/client'

const createBiddingDocumentSchema = z.object({
  code: z.string().min(1),
  name: z.string().min(1),
  description: z.string().optional(),
  customerName: z.string().optional(),
  status: z.nativeEnum(BiddingDocumentStatus).optional().default(BiddingDocumentStatus.PENDING),
  equipmentItems: z.array(z.object({
    equipmentId: z.string(),
    pageRange: z.object({
      from: z.number(),
      to: z.number(),
    }).optional(),
  })).optional(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') as BiddingDocumentStatus | null
    const customerName = searchParams.get('customerName') || ''
    const createdFrom = searchParams.get('createdFrom') || ''
    const createdTo = searchParams.get('createdTo') || ''
    
    const skip = (page - 1) * limit

    const where: any = {}
    const andConditions: any[] = []
    
    if (search) {
      andConditions.push({
        OR: [
          { code: { contains: search, mode: 'insensitive' } },
          { name: { contains: search, mode: 'insensitive' } },
          { customerName: { contains: search, mode: 'insensitive' } },
        ]
      })
    }
    
    if (status) {
      andConditions.push({ status })
    }
    
    if (customerName) {
      andConditions.push({ customerName: { contains: customerName, mode: 'insensitive' } })
    }
    
    if (createdFrom || createdTo) {
      const dateFilter: any = { createdAt: {} }
      if (createdFrom) {
        dateFilter.createdAt.gte = new Date(createdFrom)
      }
      if (createdTo) {
        const endDate = new Date(createdTo)
        endDate.setHours(23, 59, 59, 999)
        dateFilter.createdAt.lte = endDate
      }
      andConditions.push(dateFilter)
    }
    
    if (andConditions.length > 0) {
      where.AND = andConditions
    }

    const [data, total] = await Promise.all([
      prisma.biddingDocument.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              username: true,
            },
          },
        },
      }),
      prisma.biddingDocument.count({ where }),
    ])

    return NextResponse.json({
      items: data,
      total,
      page,
      limit,
    })
  } catch (error) {
    console.error('Error fetching bidding documents:', error)
    return NextResponse.json(
      { error: 'Failed to fetch bidding documents' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  const startTime = Date.now()
  let errorDetails: any = {}

  try {
    // Environment validation
    const requiredEnvVars = ['DATABASE_URL']
    const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar])
    if (missingEnvVars.length > 0) {
      console.error('[BIDDING_DOC_CREATE] Missing environment variables:', missingEnvVars)
      errorDetails.missingEnvVars = missingEnvVars
      throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`)
    }

    console.log('[BIDDING_DOC_CREATE] Starting bidding document creation', {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      workingDirectory: process.cwd(),
    })

    // Authentication check with detailed logging
    const session = await getServerSession(request)
    if (!session) {
      console.warn('[BIDDING_DOC_CREATE] Authentication failed - no session')
      return NextResponse.json({ 
        error: 'Unauthorized',
        details: process.env.NODE_ENV === 'development' ? 'No valid session found' : undefined 
      }, { status: 401 })
    }

    console.log('[BIDDING_DOC_CREATE] Session validated', {
      userId: session.userId,
      role: session.role,
      sessionId: session.sessionId,
    })

    // Request body parsing with error handling
    let body: any
    try {
      body = await request.json()
      console.log('[BIDDING_DOC_CREATE] Request body parsed', {
        hasCode: !!body.code,
        hasName: !!body.name,
        hasEquipmentItems: !!body.equipmentItems,
        equipmentItemsCount: body.equipmentItems?.length || 0,
      })
    } catch (parseError: any) {
      console.error('[BIDDING_DOC_CREATE] Failed to parse request body:', parseError)
      return NextResponse.json({
        error: 'Invalid JSON in request body',
        details: process.env.NODE_ENV === 'development' ? parseError.message : undefined
      }, { status: 400 })
    }

    // Data validation with detailed error reporting
    let validatedData: any
    try {
      validatedData = createBiddingDocumentSchema.parse(body)
      console.log('[BIDDING_DOC_CREATE] Data validation successful', {
        code: validatedData.code,
        name: validatedData.name,
        status: validatedData.status,
      })
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        console.error('[BIDDING_DOC_CREATE] Data validation failed:', validationError.issues)
        return NextResponse.json({
          error: 'Invalid request data',
          details: validationError.issues,
          providedData: process.env.NODE_ENV === 'development' ? body : undefined
        }, { status: 400 })
      }
      throw validationError
    }

    // Database operations with detailed logging
    console.log('[BIDDING_DOC_CREATE] Checking for existing document with code:', validatedData.code)
    
    let existingDocument: any
    try {
      existingDocument = await prisma.biddingDocument.findUnique({
        where: { code: validatedData.code },
      })
    } catch (dbError: any) {
      console.error('[BIDDING_DOC_CREATE] Database error during duplicate check:', dbError)
      errorDetails.dbError = {
        operation: 'findUnique',
        error: dbError.message,
        code: dbError.code,
      }
      throw new Error(`Database connection error during duplicate check: ${dbError.message}`)
    }

    if (existingDocument) {
      console.warn('[BIDDING_DOC_CREATE] Duplicate document code detected:', validatedData.code)
      return NextResponse.json({
        error: 'Bidding document with this code already exists',
        duplicateId: existingDocument.id,
        existingCreatedAt: existingDocument.createdAt
      }, { status: 400 })
    }

    // Create bidding document with comprehensive error handling
    console.log('[BIDDING_DOC_CREATE] Creating new bidding document')
    let biddingDocument: any
    try {
      biddingDocument = await prisma.biddingDocument.create({
        data: {
          ...validatedData,
          createdBy: session.userId,
        },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              username: true,
            },
          },
        },
      })
    } catch (createError: any) {
      console.error('[BIDDING_DOC_CREATE] Database error during creation:', createError)
      errorDetails.createError = {
        operation: 'create',
        error: createError.message,
        code: createError.code,
        data: validatedData,
      }
      
      // Check for common database errors
      if (createError.code === 'P2002') {
        return NextResponse.json({
          error: 'Unique constraint violation',
          details: 'A bidding document with this code already exists',
          constraint: createError.meta?.target
        }, { status: 409 })
      }
      
      if (createError.code === 'P2003') {
        return NextResponse.json({
          error: 'Foreign key constraint violation',
          details: 'Referenced user does not exist',
          foreignKey: createError.meta?.field_name
        }, { status: 400 })
      }
      
      throw new Error(`Database error during creation: ${createError.message}`)
    }

    const duration = Date.now() - startTime
    console.log('[BIDDING_DOC_CREATE] Success', {
      id: biddingDocument.id,
      code: biddingDocument.code,
      createdBy: biddingDocument.createdBy,
      duration: `${duration}ms`,
    })

    return NextResponse.json(biddingDocument, { status: 201 })
  } catch (error: any) {
    const duration = Date.now() - startTime
    
    // Comprehensive error logging
    const errorLog = {
      timestamp: new Date().toISOString(),
      error: error.message,
      stack: error.stack,
      duration: `${duration}ms`,
      environment: process.env.NODE_ENV,
      workingDirectory: process.cwd(),
      userAgent: request.headers.get('user-agent'),
      origin: request.headers.get('origin'),
      ...errorDetails,
    }
    
    console.error('[BIDDING_DOC_CREATE] Operation failed:', errorLog)

    // Return appropriate error response
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Invalid request data',
        details: error.issues
      }, { status: 400 })
    }

    // For production, don't expose sensitive error details
    const isProduction = process.env.NODE_ENV === 'production'
    return NextResponse.json({
      error: 'Failed to create bidding document',
      message: isProduction ? 'Internal server error' : error.message,
      details: isProduction ? undefined : errorDetails,
      timestamp: new Date().toISOString(),
      requestId: Math.random().toString(36).substring(7), // Simple request ID for tracking
    }, { status: 500 })
  }
}