import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { authService } from '@/services/authService'
import { useToast } from '@/hooks/useToast'
import { tokenManager } from '@/lib/tokenManager'
import type { LoginRequest, User } from '@/types/auth'

// Query keys factory
export const authKeys = {
  all: ['auth'] as const,
  session: () => [...authKeys.all, 'session'] as const,
  user: () => [...authKeys.all, 'user'] as const,
}

// Get current user session
export function useSession(enabled = true) {
  const router = useRouter()
  
  return useQuery({
    queryKey: authKeys.session(),
    queryFn: () => authService.getCurrentUser(),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 1,
    throwOnError: false,
    meta: {
      onError: (error: any) => {
        console.error('Session query error:', error)
        // Redirect to login on auth errors
        if (error?.status === 401 || error?.status === 403 || error?.message?.includes('Unauthorized')) {
          // Clear token from memory
          tokenManager.clear()
          router.push('/login')
        }
      }
    }
  })
}

// Login mutation
export function useLogin() {
  const queryClient = useQueryClient()
  const router = useRouter()
  const { success, error } = useToast()

  return useMutation({
    mutationFn: (credentials: LoginRequest) => authService.login(credentials),
    onSuccess: (data) => {
      // Set user data in cache
      queryClient.setQueryData(authKeys.user(), data.user)
      queryClient.invalidateQueries({ queryKey: authKeys.session() })
      
      success('Login successful!')
      router.push('/dashboard')
    },
    onError: (err: Error) => {
      error(err.message || 'Login failed. Please check your credentials.')
    },
  })
}

// Logout mutation
export function useLogout() {
  const queryClient = useQueryClient()
  const router = useRouter()
  const { success } = useToast()

  return useMutation({
    mutationFn: () => authService.logout(),
    onSuccess: () => {
      // Clear all auth-related cache
      queryClient.removeQueries({ queryKey: authKeys.all })
      queryClient.clear()
      
      success('Logged out successfully')
      router.push('/login')
    },
  })
}

// Refresh token mutation
export function useRefreshToken() {
  const queryClient = useQueryClient()
  const { error } = useToast()

  return useMutation({
    mutationFn: () => authService.refreshToken(),
    onSuccess: (data) => {
      // Update user data in cache
      queryClient.setQueryData(authKeys.user(), data.user)
      queryClient.invalidateQueries({ queryKey: authKeys.session() })
    },
    onError: (err: Error) => {
      error('Session expired. Please login again.')
      // Redirect to login
      window.location.href = '/login'
    },
  })
}

// Custom hook to check authentication status
export function useIsAuthenticated() {
  const { data: user, isLoading } = useSession()
  
  return {
    isAuthenticated: !!user,
    isLoading,
    user,
  }
}

// Auto refresh is now handled automatically by the API client
// No need for a separate hook

// Convenience hook for backward compatibility
export function useAuth() {
  const { data: user, isLoading } = useSession()
  
  return {
    user,
    isLoading,
    isAuthenticated: !!user,
  }
}