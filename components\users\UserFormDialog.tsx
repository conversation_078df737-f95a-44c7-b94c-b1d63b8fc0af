'use client'

import React, { useState, useEffect } from 'react'
import { X, Eye, EyeOff, AlertCircle } from 'lucide-react'
import { Dialog } from '@/components/ui/Dialog'
import { Button } from '@/components/ui/Button'
import { Select } from '@/components/ui/Select'
import { useCreateUser, useUpdateUser, useCheckUsername, useCheckEmail } from '@/hooks/queries/useUsers'
import type { User } from '@/types/user'
import type { CreateUserInput, UpdateUserInput } from '@/services/userService'

interface UserFormDialogProps {
  isOpen: boolean
  onClose: () => void
  user: User | null
}

const roleOptions = [
  { value: 'ADMIN', label: 'Quản trị viên' },
  { value: 'USER', label: 'Người dùng' },
]

const statusOptions = [
  { value: 'ACTIVE', label: 'Hoạt động' },
  { value: 'INACTIVE', label: 'Ngừng hoạt động' },
]

export function UserFormDialog({ isOpen, onClose, user }: UserFormDialogProps) {
  const isEdit = !!user
  const createMutation = useCreateUser()
  const updateMutation = useUpdateUser()
  const checkUsername = useCheckUsername()
  const checkEmail = useCheckEmail()

  const [showPassword, setShowPassword] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    name: '',
    role: 'USER' as 'ADMIN' | 'USER',
    department: '',
    phone: '',
    status: 'ACTIVE' as 'ACTIVE' | 'INACTIVE',
  })

  useEffect(() => {
    if (user) {
      setFormData({
        username: user.username,
        email: user.email,
        password: '',
        name: user.name,
        role: user.role,
        department: user.department || '',
        phone: user.phone || '',
        status: user.status,
      })
    } else {
      setFormData({
        username: '',
        email: '',
        password: '',
        name: '',
        role: 'USER',
        department: '',
        phone: '',
        status: 'ACTIVE',
      })
    }
    setErrors({})
  }, [user, isOpen])

  const validateForm = async () => {
    const newErrors: Record<string, string> = {}

    const trimmedUsername = formData.username.trim()
    if (!isEdit && !trimmedUsername) {
      newErrors.username = 'Tên đăng nhập là bắt buộc'
    } else if (!isEdit && trimmedUsername.length < 3) {
      newErrors.username = 'Tên đăng nhập phải có ít nhất 3 ký tự'
    }

    const trimmedEmail = formData.email.trim()
    if (!trimmedEmail) {
      newErrors.email = 'Email là bắt buộc'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(trimmedEmail)) {
      newErrors.email = 'Email không hợp lệ'
    }

    const trimmedPassword = formData.password.trim()
    if (!isEdit && !trimmedPassword) {
      newErrors.password = 'Mật khẩu là bắt buộc'
    } else if (!isEdit && trimmedPassword.length < 6) {
      newErrors.password = 'Mật khẩu phải có ít nhất 6 ký tự'
    } else if (isEdit && trimmedPassword && trimmedPassword.length < 6) {
      newErrors.password = 'Mật khẩu phải có ít nhất 6 ký tự'
    }

    const trimmedName = formData.name.trim()
    if (!trimmedName) {
      newErrors.name = 'Họ tên là bắt buộc'
    }

    const trimmedPhone = formData.phone.trim()
    if (trimmedPhone && !/^[0-9+\-\s()]+$/.test(trimmedPhone)) {
      newErrors.phone = 'Số điện thoại không hợp lệ'
    }

    if (!isEdit && trimmedUsername && !newErrors.username) {
      const result = await checkUsername.mutateAsync(trimmedUsername)
      if (!result.available) {
        newErrors.username = 'Tên đăng nhập đã tồn tại'
      }
    }

    if (trimmedEmail && !newErrors.email && (!isEdit || trimmedEmail !== user?.email)) {
      const result = await checkEmail.mutateAsync(trimmedEmail)
      if (!result.available) {
        newErrors.email = 'Email đã tồn tại'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!(await validateForm())) {
      return
    }

    try {
      if (isEdit) {
        const updateData: UpdateUserInput = {
          email: formData.email.trim() !== user.email ? formData.email.trim() : undefined,
          name: formData.name.trim(),
          role: formData.role,
          department: formData.department.trim() || undefined,
          phone: formData.phone.trim() || undefined,
          status: formData.status,
        }
        
        if (formData.password) {
          updateData.password = formData.password
        }

        await updateMutation.mutateAsync({
          id: user.id,
          data: updateData,
        })
      } else {
        const createData: CreateUserInput = {
          username: formData.username.trim(),
          email: formData.email.trim(),
          password: formData.password.trim(),
          name: formData.name.trim(),
          role: formData.role,
          department: formData.department.trim() || undefined,
          phone: formData.phone.trim() || undefined,
          status: formData.status,
        }
        
        await createMutation.mutateAsync(createData)
      }
      
      onClose()
    } catch (error) {
      console.error('Form submission error:', error)
    }
  }

  return (
    <Dialog isOpen={isOpen} onClose={onClose}>
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            {isEdit ? 'Chỉnh sửa người dùng' : 'Thêm người dùng mới'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            {/* Username - only for create */}
            {!isEdit && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Tên đăng nhập *
                </label>
                <input
                  type="text"
                  value={formData.username}
                  onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                  className={`w-full h-10 px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 ${
                    errors.username ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                  }`}
                  placeholder="Nhập tên đăng nhập"
                />
                {errors.username && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    {errors.username}
                  </p>
                )}
              </div>
            )}

            {/* Email */}
            <div className={isEdit ? 'col-span-2' : ''}>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Email *
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                className={`w-full h-10 px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 ${
                  errors.email ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                }`}
                placeholder="Nhập email"
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  {errors.email}
                </p>
              )}
            </div>
          </div>

          {/* Password */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Mật khẩu {!isEdit && '*'}
            </label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                className={`w-full h-10 px-3 py-2 pr-10 border rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 ${
                  errors.password ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                }`}
                placeholder={isEdit ? 'Để trống để giữ nguyên' : 'Nhập mật khẩu'}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
            {errors.password && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
                <AlertCircle className="h-3 w-3 mr-1" />
                {errors.password}
              </p>
            )}
            {!isEdit && (
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Người dùng được quản trị viên tạo sẽ tự động xác thực email
              </p>
            )}
          </div>

          {/* Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Họ tên *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className={`w-full h-10 px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 ${
                errors.name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              }`}
              placeholder="Nguyễn Văn A"
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
                <AlertCircle className="h-3 w-3 mr-1" />
                {errors.name}
              </p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            {/* Role */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Vai trò *
              </label>
              <Select
                value={formData.role}
                onChange={(e) => setFormData({ ...formData, role: e.target.value as any })}
                className="w-full"
              >
                {roleOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </Select>
            </div>

            {/* Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Trạng thái *
              </label>
              <Select
                value={formData.status}
                onChange={(e) => setFormData({ ...formData, status: e.target.value as any })}
                className="w-full"
              >
                {statusOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            {/* Department */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Phòng ban
              </label>
              <input
                type="text"
                value={formData.department}
                onChange={(e) => setFormData({ ...formData, department: e.target.value })}
                className="w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
                placeholder="Phòng IT"
              />
            </div>

            {/* Phone */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Số điện thoại
              </label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                className={`w-full h-10 px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 ${
                  errors.phone ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                }`}
                placeholder="0901234567"
              />
              {errors.phone && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  {errors.phone}
                </p>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="secondary"
              onClick={onClose}
              disabled={createMutation.isPending || updateMutation.isPending}
            >
              Hủy
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={createMutation.isPending || updateMutation.isPending}
            >
              {createMutation.isPending || updateMutation.isPending 
                ? 'Đang xử lý...' 
                : isEdit ? 'Cập nhật' : 'Tạo người dùng'
              }
            </Button>
          </div>
        </form>
      </div>
    </Dialog>
  )
}