# Google Drive Integration Setup (No-Login Approach)

This guide explains how to set up Google Drive integration using a refresh token, eliminating the need for users to log in every time.

## Prerequisites

1. Google Cloud Console account
2. Google Drive API enabled
3. OAuth 2.0 credentials created

## Initial Setup (One-time only)

### Step 1: Environment Variables

Add the following to your `.env.local` file:

```env
# Google Drive Configuration
GOOGLE_DRIVE_FOLDER_ID=134k_Dqd8pwjXib1tJeOLrPUJGZSFWIYd
GOOGLE_REFRESH_TOKEN=1//0gEvycf38mnR4CgYIARAAGBASNwF-L9Ir_Mq4DjrK3r36naJXxvNOxlxHIXLBiIvtHMGmO2OhfvBC6ElZDESk4d5y6nG4J2j27jU

# Google OAuth2 Credentials
GOOGLE_CLIENT_ID=************-me2n7jbfuvrv7r1cmr4523midpqsmc8e.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=http://localhost:3002/auth/callback
```

### Step 2: Get New Refresh Token (if needed)

If the refresh token expires or you need a new one:

```bash
npm run google:auth
```

This will:
1. Open a browser window for Google authentication
2. After login, save the refresh token to `google-tokens.json`
3. Display the refresh token to update in `.env.local`

### Step 3: Verify Setup

The integration should now work automatically without requiring user login.

## How It Works

1. **Refresh Token**: The system uses a permanent refresh token stored in environment variables
2. **Auto-Authentication**: On server start, the system automatically authenticates using the refresh token
3. **Token Refresh**: Access tokens are automatically refreshed when they expire
4. **No User Login**: Users don't need to authenticate - everything works automatically

## Features

### For Equipment Attachments

1. **File Selection**: Browse and select files from the configured Google Drive folder
2. **File Upload**: Upload files directly to Google Drive
3. **File Viewing**: View/edit files in embedded Google editors
4. **File Management**: Delete files from Google Drive

### API Endpoints

- `GET /api/google-drive/files` - List files in the Drive folder
- `POST /api/google-drive/upload` - Upload a new file
- `GET /api/google-drive/file/[id]` - Get file details
- `DELETE /api/google-drive/file/[id]` - Delete a file
- `GET /api/auth/google/status` - Check authentication status (always true)

## Troubleshooting

### Refresh Token Expired

If you see authentication errors:
1. Run `npm run google:auth` to get a new refresh token
2. Update `GOOGLE_REFRESH_TOKEN` in `.env.local`
3. Restart the server

### Missing Files

If files don't appear:
1. Check that `GOOGLE_DRIVE_FOLDER_ID` is correct
2. Ensure the Google account has access to the folder
3. Verify files are not in trash

### Permission Errors

Ensure the OAuth2 credentials have these scopes:
- `https://www.googleapis.com/auth/drive.file`
- `https://www.googleapis.com/auth/drive`

## Security Notes

1. **Never commit** `.env.local` or refresh tokens to version control
2. **Rotate tokens** periodically for security
3. **Limit folder access** to only necessary files
4. **Monitor usage** through Google Cloud Console

## Migration from OAuth Flow

If migrating from user-based OAuth:
1. No changes needed in the UI components
2. Authentication is handled automatically server-side
3. Remove any OAuth redirect handlers
4. Update API calls to remove authorization headers