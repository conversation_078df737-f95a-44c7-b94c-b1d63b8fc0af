import { NextResponse } from 'next/server';
import { googleDriveServiceNoLogin } from '@/services/googleDriveServiceNoLogin';

export async function GET() {
  try {
    // Check if Google Drive is configured
    const isConfigured = await googleDriveServiceNoLogin.isAuthenticated();
    console.log('isConfigured', isConfigured);

    if (!isConfigured) {
      return NextResponse.json({
        success: false,
        files: [],
        error: 'Google Drive not configured'
      });
    }

    const files = await googleDriveServiceNoLogin.listFiles();
    
    return NextResponse.json({
      success: true,
      files
    });
  } catch (error) {
    console.error('List files error:', error);
    return NextResponse.json({
      success: false,
      files: [],
      error: error instanceof Error ? error.message : 'Failed to list files'
    });
  }
}