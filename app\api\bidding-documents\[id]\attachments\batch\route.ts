import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from '@/lib/auth-server'
import { prisma } from '@/lib/db'
import type { BiddingDocumentAttachment } from '@/types/biddingDocument'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const { attachments } = await request.json()

    if (!attachments || !Array.isArray(attachments)) {
      return NextResponse.json(
        { error: 'Invalid attachments data' },
        { status: 400 }
      )
    }

    // Check if bidding document exists
    const biddingDocument = await prisma.biddingDocument.findUnique({
      where: { id }
    })

    if (!biddingDocument) {
      return NextResponse.json(
        { error: 'Bidding document not found' },
        { status: 404 }
      )
    }

    // Save attachments that were uploaded to Google Drive
    const savedAttachments = await Promise.all(
      attachments
        .filter((att: BiddingDocumentAttachment) => att.source === 'google_drive')
        .map((att: BiddingDocumentAttachment) =>
          prisma.biddingDocumentAttachment.create({
            data: {
              biddingDocumentId: id,
              fileName: att.fileName,
              fileUrl: att.fileUrl,
              fileSize: att.fileSize,
              mimeType: att.mimeType,
              source: 'google_drive',
              googleDriveId: att.googleDriveId,
              googleDriveUrl: att.googleDriveUrl,
              uploadedBy: session.user?.id || 'system'
            }
          })
        )
    )

    return NextResponse.json({ 
      success: true, 
      count: savedAttachments.length 
    })
  } catch (error) {
    console.error('Error saving attachments:', error)
    return NextResponse.json(
      { error: 'Failed to save attachments' },
      { status: 500 }
    )
  }
}