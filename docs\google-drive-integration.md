# Google Drive Integration for Equipment Attachments

## Overview

The Equipment Edit screen now supports Google Drive OAuth 2.0 integration for managing attachments. Users can:
- Select files from their Google Drive
- Upload files directly to Google Drive
- Edit files in Google Drive
- View files embedded in the application

## Implementation Details

### Components Created

1. **GoogleDrivePicker.tsx**
   - Implements Google Picker API
   - Allows users to select files from their Google Drive
   - Supports multi-select and file type filtering

2. **GoogleDriveUpload.tsx**
   - <PERSON>les direct file uploads to Google Drive
   - Converts files to appropriate Google Docs format when possible

3. **GoogleDriveViewer.tsx**
   - Embeds Google Drive files for viewing/editing
   - Supports fullscreen mode
   - Allows opening files in Google Drive

4. **GoogleDriveDocumentList.tsx**
   - Enhanced document list with Google Drive support
   - Shows Google Drive icon for cloud files
   - Supports viewing, editing, and deleting

5. **GoogleDriveAttachments.tsx**
   - Main component that combines all Google Drive features
   - Handles authentication state
   - Manages document state for the equipment form

### API Routes

1. **/api/auth/google/**
   - Initiates OAuth 2.0 flow

2. **/api/auth/google/callback/**
   - Handles OAuth callback
   - Stores tokens in secure cookies

3. **/api/auth/google/status/**
   - Checks authentication status

4. **/api/google-drive/upload/**
   - Handles file uploads to Google Drive

### Services

- **googleDriveService.ts**
  - Singleton service for Google Drive operations
  - Manages OAuth client
  - Handles token management

### Configuration

Add the following environment variables to `.env.local`:

```env
# Google Drive OAuth 2.0
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:3002/api/auth/google/callback
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id
NEXT_PUBLIC_GOOGLE_API_KEY=your-google-api-key
NEXT_PUBLIC_GOOGLE_APP_ID=your-google-app-id
```

## Setting up Google Cloud Console

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google Drive API
4. Create OAuth 2.0 credentials:
   - Application type: Web application
   - Authorized redirect URIs: http://localhost:3002/api/auth/google/callback
5. Create an API key for the Picker API
6. Configure OAuth consent screen

## Usage

1. Navigate to Equipment Edit page
2. In the attachments section, click "Connect to Google Drive" if not authenticated
3. Once authenticated, use either:
   - "Choose from Google Drive" to select existing files
   - "Upload to Google Drive" to upload new files
4. Files can be viewed/edited directly in the embedded viewer
5. Click on file names to open in Google Drive

## Security Considerations

- OAuth tokens are stored in secure httpOnly cookies
- Access tokens expire after 1 hour
- Refresh tokens are used to obtain new access tokens
- Files are shared with "anyone with link" permission for embedding

## Future Enhancements

1. Implement server-side document association storage
2. Add file preview thumbnails
3. Support for more file types
4. Implement folder organization
5. Add file sharing controls
6. Batch operations support