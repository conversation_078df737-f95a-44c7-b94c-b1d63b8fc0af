'use client'

import { useState } from 'react'
import { BarChart3, Edit, Save, X } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { useUpdateEvaluation } from '@/hooks/queries/useBiddingDocuments'
import type { BiddingDocument } from '@/types/biddingDocument'

interface EvaluationSectionProps {
  document: BiddingDocument
}

export function EvaluationSection({ document }: EvaluationSectionProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [notes, setNotes] = useState(document.evaluationResult?.notes || '')
  
  const updateMutation = useUpdateEvaluation()

  const handleSave = async () => {
    try {
      await updateMutation.mutateAsync({
        biddingDocumentId: document.id,
        notes
      })
      setIsEditing(false)
    } catch (error) {
      // Error handled by mutation
    }
  }

  const handleCancel = () => {
    setNotes(document.evaluationResult?.notes || '')
    setIsEditing(false)
  }

  const successRate = document.evaluationResult?.successRate || 85 // Mock data

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Đánh giá kết quả
        </h2>
        {!isEditing && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
          >
            <Edit className="w-4 h-4 mr-2" />
            Chỉnh sửa ghi chú
          </Button>
        )}
      </div>

      {/* Success Rate */}
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-2">
          <BarChart3 className="w-5 h-5 text-gray-400" />
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Tỷ lệ AI xử lý thành công
          </span>
        </div>
        <div className="relative pt-1">
          <div className="flex mb-2 items-center justify-between">
            <div>
              <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-green-600 bg-green-200">
                {successRate}%
              </span>
            </div>
          </div>
          <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-200 dark:bg-gray-700">
            <div
              style={{ width: `${successRate}%` }}
              className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-green-500"
            />
          </div>
        </div>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Hệ thống AI đã xử lý thành công {successRate}% yêu cầu kỹ thuật từ tài liệu mời thầu
        </p>
      </div>

      {/* Notes */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Ghi chú đánh giá
        </label>
        {isEditing ? (
          <div className="space-y-3">
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
              placeholder="Nhập ghi chú đánh giá..."
            />
            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleCancel}
                disabled={updateMutation.isPending}
              >
                <X className="w-4 h-4 mr-1" />
                Hủy
              </Button>
              <Button
                type="button"
                size="sm"
                onClick={handleSave}
                disabled={updateMutation.isPending}
              >
                <Save className="w-4 h-4 mr-1" />
                {updateMutation.isPending ? 'Đang lưu...' : 'Lưu'}
              </Button>
            </div>
          </div>
        ) : (
          <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
            <p className="text-sm text-gray-700 dark:text-gray-300">
              {notes || 'Chưa có ghi chú đánh giá'}
            </p>
            {document.evaluationResult && (
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                Đánh giá bởi {document.evaluationResult.evaluatedBy.name} lúc{' '}
                {new Date(document.evaluationResult.evaluatedAt).toLocaleString('vi-VN')}
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  )
}