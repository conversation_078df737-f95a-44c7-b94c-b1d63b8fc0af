import { PrismaClient, Role, Status } from '@prisma/client'
import { hashPassword } from '../lib/auth'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seed...')

  // Create demo users
  const users = [
    {
      username: 'admin',
      email: '<EMAIL>',
      password: 'admin123',
      name: 'System Administrator',
      role: Role.ADMIN,
      department: 'IT',
      phone: '0901234567',
    },
    {
      username: 'user1',
      email: '<EMAIL>',
      password: 'user123',
      name: 'Regular User',
      role: Role.USER,
      department: 'Operations',
      phone: '0907654321',
    },
    {
      username: 'user2',
      email: '<EMAIL>',
      password: 'user123',
      name: 'Another User',
      role: Role.USER,
      department: 'Sales',
      phone: '0909876543',
    },
  ]

  for (const userData of users) {
    const { password, ...userInfo } = userData
    const passwordHash = await hashPassword(password)

    const user = await prisma.user.upsert({
      where: { username: userData.username },
      update: {
        ...userInfo,
        passwordHash,
        status: Status.ACTIVE,
        emailVerified: true,
      },
      create: {
        ...userInfo,
        passwordHash,
        status: Status.ACTIVE,
        emailVerified: true,
      },
    })

    console.log(`✅ Created/Updated user: ${user.username} (${user.role})`)
  }

  // Create some sample audit logs
  const adminUser = await prisma.user.findUnique({
    where: { username: 'admin' },
  })

  if (adminUser) {
    await prisma.auditLog.create({
      data: {
        userId: adminUser.id,
        action: 'SEED_DATABASE',
        details: { message: 'Database seeded with demo data' },
        ipAddress: '127.0.0.1',
      },
    })
    console.log('✅ Created sample audit log')
  }

  // Create sample catalogs
  const catalogs = [
    {
      catalogCode: 'CAT001',
      catalogName: 'Thiết bị chẩn đoán hình ảnh',
      description: 'Máy X-quang, CT Scanner, MRI, siêu âm và các thiết bị chẩn đoán hình ảnh khác',
    },
    {
      catalogCode: 'CAT002',
      catalogName: 'Thiết bị phẫu thuật',
      description: 'Dụng cụ phẫu thuật, dao mổ điện, thiết bị nội soi và phụ kiện phẫu thuật',
    },
    {
      catalogCode: 'CAT003',
      catalogName: 'Thiết bị xét nghiệm',
      description: 'Máy xét nghiệm máu, sinh hóa, huyết học, vi sinh và các thiết bị phân tích',
    },
    {
      catalogCode: 'CAT004',
      catalogName: 'Thiết bị hồi sức cấp cứu',
      description: 'Máy thở, monitor theo dõi, máy sốc điện, bơm tiêm điện và thiết bị cấp cứu',
    },
    {
      catalogCode: 'CAT005',
      catalogName: 'Thiết bị nha khoa',
      description: 'Ghế nha khoa, máy khoan, thiết bị chụp X-quang răng và dụng cụ nha khoa',
    },
    {
      catalogCode: 'CAT006',
      catalogName: 'Thiết bị vật lý trị liệu',
      description: 'Máy điện xung, sóng ngắn, laser trị liệu và thiết bị phục hồi chức năng',
    },
    {
      catalogCode: 'CAT007',
      catalogName: 'Thiết bị thận nhân tạo',
      description: 'Máy lọc máu, máy lọc màng bụng và các thiết bị điều trị thận',
    },
    {
      catalogCode: 'CAT008',
      catalogName: 'Thiết bị tim mạch',
      description: 'Máy điện tim, holter, máy siêu âm tim và thiết bị can thiệp tim mạch',
    },
    {
      catalogCode: 'CAT009',
      catalogName: 'Thiết bị sản phụ khoa',
      description: 'Máy monitor sản khoa, máy siêu âm thai, thiết bị sinh non và dụng cụ sản khoa',
    },
    {
      catalogCode: 'CAT010',
      catalogName: 'Thiết bị nội soi',
      description: 'Máy nội soi tiêu hóa, nội soi phế quản, nội soi tai mũi họng và phụ kiện',
    },
    {
      catalogCode: 'CAT011',
      catalogName: 'Thiết bị gây mê hồi sức',
      description: 'Máy gây mê, máy thở, monitor theo dõi và thiết bị kiểm soát đau',
    },
    {
      catalogCode: 'CAT012',
      catalogName: 'Thiết bị y học hạt nhân',
      description: 'Máy PET/CT, SPECT, máy đo mật độ xương và thiết bị xạ trị',
    },
    {
      catalogCode: 'CAT013',
      catalogName: 'Thiết bị tiệt trùng',
      description: 'Nồi hấp tiệt trùng, máy plasma, máy ETO và thiết bị khử khuẩn',
    },
    {
      catalogCode: 'CAT014',
      catalogName: 'Thiết bị nhãn khoa',
      description: 'Máy đo khúc xạ, máy soi đáy mắt, máy laser mắt và dụng cụ nhãn khoa',
    },
    {
      catalogCode: 'CAT015',
      catalogName: 'Thiết bị tai mũi họng',
      description: 'Máy đo thính lực, máy nội soi TMH, kính hiển vi phẫu thuật và dụng cụ TMH',
    },
    {
      catalogCode: 'CAT016',
      catalogName: 'Thiết bị dinh dưỡng',
      description: 'Máy pha chế dinh dưỡng, bơm dinh dưỡng và thiết bị đánh giá dinh dưỡng',
    },
    {
      catalogCode: 'CAT017',
      catalogName: 'Thiết bị chăm sóc bệnh nhân',
      description: 'Giường bệnh điện, xe lăn, nệm chống loét và thiết bị hỗ trợ bệnh nhân',
    },
    {
      catalogCode: 'CAT018',
      catalogName: 'Thiết bị thẩm mỹ',
      description: 'Máy laser thẩm mỹ, máy RF, máy triệt lông và thiết bị làm đẹp',
    },
    {
      catalogCode: 'CAT019',
      catalogName: 'Thiết bị dược phẩm',
      description: 'Tủ bảo quản thuốc, máy đóng gói, máy pha chế và thiết bị kiểm nghiệm dược',
    },
    {
      catalogCode: 'CAT020',
      catalogName: 'Thiết bị an toàn y tế',
      description: 'Hệ thống báo động, thiết bị bảo hộ, tủ an toàn sinh học và thiết bị phòng dịch',
    },
  ]

  if (adminUser) {
    for (const catalogData of catalogs) {
      const catalog = await prisma.catalog.upsert({
        where: { catalogCode: catalogData.catalogCode },
        update: {
          ...catalogData,
          status: Status.ACTIVE,
          createdBy: adminUser.id,
        },
        create: {
          ...catalogData,
          status: Status.ACTIVE,
          createdBy: adminUser.id,
        },
      })
      console.log(`✅ Created/Updated catalog: ${catalog.catalogCode} - ${catalog.catalogName}`)
    }
    console.log(`✅ Created ${catalogs.length} sample catalogs`)
  }

  console.log('🎉 Database seed completed successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })