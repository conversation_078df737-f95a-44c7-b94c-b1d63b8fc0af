import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from '@/lib/auth-server'
import { prisma } from '@/lib/db'
import { BiddingDocumentStatus } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')
    const period = searchParams.get('period') || 'month' // 'week', 'month', 'quarter', 'year'

    // Set default date range if not provided
    const now = new Date()
    let startDate: Date
    let endDate = dateTo ? new Date(dateTo) : now

    if (dateFrom) {
      startDate = new Date(dateFrom)
    } else {
      startDate = new Date(now)
      switch (period) {
        case 'week':
          startDate.setDate(now.getDate() - 30) // Last 30 days for weekly view
          break
        case 'quarter':
          startDate.setMonth(now.getMonth() - 3)
          break
        case 'year':
          startDate.setFullYear(now.getFullYear() - 1)
          break
        default: // month
          startDate.setMonth(now.getMonth() - 6) // Last 6 months
      }
    }

    // Ensure end date includes full day
    endDate.setHours(23, 59, 59, 999)

    // 1. Bidding Documents Trend Over Time
    const biddingTrend = await prisma.$queryRaw`
      SELECT 
        DATE_TRUNC(${period === 'week' ? 'week' : 'month'}, "createdAt") as period,
        COUNT(*) as count,
        COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed,
        COUNT(CASE WHEN status = 'IN_PROGRESS' THEN 1 END) as in_progress,
        COUNT(CASE WHEN status = 'PENDING' THEN 1 END) as pending
      FROM "BiddingDocument"
      WHERE "createdAt" >= ${startDate} AND "createdAt" <= ${endDate}
      GROUP BY DATE_TRUNC(${period === 'week' ? 'week' : 'month'}, "createdAt")
      ORDER BY period ASC
    ` as Array<{
      period: Date
      count: bigint
      completed: bigint
      in_progress: bigint
      pending: bigint
    }>

    // 2. Success Rate Analysis
    const successRateData = await prisma.biddingDocument.groupBy({
      by: ['status'],
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      },
      _count: {
        status: true
      }
    })

    const totalDocuments = successRateData.reduce((sum, item) => sum + item._count.status, 0)
    const completedDocuments = successRateData.find(item => item.status === BiddingDocumentStatus.COMPLETED)?._count.status || 0
    const successRate = totalDocuments > 0 ? (completedDocuments / totalDocuments) * 100 : 0

    // 3. Document Types Distribution (by customer/category)
    const customerDistribution = await prisma.biddingDocument.groupBy({
      by: ['customerName'],
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate
        },
        customerName: {
          not: null
        }
      },
      _count: {
        customerName: true
      },
      orderBy: {
        _count: {
          customerName: 'desc'
        }
      },
      take: 10 // Top 10 customers
    })

    // 4. Equipment Categories Distribution
    const equipmentCategoriesData = await prisma.catalog.findMany({
      include: {
        _count: {
          select: {
            equipment: {
              where: {
                createdAt: {
                  gte: startDate,
                  lte: endDate
                }
              }
            }
          }
        }
      },
      orderBy: {
        equipment: {
          _count: 'desc'
        }
      },
      take: 10
    })

    // 5. User Activity Analysis
    const userActivityData = await prisma.user.findMany({
      where: {
        biddingDocuments: {
          some: {
            createdAt: {
              gte: startDate,
              lte: endDate
            }
          }
        }
      },
      include: {
        _count: {
          select: {
            biddingDocuments: {
              where: {
                createdAt: {
                  gte: startDate,
                  lte: endDate
                }
              }
            }
          }
        }
      },
      orderBy: {
        biddingDocuments: {
          _count: 'desc'
        }
      },
      take: 10
    })

    // 6. Monthly Growth Analysis
    const monthlyGrowth = await prisma.$queryRaw`
      WITH monthly_stats AS (
        SELECT 
          DATE_TRUNC('month', "createdAt") as month,
          COUNT(*) as current_month_count
        FROM "BiddingDocument"
        WHERE "createdAt" >= ${startDate} AND "createdAt" <= ${endDate}
        GROUP BY DATE_TRUNC('month', "createdAt")
      ),
      growth_calc AS (
        SELECT 
          month,
          current_month_count,
          LAG(current_month_count, 1) OVER (ORDER BY month) as previous_month_count
        FROM monthly_stats
      )
      SELECT 
        month,
        current_month_count,
        previous_month_count,
        CASE 
          WHEN previous_month_count > 0 THEN
            ROUND(((current_month_count::float - previous_month_count::float) / previous_month_count::float) * 100, 2)
          ELSE 0
        END as growth_percentage
      FROM growth_calc
      ORDER BY month ASC
    ` as Array<{
      month: Date
      current_month_count: bigint
      previous_month_count: bigint | null
      growth_percentage: number
    }>

    // 7. Performance Metrics
    const avgCompletionTime = await prisma.$queryRaw`
      SELECT 
        AVG(EXTRACT(DAY FROM ("updatedAt" - "createdAt"))) as avg_days
      FROM "BiddingDocument"
      WHERE 
        status = 'COMPLETED'
        AND "createdAt" >= ${startDate} 
        AND "createdAt" <= ${endDate}
    ` as Array<{ avg_days: number | null }>

    // Transform the data for frontend consumption
    const analytics = {
      biddingTrend: biddingTrend.map(item => ({
        period: item.period.toISOString().split('T')[0],
        total: Number(item.count),
        completed: Number(item.completed),
        inProgress: Number(item.in_progress),
        pending: Number(item.pending)
      })),
      
      successRate: {
        percentage: Math.round(successRate * 100) / 100,
        total: totalDocuments,
        completed: completedDocuments,
        distribution: successRateData.map(item => ({
          status: item.status,
          count: item._count.status,
          percentage: Math.round((item._count.status / totalDocuments) * 100 * 100) / 100
        }))
      },
      
      customerDistribution: customerDistribution.map(item => ({
        name: item.customerName || 'Không xác định',
        count: item._count.customerName,
        percentage: totalDocuments > 0 ? Math.round((item._count.customerName / totalDocuments) * 100 * 100) / 100 : 0
      })),
      
      equipmentCategories: equipmentCategoriesData.map(catalog => ({
        name: catalog.name,
        code: catalog.code,
        count: catalog._count.equipment,
        description: catalog.description
      })),
      
      userActivity: userActivityData.map(user => ({
        id: user.id,
        name: user.name,
        username: user.username,
        documentsCount: user._count.biddingDocuments,
        role: user.role
      })),
      
      monthlyGrowth: monthlyGrowth.map(item => ({
        month: item.month.toISOString().split('T')[0],
        count: Number(item.current_month_count),
        previousCount: item.previous_month_count ? Number(item.previous_month_count) : 0,
        growthPercentage: item.growth_percentage || 0
      })),
      
      performanceMetrics: {
        avgCompletionDays: avgCompletionTime[0]?.avg_days ? Math.round(avgCompletionTime[0].avg_days * 10) / 10 : 0,
        totalDocuments,
        activeDocuments: successRateData.find(item => item.status === BiddingDocumentStatus.IN_PROGRESS)?._count.status || 0,
        completionRate: successRate
      },
      
      dateRange: {
        from: startDate.toISOString().split('T')[0],
        to: endDate.toISOString().split('T')[0],
        period
      },
      
      generatedAt: new Date().toISOString()
    }

    return NextResponse.json(analytics)
  } catch (error) {
    console.error('Error fetching dashboard analytics:', error)
    
    let errorMessage = 'Failed to fetch analytics data'
    let errorDetails = 'Unknown error'
    
    if (error instanceof Error) {
      errorDetails = error.message
      if (error.message.includes('connect')) {
        errorMessage = 'Database connection error'
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Database query timeout'
      }
    }
    
    return NextResponse.json(
      { 
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? errorDetails : 'Internal server error'
      },
      { status: 500 }
    )
  }
}