import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { equipmentService } from '@/services/equipmentService';
import type {
  CreateEquipmentDto,
  UpdateEquipmentDto,
  EquipmentFilter
} from '@/types/equipment';

// Equipment queries
export function useEquipments(filter?: EquipmentFilter) {
  return useQuery({
    queryKey: ['equipments', filter],
    queryFn: ({ signal }) => equipmentService.getEquipments(filter, signal),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useEquipment(id: string) {
  return useQuery({
    queryKey: ['equipment', id],
    queryFn: ({ signal }) => equipmentService.getEquipmentById(id, signal),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

// Equipment mutations
export function useCreateEquipment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateEquipmentDto) => equipmentService.createEquipment(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['equipments'] });
    },
  });
}

export function useUpdateEquipment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateEquipmentDto }) =>
      equipmentService.updateEquipment(id, data),
    onSuccess: (updatedEquipment) => {
      queryClient.invalidateQueries({ queryKey: ['equipments'] });
      queryClient.invalidateQueries({ queryKey: ['equipment', updatedEquipment.id] });
    },
  });
}

export function useDeleteEquipment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => equipmentService.deleteEquipment(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['equipments'] });
    },
  });
}

// Bulk operations
export function useImportEquipment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (file: File) => equipmentService.importEquipment(file),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['equipments'] });
    },
  });
}

export function useExportEquipment() {
  return useMutation({
    mutationFn: (filter?: EquipmentFilter) => equipmentService.exportEquipment(filter),
  });
}

// Statistics
export function useEquipmentStats() {
  return useQuery({
    queryKey: ['equipment-stats'],
    queryFn: () => equipmentService.getEquipmentStats(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Document management hooks
export function useEquipmentDocuments(equipmentId: string) {
  return useQuery({
    queryKey: ['equipment', equipmentId, 'documents'],
    queryFn: ({ signal }) => equipmentService.getDocuments(equipmentId, signal),
    enabled: !!equipmentId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useUploadDocument() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ equipmentId, file }: { equipmentId: string; file: File }) =>
      equipmentService.uploadDocument(equipmentId, file),
    onSuccess: (_, { equipmentId }) => {
      queryClient.invalidateQueries({ queryKey: ['equipment', equipmentId, 'documents'] });
      queryClient.invalidateQueries({ queryKey: ['equipment', equipmentId] });
    },
  });
}

export function useDeleteDocument() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ equipmentId, documentId }: { equipmentId: string; documentId: string }) =>
      equipmentService.deleteDocument(equipmentId, documentId),
    onSuccess: (_, { equipmentId }) => {
      queryClient.invalidateQueries({ queryKey: ['equipment', equipmentId, 'documents'] });
      queryClient.invalidateQueries({ queryKey: ['equipment', equipmentId] });
    },
  });
}