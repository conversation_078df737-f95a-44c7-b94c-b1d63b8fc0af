'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { 
  FileText, 
  Eye, 
  Edit, 
  Trash2, 
  Download,
  FileCheck,
  MoreVertical 
} from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/Button'
import { Pagination } from '@/components/ui/Pagination'
import { TooltipProvider } from '@/components/ui/Tooltip'
import { TruncatedText } from '@/components/ui/TruncatedText'
import { DeleteBiddingDocumentDialog } from './DeleteBiddingDocumentDialog'
import { EmptyState } from './EmptyState'
import { BiddingDocumentStatus } from '@/types/biddingDocument'
import { useExportTechnicalResponse } from '@/hooks/queries/useBiddingDocuments'
import type { BiddingDocument } from '@/types/biddingDocument'

interface BiddingDocumentTableProps {
  documents: BiddingDocument[]
  isLoading: boolean
  pagination: {
    page: number
    limit: number
    total: number
    onPageChange: (page: number) => void
    onPageSizeChange?: (limit: number) => void
  }
  onCreateClick?: () => void
  hasActiveFilters?: boolean
}

export function BiddingDocumentTable({
  documents,
  isLoading,
  pagination,
  onCreateClick,
  hasActiveFilters = false
}: BiddingDocumentTableProps) {
  const router = useRouter()
  const [deletingDocumentId, setDeletingDocumentId] = useState<string | null>(null)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  
  const exportMutation = useExportTechnicalResponse()

  const getStatusBadge = (status: BiddingDocumentStatus) => {
    const statusConfig = {
      [BiddingDocumentStatus.PENDING]: { label: 'Pending', className: 'bg-yellow-100 text-yellow-700' },
      [BiddingDocumentStatus.IN_PROGRESS]: { label: 'In Progress', className: 'bg-blue-100 text-blue-700' },
      [BiddingDocumentStatus.COMPLETED]: { label: 'Completed', className: 'bg-green-100 text-green-700' },
    }

    const config = statusConfig[status] || { label: status, className: 'bg-gray-100 text-gray-700' }
    
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${config.className}`}>
        {config.label}
      </span>
    )
  }

  const handleExport = (doc: BiddingDocument, format: 'WORD' | 'PDF' | 'EXCEL') => {
    exportMutation.mutate({ id: doc.id, format })
    setActiveDropdown(null)
  }

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-8 text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="mt-2 text-gray-500">Đang tải dữ liệu...</p>
        </div>
      </div>
    )
  }

  if (documents.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <EmptyState 
          onCreateClick={onCreateClick || (() => {})}
          hasFilters={hasActiveFilters}
        />
      </div>
    )
  }

  return (
    <TooltipProvider>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Mã hồ sơ
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Tên hồ sơ
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Khách hàng
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Số thiết bị
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Trạng thái
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Ngày tạo
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Thao tác
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {documents.map((doc) => (
                <tr key={doc.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                    {doc.code}
                  </td>
                  <td className="px-6 py-4">
                    <TruncatedText 
                      text={doc.name}
                      className="text-sm text-gray-900 dark:text-gray-100"
                      maxWidth="max-w-[250px]"
                    />
                  </td>
                  <td className="px-6 py-4">
                    {doc.customerName ? (
                      <TruncatedText 
                        text={doc.customerName}
                        className="text-sm text-gray-500 dark:text-gray-400"
                        maxWidth="max-w-[200px]"
                      />
                    ) : (
                      <span className="text-sm text-gray-500 dark:text-gray-400">-</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {doc.equipmentItems?.length || 0}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(doc.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {new Date(doc.createdAt).toLocaleDateString('vi-VN')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end gap-1">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => router.push(`/bidding-documents/${doc.id}`)}
                        title="Xem chi tiết"
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => router.push(`/bidding-documents/${doc.id}/edit`)}
                        title="Chỉnh sửa"
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setDeletingDocumentId(doc.id)
                        }}
                        title="Xóa"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                      
                      {/* Dropdown menu for export options */}
                      {doc.status === BiddingDocumentStatus.COMPLETED && (
                        <div className="relative">
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => setActiveDropdown(activeDropdown === doc.id ? null : doc.id)}
                            title="Xuất báo cáo"
                          >
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                          
                          {activeDropdown === doc.id && (
                            <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg z-10 border border-gray-200 dark:border-gray-700">
                              <div className="py-1">
                                <button
                                  onClick={() => handleExport(doc, 'WORD')}
                                  className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                                >
                                  <Download className="w-4 h-4" />
                                  Xuất Word
                                </button>
                                <button
                                  onClick={() => handleExport(doc, 'PDF')}
                                  className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                                >
                                  <Download className="w-4 h-4" />
                                  Xuất PDF
                                </button>
                                <button
                                  onClick={() => handleExport(doc, 'EXCEL')}
                                  className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                                >
                                  <Download className="w-4 h-4" />
                                  Xuất Excel
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
          <Pagination
            currentPage={pagination.page}
            totalPages={Math.ceil(pagination.total / pagination.limit)}
            onPageChange={pagination.onPageChange}
            totalRecords={pagination.total}
            pageSize={pagination.limit}
            onPageSizeChange={pagination.onPageSizeChange || ((newSize) => console.log('Page size changed to:', newSize))}
          />
        </div>
      </div>

      {/* Delete Dialog */}
      {deletingDocumentId && (
        <DeleteBiddingDocumentDialog
          documentId={deletingDocumentId}
          isOpen={true}
          onClose={() => setDeletingDocumentId(null)}
        />
      )}
    </TooltipProvider>
  )
}