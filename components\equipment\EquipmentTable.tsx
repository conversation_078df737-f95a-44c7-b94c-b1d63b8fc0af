'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Edit, Trash2, Eye, ChevronUp, ChevronDown, Calendar, Hash, Settings, Wrench, Building, DollarSign } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Dialog } from '@/components/ui/Dialog';
import { useDeleteEquipment } from '@/hooks/queries/useEquipment';
import { useAuth } from '@/hooks/queries/useAuth';
import type { Equipment } from '@/types/equipment';

interface EquipmentTableProps {
  equipments: Equipment[];
  isLoading?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  onSort?: (column: 'equipmentCode' | 'name' | 'createdAt') => void;
}

export function EquipmentTable({
  equipments,
  isLoading = false,
  sortBy,
  sortOrder,
  onSort
}: EquipmentTableProps) {
  const router = useRouter();
  const { user } = useAuth();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [equipmentToDelete, setEquipmentToDelete] = useState<Equipment | null>(null);
  
  const deleteEquipmentMutation = useDeleteEquipment();
  
  const canEdit = user?.role === 'ADMIN' || user?.role === 'USER';
  const canDelete = user?.role === 'ADMIN' || user?.role === 'USER';

  const handleView = (equipment: Equipment) => {
    router.push(`/equipment/${equipment.id}`);
  };

  const handleEdit = (equipment: Equipment) => {
    router.push(`/equipment/${equipment.id}/edit`);
  };

  const handleDelete = (equipment: Equipment) => {
    setEquipmentToDelete(equipment);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!equipmentToDelete) return;

    try {
      await deleteEquipmentMutation.mutateAsync(equipmentToDelete.id);
      setDeleteDialogOpen(false);
      setEquipmentToDelete(null);
    } catch (error) {
      console.error('Failed to delete equipment:', error);
    }
  };

  const renderSortIcon = (column: string) => {
    if (sortBy !== column) return null;
    return sortOrder === 'asc' ? (
      <ChevronUp className="w-4 h-4 inline ml-1" />
    ) : (
      <ChevronDown className="w-4 h-4 inline ml-1" />
    );
  };

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="animate-pulse">
          <div className="h-14 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800"></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="border-t border-gray-200 dark:border-gray-700">
              <div className="h-20 bg-gray-50 dark:bg-gray-800 p-6">
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded-md w-3/4"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded-md w-1/2"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (equipments.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="text-center py-16">
          <div className="mx-auto w-20 h-20 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-full flex items-center justify-center mb-6">
            <Wrench className="w-10 h-10 text-blue-500 dark:text-blue-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Chưa có thiết bị nào
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 max-w-sm mx-auto">
            Thêm thiết bị đầu tiên để bắt đầu quản lý tài sản của bạn
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
            <tr>
              <th 
                className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 group"
                onClick={() => onSort?.('equipmentCode')}
              >
                <div className="flex items-center gap-2">
                  <Hash className="w-4 h-4 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-200" />
                  Mã thiết bị
                  {renderSortIcon('equipmentCode')}
                </div>
              </th>
              <th 
                className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 group"
                onClick={() => onSort?.('name')}
              >
                <div className="flex items-center gap-2">
                  <Wrench className="w-4 h-4 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-200" />
                  Tên thiết bị
                  {renderSortIcon('name')}
                </div>
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                <div className="flex items-center gap-2">
                  <Settings className="w-4 h-4 text-gray-400" />
                  Danh mục
                </div>
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                <div className="flex items-center gap-2">
                  <Building className="w-4 h-4 text-gray-400" />
                  Nhà sản xuất
                </div>
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                <div className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4 text-gray-400" />
                  Giá
                </div>
              </th>
              <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                <div className="flex items-center justify-center gap-2">
                  <Settings className="w-4 h-4 text-gray-400" />
                  Trạng thái
                </div>
              </th>
              <th className="px-6 py-4 text-right text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                Thao tác
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-100 dark:divide-gray-700">
            {equipments.map((equipment, index) => (
              <tr 
                key={equipment.id} 
                className={`hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/10 dark:hover:to-indigo-900/10 transition-all duration-200 group ${
                  index % 2 === 0 ? 'bg-white dark:bg-gray-800' : 'bg-gray-50/50 dark:bg-gray-800/50'
                }`}
              >
                <td className="px-6 py-5 whitespace-nowrap">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center text-white font-semibold text-sm">
                      {equipment.equipmentCode.charAt(0)}
                    </div>
                    <div>
                      <div className="text-sm font-semibold text-gray-900 dark:text-gray-100 font-mono">
                        {equipment.equipmentCode}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        ID: {equipment.id.slice(-8)}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-5 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {equipment.name}
                  </div>
                </td>
                <td className="px-6 py-5 whitespace-nowrap">
                  <div className="text-sm text-gray-600 dark:text-gray-300">
                    {equipment.catalog?.catalogName || (
                      <span className="italic text-gray-400 dark:text-gray-500">Chưa phân loại</span>
                    )}
                  </div>
                </td>
                <td className="px-6 py-5 whitespace-nowrap">
                  <div className="text-sm text-gray-600 dark:text-gray-300">
                    {equipment.manufacturer || (
                      <span className="italic text-gray-400 dark:text-gray-500">Chưa có thông tin</span>
                    )}
                  </div>
                </td>
                <td className="px-6 py-5 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {equipment.price ? (
                      <span className="inline-flex items-center gap-1 px-2 py-1 rounded-md bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-300 text-xs font-semibold">
                        <DollarSign className="w-3 h-3" />
                        {new Intl.NumberFormat('vi-VN').format(equipment.price)} VNĐ
                      </span>
                    ) : (
                      <span className="italic text-gray-400 dark:text-gray-500">Chưa có giá</span>
                    )}
                  </div>
                </td>
                <td className="px-6 py-5 whitespace-nowrap text-center">
                  <span className={`inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-semibold rounded-full border ${
                    equipment.status === 'ACTIVE'
                      ? 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200 dark:from-green-900/40 dark:to-emerald-900/40 dark:text-green-200 dark:border-green-700'
                      : 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 border-gray-200 dark:from-gray-800/40 dark:to-gray-700/40 dark:text-gray-300 dark:border-gray-600'
                  }`}>
                    <div className={`w-2 h-2 rounded-full ${
                      equipment.status === 'ACTIVE' ? 'bg-green-500' : 'bg-gray-400'
                    }`} />
                    {equipment.status === 'ACTIVE' ? 'Hoạt động' : 'Không hoạt động'}
                  </span>
                </td>
                <td className="px-6 py-5 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex items-center justify-end gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleView(equipment)}
                      title="Xem chi tiết"
                      className="w-8 h-8 hover:bg-blue-100 hover:text-blue-600 dark:hover:bg-blue-900/20 dark:hover:text-blue-400 transition-colors"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    {canEdit && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEdit(equipment)}
                        title="Chỉnh sửa"
                        className="w-8 h-8 hover:bg-amber-100 hover:text-amber-600 dark:hover:bg-amber-900/20 dark:hover:text-amber-400 transition-colors"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    )}
                    {canDelete && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDelete(equipment)}
                        title="Xóa"
                        className="w-8 h-8 hover:bg-red-100 hover:text-red-600 dark:hover:bg-red-900/20 dark:hover:text-red-400 transition-colors"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
          </table>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        title="Xác nhận xóa"
        description={`Bạn có chắc chắn muốn xóa thiết bị "${equipmentToDelete?.name}"? Hành động này không thể hoàn tác.`}
      >
        <div className="flex justify-end gap-3 mt-4">
          <Button
            variant="outline"
            onClick={() => setDeleteDialogOpen(false)}
          >
            Hủy
          </Button>
          <Button
            variant="primary"
            onClick={confirmDelete}
            disabled={deleteEquipmentMutation.isPending}
          >
            {deleteEquipmentMutation.isPending ? 'Đang xóa...' : 'Xóa'}
          </Button>
        </div>
      </Dialog>
    </>
  );
}