import { NextRequest, NextResponse } from 'next/server';
import { googleDriveServiceNoLogin } from '@/services/googleDriveServiceNoLogin';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if Google Drive is configured
    const isConfigured = await googleDriveServiceNoLogin.isAuthenticated();
    console.log('isConfigured', isConfigured);
    if (!isConfigured) {
      return NextResponse.json(
        { error: 'Google Drive not configured' },
        { status: 503 }
      );
    }

    const { id } = await params;
    const file = await googleDriveServiceNoLogin.getFile(id);
    
    return NextResponse.json({
      success: true,
      ...file
    });
  } catch (error) {
    console.error('Get file error:', error);
    return NextResponse.json(
      { error: 'Failed to get file' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if Google Drive is configured
    const isConfigured = await googleDriveServiceNoLogin.isAuthenticated();
    console.log('isConfigured', isConfigured);
    if (!isConfigured) {
      return NextResponse.json(
        { error: 'Google Drive not configured' },
        { status: 503 }
      );
    }

    const { id } = await params;
    await googleDriveServiceNoLogin.deleteFile(id);
    
    return NextResponse.json({
      success: true,
      message: 'File deleted successfully'
    });
  } catch (error) {
    console.error('Delete file error:', error);
    return NextResponse.json(
      { error: 'Failed to delete file' },
      { status: 500 }
    );
  }
}