import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { medicalRecordService } from '@/services/medicalRecordService'
import { useToast } from '@/hooks/useToast'
import type { 
  MedicalRecordFilter,
  CreateMedicalRecordRequest,
  UpdateMedicalRecordRequest 
} from '@/types/medicalRecord'

const medicalRecordKeys = {
  all: ['medical-records'] as const,
  lists: () => [...medicalRecordKeys.all, 'list'] as const,
  list: (filter: MedicalRecordFilter) => [...medicalRecordKeys.lists(), filter] as const,
  details: () => [...medicalRecordKeys.all, 'detail'] as const,
  detail: (id: string) => [...medicalRecordKeys.details(), id] as const,
}

// Get medical records list
export function useMedicalRecords(filter?: MedicalRecordFilter) {
  return useQuery({
    queryKey: medicalRecordKeys.list(filter || {}),
    queryFn: ({ signal }) => medicalRecordService.getMedicalRecords(filter),
  })
}

// Get medical record by ID
export function useMedicalRecord(id: string, enabled = true) {
  return useQuery({
    queryKey: medicalRecordKeys.detail(id),
    queryFn: ({ signal }) => medicalRecordService.getMedicalRecordById(id),
    enabled: enabled && !!id,
  })
}

// Create medical record
export function useCreateMedicalRecord() {
  const queryClient = useQueryClient()
  const toast = useToast()

  return useMutation({
    mutationFn: (data: CreateMedicalRecordRequest) => 
      medicalRecordService.createMedicalRecord(data),
    onSuccess: (newRecord) => {
      queryClient.invalidateQueries({ queryKey: medicalRecordKeys.lists() })
      toast.success('Tạo hồ sơ y tế mới thành công')
      return newRecord
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Không thể tạo hồ sơ y tế')
    },
  })
}

// Update medical record
export function useUpdateMedicalRecord() {
  const queryClient = useQueryClient()
  const toast = useToast()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateMedicalRecordRequest }) =>
      medicalRecordService.updateMedicalRecord(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: medicalRecordKeys.detail(variables.id) })
      queryClient.invalidateQueries({ queryKey: medicalRecordKeys.lists() })
      toast.success('Cập nhật hồ sơ y tế thành công')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Không thể cập nhật hồ sơ y tế')
    },
  })
}

// Delete medical record
export function useDeleteMedicalRecord() {
  const queryClient = useQueryClient()
  const toast = useToast()

  return useMutation({
    mutationFn: (id: string) => medicalRecordService.deleteMedicalRecord(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: medicalRecordKeys.lists() })
      toast.success('Xóa hồ sơ y tế thành công')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Không thể xóa hồ sơ y tế')
    },
  })
}

// Upload attachment
export function useUploadMedicalAttachment() {
  const queryClient = useQueryClient()
  const toast = useToast()

  return useMutation({
    mutationFn: ({ 
      medicalRecordId, 
      file 
    }: { 
      medicalRecordId: string
      file: File
    }) => medicalRecordService.uploadAttachment(medicalRecordId, file),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: medicalRecordKeys.detail(variables.medicalRecordId) 
      })
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Không thể tải lên tài liệu')
    },
  })
}

// Delete attachment
export function useDeleteMedicalAttachment() {
  const queryClient = useQueryClient()
  const toast = useToast()

  return useMutation({
    mutationFn: ({ 
      medicalRecordId, 
      attachmentId 
    }: { 
      medicalRecordId: string
      attachmentId: string 
    }) => medicalRecordService.deleteAttachment(medicalRecordId, attachmentId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: medicalRecordKeys.detail(variables.medicalRecordId) 
      })
      toast.success('Xóa tài liệu thành công')
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Không thể xóa tài liệu')
    },
  })
}

// Save attachments
export function useSaveMedicalAttachments() {
  const queryClient = useQueryClient()
  const toast = useToast()

  return useMutation({
    mutationFn: ({ 
      medicalRecordId, 
      attachments 
    }: { 
      medicalRecordId: string
      attachments: any[]
    }) => medicalRecordService.saveAttachments(medicalRecordId, attachments),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ 
        queryKey: medicalRecordKeys.detail(variables.medicalRecordId) 
      })
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Không thể lưu tài liệu')
    },
  })
}

// Check patient ID exists
export function useCheckPatientIdExists() {
  const toast = useToast()

  return useMutation({
    mutationFn: (patientId: string) => 
      medicalRecordService.checkPatientIdExists(patientId),
    onError: (error: Error) => {
      toast.error(error.message || 'Không thể kiểm tra mã bệnh nhân')
    },
  })
}