generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String              @id @default(cuid())
  username          String              @unique
  email             String              @unique
  passwordHash      String
  name              String
  role              Role                @default(USER)
  department        String?
  phone             String?
  avatar            String?
  status            Status              @default(ACTIVE)
  emailVerified     Boolean             @default(false)
  twoFactorEnabled  Boolean             @default(false)
  lastLoginAt       DateTime?
  loginAttempts     Int                 @default(0)
  lockedUntil       DateTime?
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  auditLogs         AuditLog[]
  createdCatalogs   Catalog[]           @relation("CatalogCreator")
  createdEquipments Equipment[]         @relation("EquipmentCreator")
  sessions          Session[]
  uploadedEquipmentDocs EquipmentDocument[] @relation("EquipmentDocumentUploader")
  uploadedDocuments Document[]          @relation("DocumentUploader")
  createdBiddingDocuments BiddingDocument[] @relation("BiddingDocumentCreator")

  @@index([username])
  @@index([email])
}

model Session {
  id           String        @id @default(cuid())
  userId       String
  token        String        @unique
  refreshToken String        @unique
  ipAddress    String?
  userAgent    String?
  status       SessionStatus @default(ACTIVE)
  expiresAt    DateTime
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @default(now())
  loggedOutAt  DateTime?
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([token])
  @@index([refreshToken])
  @@index([userId])
  @@index([status])
}

model AuditLog {
  id        String   @id @default(cuid())
  userId    String
  action    String
  details   Json?
  ipAddress String?
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([action])
  @@index([createdAt])
}

model Catalog {
  id          String      @id @default(cuid())
  catalogCode String      @unique
  catalogName String
  description String?
  status      Status      @default(ACTIVE)
  createdBy   String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  creator     User?       @relation("CatalogCreator", fields: [createdBy], references: [id])
  equipments  Equipment[]

  @@index([catalogCode])
  @@index([catalogName])
  @@index([status])
}

model Equipment {
  id             String              @id @default(cuid())
  equipmentCode  String              @unique
  name           String
  description    String?
  model          String?
  manufacturer   String?
  unit           String?
  price          Float?
  specifications Json?
  catalogId      String
  status         Status              @default(ACTIVE)
  createdBy      String?
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt
  catalog        Catalog             @relation(fields: [catalogId], references: [id])
  creator        User?               @relation("EquipmentCreator", fields: [createdBy], references: [id])
  documents      EquipmentDocument[]

  @@index([equipmentCode])
  @@index([name])
  @@index([catalogId])
  @@index([status])
}

model EquipmentDocument {
  id          String    @id @default(cuid())
  equipmentId String
  fileName    String
  fileUrl     String
  fileSize    Int
  mimeType    String
  uploadedAt  DateTime  @default(now())
  uploadedBy  String?
  
  // Google Drive fields
  googleDriveId       String?
  googleDriveUrl      String?
  googleDriveEmbedUrl String?
  googleDriveIconUrl  String?

  equipment   Equipment @relation(fields: [equipmentId], references: [id], onDelete: Cascade)
  uploader    User?     @relation("EquipmentDocumentUploader", fields: [uploadedBy], references: [id])

  @@index([equipmentId])
}

model Document {
  id           String   @id @default(cuid())
  fileName     String
  fileUrl      String
  fileSize     Int
  documentType String   // catalog, datasheet, image, iso, license, financial, other
  tags         String[]
  description  String?
  uploadedBy   String?
  uploadedAt   DateTime @default(now())
  
  uploader     User?    @relation("DocumentUploader", fields: [uploadedBy], references: [id])

  @@index([fileName])
  @@index([documentType])
  @@index([uploadedAt])
}

enum Role {
  ADMIN
  USER
}

enum Status {
  ACTIVE
  INACTIVE
}

enum SessionStatus {
  ACTIVE
  EXPIRED
  LOGGED_OUT
}

model BiddingDocument {
  id           String                  @id @default(cuid())
  code         String                  @unique
  name         String
  description  String?
  customerName String?
  status       BiddingDocumentStatus   @default(PENDING)
  equipmentItems Json?
  createdBy    String
  createdAt    DateTime                @default(now())
  updatedAt    DateTime                @updatedAt
  creator      User                    @relation("BiddingDocumentCreator", fields: [createdBy], references: [id])
  attachments  BiddingDocumentAttachment[]

  @@index([code])
  @@index([status])
  @@index([createdBy])
}

model BiddingDocumentAttachment {
  id               String          @id @default(cuid())
  biddingDocumentId String
  fileName         String
  fileUrl          String
  fileSize         Int
  mimeType         String
  source           String          @default("local") // "local" or "google_drive"
  googleDriveId    String?
  googleDriveUrl   String?
  uploadedBy       String
  uploadedAt       DateTime        @default(now())
  
  biddingDocument  BiddingDocument @relation(fields: [biddingDocumentId], references: [id], onDelete: Cascade)

  @@index([biddingDocumentId])
}

enum BiddingDocumentStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
}
