import type { User } from '@/types/auth'

/**
 * Utility functions for authentication cache management
 * Handles localStorage operations safely with error handling
 */
export const authUtils = {
  /**
   * Get cached user data from localStorage
   * Returns null if no cache or if parsing fails
   */
  getCachedUser: (): User | null => {
    if (typeof window === 'undefined') return null
    
    try {
      const cached = localStorage.getItem('cached_user')
      if (!cached) return null
      
      const parsed = JSON.parse(cached)
      
      // Validate that it has required User properties
      if (parsed && typeof parsed === 'object' && parsed.id && parsed.username) {
        return parsed as User
      }
      
      // Invalid cached data, remove it
      localStorage.removeItem('cached_user')
      return null
    } catch (error) {
      console.error('Failed to parse cached user:', error)
      localStorage.removeItem('cached_user')
      return null
    }
  },

  /**
   * Cache user data to localStorage
   * Handles quota exceeded and other storage errors gracefully
   */
  setCachedUser: (user: User): void => {
    if (typeof window === 'undefined') return
    
    try {
      localStorage.setItem('cached_user', JSON.stringify(user))
    } catch (error) {
      console.error('Failed to cache user:', error)
      
      // Handle quota exceeded by clearing old cache
      if (error instanceof DOMException && (
        error.code === 22 || // QuotaExceededError
        error.name === 'QuotaExceededError' ||
        error.name === 'NS_ERROR_DOM_QUOTA_REACHED'
      )) {
        console.warn('localStorage quota exceeded, clearing cache')
        try {
          localStorage.removeItem('cached_user')
          localStorage.setItem('cached_user', JSON.stringify(user))
        } catch (retryError) {
          console.error('Failed to cache user after clearing:', retryError)
        }
      }
    }
  },

  /**
   * Clear cached user data from localStorage
   */
  clearCachedUser: (): void => {
    if (typeof window === 'undefined') return
    
    try {
      localStorage.removeItem('cached_user')
    } catch (error) {
      console.error('Failed to clear cached user:', error)
    }
  },

  /**
   * Check if user data has meaningful changes
   * Used to avoid unnecessary re-renders
   */
  hasUserChanged: (oldUser: User | null, newUser: User | null): boolean => {
    if (!oldUser && !newUser) return false
    if (!oldUser || !newUser) return true
    
    // Compare key properties that affect UI
    return (
      oldUser.id !== newUser.id ||
      oldUser.name !== newUser.name ||
      oldUser.role !== newUser.role ||
      oldUser.avatar !== newUser.avatar ||
      oldUser.status !== newUser.status
    )
  },

  /**
   * Get user display name with fallbacks
   */
  getUserDisplayName: (user: User | null): string => {
    if (!user) return 'Loading...'
    return user.name || user.username || 'Unknown User'
  },

  /**
   * Get user avatar initial
   */
  getUserInitial: (user: User | null): string => {
    if (!user) return 'U'
    const name = user.name || user.username || 'User'
    return name.charAt(0).toUpperCase()
  }
}

/**
 * Hook for managing optimistic auth state
 * Provides immediate UI updates with cached data while verifying with server
 */
export function useOptimisticUser() {
  const cachedUser = authUtils.getCachedUser()
  
  return {
    hasCache: !!cachedUser,
    cachedUser,
    shouldShowOptimisticUI: !!cachedUser
  }
}