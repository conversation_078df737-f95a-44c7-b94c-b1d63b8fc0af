import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { profileService } from '@/services/profileService'
import { useToast } from '@/hooks/useToast'
import type { UpdateProfileInput, ChangePasswordInput } from '@/services/profileService'

export function useProfile() {
  return useQuery({
    queryKey: ['profile'],
    queryFn: ({ signal }) => profileService.getProfile(signal),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useUpdateProfile() {
  const queryClient = useQueryClient()
  const toast = useToast()

  return useMutation({
    mutationFn: (data: UpdateProfileInput) => profileService.updateProfile(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['profile'] })
      toast.success('Thông tin cá nhân đã được cập nhật')
    },
    onError: (error) => {
      console.error('Update profile error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Không thể cập nhật thông tin'
      toast.error(errorMessage)
    },
  })
}

export function useChangePassword() {
  const toast = useToast()

  return useMutation({
    mutationFn: (data: ChangePasswordInput) => profileService.changePassword(data),
    onSuccess: (response) => {
      toast.success(response.message || 'Mật khẩu đã được thay đổi thành công')
    },
    onError: (error) => {
      console.error('Change password error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Không thể đổi mật khẩu'
      toast.error(errorMessage)
    },
  })
}