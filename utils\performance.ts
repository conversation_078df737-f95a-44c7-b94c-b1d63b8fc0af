/**
 * Performance utilities cho auth optimization
 */

// Debounce function để tránh multiple API calls
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    
    timeoutId = setTimeout(() => {
      func(...args)
    }, delay)
  }
}

// Throttle function để limit API calls
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let inThrottle = false
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => {
        inThrottle = false
      }, delay)
    }
  }
}

// Cache with TTL
export class TTLCache<T> {
  private cache = new Map<string, { value: T; expiry: number }>()
  
  constructor(private defaultTTL: number = 5 * 60 * 1000) {} // 5 minutes
  
  set(key: string, value: T, ttl?: number): void {
    const expiry = Date.now() + (ttl ?? this.defaultTTL)
    this.cache.set(key, { value, expiry })
  }
  
  get(key: string): T | null {
    const item = this.cache.get(key)
    
    if (!item) return null
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key)
      return null
    }
    
    return item.value
  }
  
  has(key: string): boolean {
    return this.get(key) !== null
  }
  
  clear(): void {
    this.cache.clear()
  }
  
  delete(key: string): boolean {
    return this.cache.delete(key)
  }
}

// Memory cache cho auth data
export const authCache = new TTLCache<any>(10 * 60 * 1000) // 10 minutes

// Performance monitoring
export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics = new Map<string, number[]>()
  
  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }
  
  startTiming(label: string): () => number {
    const start = performance.now()
    
    return () => {
      const duration = performance.now() - start
      this.recordMetric(label, duration)
      return duration
    }
  }
  
  recordMetric(label: string, value: number): void {
    if (!this.metrics.has(label)) {
      this.metrics.set(label, [])
    }
    
    const values = this.metrics.get(label)!
    values.push(value)
    
    // Keep only last 100 measurements
    if (values.length > 100) {
      values.shift()
    }
  }
  
  getAverageMetric(label: string): number {
    const values = this.metrics.get(label)
    if (!values || values.length === 0) return 0
    
    return values.reduce((sum, val) => sum + val, 0) / values.length
  }
  
  getAllMetrics(): Record<string, { average: number; count: number; latest: number }> {
    const result: Record<string, { average: number; count: number; latest: number }> = {}
    
    for (const [label, values] of this.metrics.entries()) {
      if (values.length > 0) {
        result[label] = {
          average: values.reduce((sum, val) => sum + val, 0) / values.length,
          count: values.length,
          latest: values[values.length - 1]
        }
      }
    }
    
    return result
  }
  
  logMetrics(): void {
    const metrics = this.getAllMetrics()
    console.table(metrics)
  }
}

// Global performance monitor instance
export const perfMonitor = PerformanceMonitor.getInstance()

// Optimization flags
export const OPTIMIZATION_FLAGS = {
  // Cache user data trong localStorage
  CACHE_USER_DATA: true,
  
  // Debounce auth checks
  DEBOUNCE_AUTH_CHECKS: true,
  
  // Use optimistic updates
  OPTIMISTIC_UPDATES: true,
  
  // Prefetch user data
  PREFETCH_DATA: true,
  
  // Enable performance monitoring
  PERFORMANCE_MONITORING: process.env.NODE_ENV === 'development',
  
  // Auto refresh interval (ms)
  AUTO_REFRESH_INTERVAL: 5 * 60 * 1000, // 5 minutes
  
  // Cache TTL (ms)
  CACHE_TTL: 10 * 60 * 1000, // 10 minutes
  
  // Debounce delay (ms)
  DEBOUNCE_DELAY: 300,
  
  // Throttle delay (ms) 
  THROTTLE_DELAY: 1000
} as const

// Helper để wrap async functions với performance monitoring
export function withPerformanceMonitoring<T extends (...args: any[]) => Promise<any>>(
  func: T,
  label: string
): T {
  return (async (...args: Parameters<T>) => {
    if (!OPTIMIZATION_FLAGS.PERFORMANCE_MONITORING) {
      return func(...args)
    }
    
    const endTiming = perfMonitor.startTiming(label)
    try {
      const result = await func(...args)
      const duration = endTiming()
      
      if (duration > 1000) { // Log slow operations
        console.warn(`Slow operation detected: ${label} took ${duration.toFixed(2)}ms`)
      }
      
      return result
    } catch (error) {
      endTiming()
      throw error
    }
  }) as T
}