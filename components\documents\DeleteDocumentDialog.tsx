'use client';

import { AlertTriangle } from 'lucide-react';
import { Dialog } from '@/components/ui/Dialog';
import { useDeleteDocument } from '@/hooks/queries/useDocuments';
import type { Document } from '@/types/document';

interface DeleteDocumentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  document: Document;
}

export function DeleteDocumentDialog({ isOpen, onClose, document }: DeleteDocumentDialogProps) {
  const deleteMutation = useDeleteDocument();

  const handleDelete = async () => {
    try {
      await deleteMutation.mutateAsync(document.id);
      onClose();
    } catch (error) {
      console.error('Delete error:', error);
    }
  };

  return (
    <Dialog isOpen={isOpen} onClose={onClose}>
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full">
        <div className="flex items-center gap-3 mb-4">
          <div className="flex-shrink-0 w-10 h-10 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center">
            <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Delete Document
          </h3>
        </div>

        <div className="mb-6">
          <p className="text-gray-600 dark:text-gray-400">
            Are you sure you want to delete this document?
          </p>
          <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <p className="font-medium text-gray-900 dark:text-gray-100">
              {document.fileName}
            </p>
            {document.description && (
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {document.description}
              </p>
            )}
          </div>
          <p className="text-sm text-red-600 dark:text-red-400 mt-3">
            This action cannot be undone.
          </p>
        </div>

        <div className="flex justify-end gap-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleDelete}
            disabled={deleteMutation.isPending}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {deleteMutation.isPending ? 'Deleting...' : 'Delete'}
          </button>
        </div>
      </div>
    </Dialog>
  );
}