# AI Profile Generation - Deployment Troubleshooting Guide

## Overview

This guide helps diagnose and fix issues with the AI Profile Generation feature when deployed to production servers.

## Common Deployment Issues

### 1. File System Access Issues

**Symptoms:**
- Error: "Không thể truy cập tệp" (Cannot access file)
- Error codes: ENOENT, EACCES

**Root Causes:**
- Upload directories don't exist on production server
- File permissions are incorrect
- Files uploaded locally aren't present on server

**Solutions:**
```bash
# Ensure upload directories exist
mkdir -p uploads/bidding-documents/
mkdir -p public/uploads/bidding-documents/

# Set correct permissions
chmod 755 uploads/
chmod 755 public/uploads/
chmod 644 uploads/bidding-documents/*
```

**Environment Variables:**
```env
# Optional: Set custom file size limit (default 50MB)
MAX_PDF_SIZE=52428800
```

### 2. AI API Connection Issues

**Symptoms:**
- Error: "Lỗi kết nối AI API" (AI API connection error)
- Error: "AI API bị timeout" (AI API timeout)
- Status codes: 502, 504

**Root Causes:**
- Server firewall blocks outbound connections
- AI API endpoint is unreachable from production
- Network timeout issues

**Solutions:**
```env
# Configure AI API settings
AI_API_URL=https://dev.vietmy.lifesup.ai/service/query/query-from-pdf
AI_API_TIMEOUT=120000
```

**Server Configuration:**
```bash
# Test AI API connectivity from server
curl -v https://dev.vietmy.lifesup.ai/service/query/query-from-pdf

# Check firewall rules
sudo ufw status
sudo iptables -L OUTPUT
```

### 3. Memory and Performance Issues

**Symptoms:**
- Process killed unexpectedly
- Out of memory errors
- Slow response times

**Root Causes:**
- Large PDF files consume too much memory
- Server has insufficient memory
- No memory cleanup after processing

**Solutions:**
```env
# Reduce maximum file size if needed
MAX_PDF_SIZE=25600000  # 25MB instead of 50MB
```

**Server Monitoring:**
```bash
# Monitor memory usage
htop
free -h

# Monitor disk space
df -h

# Check server logs
tail -f /var/log/nginx/error.log
pm2 logs your-app-name
```

## Error Codes and Meanings

### HTTP Status Codes
- `400` - Bad Request (invalid file type, missing data)
- `401` - Unauthorized (authentication failed)
- `404` - Not Found (attachment not found in database)
- `500` - Internal Server Error (file access, parsing issues)
- `502` - Bad Gateway (AI API connection failed)
- `504` - Gateway Timeout (AI API timeout)

### File System Error Codes
- `ENOENT` - File or directory not found
- `EACCES` - Permission denied
- `EMFILE` - Too many open files
- `ENFILE` - System limit on number of open files

## Deployment Checklist

### Before Deployment
- [ ] Upload directories exist with correct permissions
- [ ] Environment variables configured
- [ ] AI API endpoint accessible from server
- [ ] SSL certificates valid for external API calls

### After Deployment
- [ ] Test file upload functionality
- [ ] Verify AI API connectivity
- [ ] Check server logs for errors
- [ ] Monitor memory usage
- [ ] Test with actual PDF files

## Monitoring and Logging

### Log Locations
The enhanced API provides structured logging with timestamps:

```bash
# Search for AI profile logs
grep "AI_PROFILE" /var/log/your-app.log

# Monitor real-time logs
tail -f /var/log/your-app.log | grep "AI_PROFILE"
```

### Key Log Messages

**Success Flow:**
```
[timestamp] AI_PROFILE_INFO [REQUEST_START]: AI profile creation request started
[timestamp] AI_PROFILE_INFO [AUTH_SUCCESS]: User authenticated: user-id
[timestamp] AI_PROFILE_INFO [FILE_ACCESS]: File accessibility verified
[timestamp] AI_PROFILE_INFO [AI_API_CALL_START]: Calling AI API
[timestamp] AI_PROFILE_INFO [AI_API_SUCCESS]: AI API response parsed successfully
[timestamp] AI_PROFILE_INFO [REQUEST_SUCCESS]: AI profile generation completed successfully
```

**Error Indicators:**
```
[timestamp] AI_PROFILE_ERROR [FILE_ACCESS_FAILED]: ENOENT error
[timestamp] AI_PROFILE_ERROR [AI_API_TIMEOUT]: AI API call timed out
[timestamp] AI_PROFILE_ERROR [AI_API_NETWORK_ERROR]: Network connection failed
```

## Production Environment Variables

### Required Variables
```env
# Database connection
DATABASE_URL=postgresql://...

# AI API configuration
AI_API_URL=https://dev.vietmy.lifesup.ai/service/query/query-from-pdf
AI_API_TIMEOUT=120000
MAX_PDF_SIZE=52428800

# Application URLs
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXT_PUBLIC_API_URL=https://your-domain.com/api
```

### Optional Variables
```env
# Increase timeout for slow networks
AI_API_TIMEOUT=180000

# Reduce file size limit for memory-constrained servers
MAX_PDF_SIZE=26214400  # 25MB
```

## Common Fix Commands

### Reset File Permissions
```bash
# Fix upload directory permissions
find uploads/ -type d -exec chmod 755 {} \;
find uploads/ -type f -exec chmod 644 {} \;
```

### Test AI API Connectivity
```bash
# Test from server command line
curl -X POST \
  -H "accept: application/json" \
  -F "file=@test.pdf" \
  -F "topic=test equipment" \
  https://dev.vietmy.lifesup.ai/service/query/query-from-pdf
```

### Clean Up Resources
```bash
# Remove old temp files
find /tmp -name "*.pdf" -mtime +1 -delete

# Restart application if memory issues
pm2 restart your-app-name
```

## Support Information

When reporting issues, include:
1. Complete error logs with timestamps
2. Server specifications (RAM, CPU, disk space)
3. Network configuration details
4. Environment variables (redacted)
5. File sizes and types being processed

## Version History

- v1.0 - Initial troubleshooting guide
- v1.1 - Added comprehensive logging and error handling