import { cookies } from 'next/headers'
import { NextRequest } from 'next/server'
import { prisma } from '@/lib/db'
import { verifyAccessToken } from '@/lib/auth'

export async function getServerSession(request?: NextRequest) {
  try {
    let token: string | undefined
    
    // If request is provided, get token from Authorization header
    if (request) {
      const authorization = request.headers.get('authorization')
      if (authorization?.startsWith('Bearer ')) {
        token = authorization.substring(7)
      }
    }
    
    // Get session ID from cookie
    const cookieStore = await cookies()
    const sessionId = cookieStore.get('session-id')

    if (!token || !sessionId) {
      return null
    }

    // Verify token
    const payload = await verifyAccessToken(token)
    if (!payload) {
      return null
    }

    // Get session from database (only active ones)
    const session = await prisma.session.findFirst({
      where: { 
        id: sessionId.value,
        status: 'ACTIVE',
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            email: true,
            name: true,
            role: true,
            department: true,
            status: true,
          }
        }
      }
    })

    if (!session || session.expiresAt < new Date()) {
      // Mark expired session if found
      if (session && session.expiresAt < new Date()) {
        await prisma.session.update({
          where: { id: session.id },
          data: { 
            status: 'EXPIRED',
            updatedAt: new Date(),
          },
        })
      }
      return null
    }

    return {
      user: session.user,
      userId: session.user.id,
      role: session.user.role,
      sessionId: session.id,
    }
  } catch (error) {
    console.error('getServerSession error:', error)
    return null
  }
}

// Alias for backward compatibility
export const verifySession = getServerSession