# Build stage
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY prisma ./prisma/

# Install dependencies
RUN npm ci

# Copy app files
COPY . .

# Generate Prisma client
RUN npm run db:generate

# Build the app
RUN npm run build

# Production stage
FROM node:18-alpine AS runner

WORKDIR /app

# Create a non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Copy necessary files from builder
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/scripts ./scripts

# Create upload directories with proper permissions
RUN mkdir -p uploads/bidding-documents \
    uploads/equipment \
    uploads/documents \
    public/uploads/bidding-documents \
    public/uploads/equipment \
    public/uploads/documents \
    && chown -R nextjs:nodejs uploads \
    && chown -R nextjs:nodejs public/uploads \
    && chmod -R 755 uploads \
    && chmod -R 755 public/uploads

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 3002

# Start the app
CMD ["npm", "start"]