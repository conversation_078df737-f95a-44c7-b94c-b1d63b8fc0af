import { CatalogService, catalogService } from '@/services/catalogService'
import {
  createMockCatalog,
  createMockCatalogsResponse,
  createMockCreateCatalogRequest,
  createMockUpdateCatalogRequest,
  createMockCheckCodeResponse,
  createMockEquipment,
  createMockEquipmentsResponse,
  createSuccessResponse,
  createErrorResponse,
  mockFetchImplementation
} from '../../utils/catalog-test-utils'

// Mock fetch globally
global.fetch = jest.fn()

describe('CatalogService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = CatalogService.getInstance()
      const instance2 = CatalogService.getInstance()
      expect(instance1).toBe(instance2)
      expect(instance1).toBe(catalogService)
    })
  })

  describe('Catalog Methods', () => {
    describe('getCatalogs', () => {
      it('should fetch catalogs without filters', async () => {
        const mockResponse = createMockCatalogsResponse()
        ;(global.fetch as jest.Mock).mockResolvedValueOnce(
          createSuccessResponse(mockResponse)
        )

        const result = await catalogService.getCatalogs()

        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/catalogs'),
          expect.objectContaining({
            method: 'GET',
            headers: expect.objectContaining({
              'Content-Type': 'application/json'
            })
          })
        )
        expect(result).toEqual(mockResponse)
      })

      it('should fetch catalogs with filters', async () => {
        const mockResponse = createMockCatalogsResponse()
        ;(global.fetch as jest.Mock).mockResolvedValueOnce(
          createSuccessResponse(mockResponse)
        )

        const filters = {
          search: 'thiết bị',
          status: 'ACTIVE' as const,
          page: 2,
          limit: 20,
          sortBy: 'catalogName' as const,
          sortOrder: 'asc' as const
        }

        const result = await catalogService.getCatalogs(filters)

        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/catalogs?search=thi%E1%BA%BFt+b%E1%BB%8B&status=ACTIVE&page=2&limit=20&sortBy=catalogName&sortOrder=asc'),
          expect.any(Object)
        )
        expect(result).toEqual(mockResponse)
      })

      it('should handle fetch errors', async () => {
        ;(global.fetch as jest.Mock).mockResolvedValueOnce(
          createErrorResponse(500, 'Internal Server Error')
        )

        await expect(catalogService.getCatalogs()).rejects.toThrow()
      })

      it('should support abort signal', async () => {
        const mockResponse = createMockCatalogsResponse()
        ;(global.fetch as jest.Mock).mockResolvedValueOnce(
          createSuccessResponse(mockResponse)
        )

        const controller = new AbortController()
        await catalogService.getCatalogs({}, controller.signal)

        expect(global.fetch).toHaveBeenCalledWith(
          expect.any(String),
          expect.objectContaining({
            signal: controller.signal
          })
        )
      })
    })

    describe('getCatalogById', () => {
      it('should fetch catalog by id', async () => {
        const mockCatalog = createMockCatalog()
        ;(global.fetch as jest.Mock).mockResolvedValueOnce(
          createSuccessResponse(mockCatalog)
        )

        const result = await catalogService.getCatalogById('catalog-1')

        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/catalogs/catalog-1'),
          expect.any(Object)
        )
        expect(result).toEqual(mockCatalog)
      })

      it('should handle 404 error', async () => {
        ;(global.fetch as jest.Mock).mockResolvedValueOnce(
          createErrorResponse(404, 'Catalog not found')
        )

        await expect(catalogService.getCatalogById('invalid-id')).rejects.toThrow()
      })
    })

    describe('createCatalog', () => {
      it('should create a new catalog', async () => {
        const request = createMockCreateCatalogRequest()
        const mockCatalog = createMockCatalog(request)
        ;(global.fetch as jest.Mock).mockResolvedValueOnce(
          createSuccessResponse(mockCatalog)
        )

        const result = await catalogService.createCatalog(request)

        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/catalogs'),
          expect.objectContaining({
            method: 'POST',
            body: JSON.stringify(request)
          })
        )
        expect(result).toEqual(mockCatalog)
      })

      it('should handle validation errors', async () => {
        ;(global.fetch as jest.Mock).mockResolvedValueOnce(
          createErrorResponse(400, 'Validation failed')
        )

        const request = createMockCreateCatalogRequest({ catalogCode: '' })
        await expect(catalogService.createCatalog(request)).rejects.toThrow()
      })
    })

    describe('updateCatalog', () => {
      it('should update an existing catalog', async () => {
        const request = createMockUpdateCatalogRequest()
        const mockCatalog = createMockCatalog({ ...request, id: 'catalog-1' })
        ;(global.fetch as jest.Mock).mockResolvedValueOnce(
          createSuccessResponse(mockCatalog)
        )

        const result = await catalogService.updateCatalog('catalog-1', request)

        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/catalogs/catalog-1'),
          expect.objectContaining({
            method: 'PUT',
            body: JSON.stringify(request)
          })
        )
        expect(result).toEqual(mockCatalog)
      })

      it('should handle not found error', async () => {
        ;(global.fetch as jest.Mock).mockResolvedValueOnce(
          createErrorResponse(404, 'Catalog not found')
        )

        const request = createMockUpdateCatalogRequest()
        await expect(catalogService.updateCatalog('invalid-id', request)).rejects.toThrow()
      })
    })

    describe('deleteCatalog', () => {
      it('should delete a catalog without force', async () => {
        ;(global.fetch as jest.Mock).mockResolvedValueOnce(
          createSuccessResponse({ message: 'Catalog deleted successfully' })
        )

        await catalogService.deleteCatalog('catalog-1')

        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/catalogs/catalog-1'),
          expect.objectContaining({
            method: 'DELETE'
          })
        )
      })

      it('should delete a catalog with force', async () => {
        ;(global.fetch as jest.Mock).mockResolvedValueOnce(
          createSuccessResponse({ message: 'Catalog deactivated successfully' })
        )

        await catalogService.deleteCatalog('catalog-1', true)

        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/catalogs/catalog-1?force=true'),
          expect.objectContaining({
            method: 'DELETE'
          })
        )
      })

      it('should handle delete conflicts', async () => {
        ;(global.fetch as jest.Mock).mockResolvedValueOnce(
          createErrorResponse(409, 'Cannot delete catalog with equipment')
        )

        await expect(catalogService.deleteCatalog('catalog-1')).rejects.toThrow()
      })
    })

    describe('checkCatalogCode', () => {
      it('should check if catalog code exists', async () => {
        const mockResponse = createMockCheckCodeResponse({ exists: false })
        ;(global.fetch as jest.Mock).mockResolvedValueOnce(
          createSuccessResponse(mockResponse)
        )

        const result = await catalogService.checkCatalogCode('CAT999')

        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/catalogs/check-code'),
          expect.objectContaining({
            method: 'POST',
            body: JSON.stringify({ code: 'CAT999' })
          })
        )
        expect(result).toEqual(mockResponse)
      })

      it('should return existing catalog when code exists', async () => {
        const mockCatalog = createMockCatalog()
        const mockResponse = createMockCheckCodeResponse({ 
          exists: true, 
          catalog: mockCatalog 
        })
        ;(global.fetch as jest.Mock).mockResolvedValueOnce(
          createSuccessResponse(mockResponse)
        )

        const result = await catalogService.checkCatalogCode('CAT001')

        expect(result.exists).toBe(true)
        expect(result.catalog).toEqual(mockCatalog)
      })
    })

    describe('getCatalogEquipments', () => {
      it('should fetch equipments for a catalog', async () => {
        const mockEquipments = [
          createMockEquipment({ id: 'eq-1' }),
          createMockEquipment({ id: 'eq-2' })
        ]
        const mockCatalog = createMockCatalog({ 
          equipments: mockEquipments 
        } as any)
        
        ;(global.fetch as jest.Mock).mockResolvedValueOnce(
          createSuccessResponse(mockCatalog)
        )

        const result = await catalogService.getCatalogEquipments('catalog-1')

        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/catalogs/catalog-1'),
          expect.any(Object)
        )
        expect(result).toEqual(mockEquipments)
      })

      it('should return empty array if no equipments', async () => {
        const mockCatalog = createMockCatalog()
        ;(global.fetch as jest.Mock).mockResolvedValueOnce(
          createSuccessResponse(mockCatalog)
        )

        const result = await catalogService.getCatalogEquipments('catalog-1')

        expect(result).toEqual([])
      })
    })
  })

  describe('Bulk Operations', () => {
    describe('bulkDeleteCatalogs', () => {
      it('should delete multiple catalogs', async () => {
        const mockResponse = { success: 3, failed: 0 }
        ;(global.fetch as jest.Mock).mockResolvedValueOnce(
          createSuccessResponse(mockResponse)
        )

        const ids = ['catalog-1', 'catalog-2', 'catalog-3']
        const result = await catalogService.bulkDeleteCatalogs(ids)

        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/catalogs/bulk-delete'),
          expect.objectContaining({
            method: 'POST',
            body: JSON.stringify({ ids })
          })
        )
        expect(result).toEqual(mockResponse)
      })

      it('should handle partial failures', async () => {
        const mockResponse = { success: 2, failed: 1 }
        ;(global.fetch as jest.Mock).mockResolvedValueOnce(
          createSuccessResponse(mockResponse)
        )

        const ids = ['catalog-1', 'catalog-2', 'catalog-3']
        const result = await catalogService.bulkDeleteCatalogs(ids)

        expect(result.success).toBe(2)
        expect(result.failed).toBe(1)
      })
    })
  })

  describe('Export/Import Operations', () => {
    describe('exportCatalogs', () => {
      it('should export catalogs to blob', async () => {
        const mockBlob = new Blob(['mock excel data'], { 
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
        })
        ;(global.fetch as jest.Mock).mockResolvedValueOnce({
          ok: true,
          blob: async () => mockBlob
        })

        const result = await catalogService.exportCatalogs()

        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/catalogs/export'),
          expect.objectContaining({
            method: 'GET'
          })
        )
        expect(result).toBeInstanceOf(Blob)
      })

      it('should export catalogs with filters', async () => {
        const mockBlob = new Blob(['mock excel data'])
        ;(global.fetch as jest.Mock).mockResolvedValueOnce({
          ok: true,
          blob: async () => mockBlob
        })

        const filters = { search: 'thiết bị', status: 'ACTIVE' as const }
        await catalogService.exportCatalogs(filters)

        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/catalogs/export?search=thi%E1%BA%BFt+b%E1%BB%8B&status=ACTIVE'),
          expect.any(Object)
        )
      })

      it('should handle export errors', async () => {
        ;(global.fetch as jest.Mock).mockResolvedValueOnce({
          ok: false,
          status: 500
        })

        await expect(catalogService.exportCatalogs()).rejects.toThrow('Failed to export catalogs')
      })
    })

    describe('importCatalogs', () => {
      it('should import catalogs from file', async () => {
        const mockResponse = { success: 5, failed: 0 }
        ;(global.fetch as jest.Mock).mockResolvedValueOnce({
          ok: true,
          json: async () => mockResponse
        })

        const file = new File(['mock data'], 'catalogs.xlsx', {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        const result = await catalogService.importCatalogs(file)

        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/catalogs/import'),
          expect.objectContaining({
            method: 'POST',
            body: expect.any(FormData)
          })
        )
        expect(result).toEqual(mockResponse)
      })

      it('should handle import with errors', async () => {
        const mockResponse = { 
          success: 3, 
          failed: 2, 
          errors: ['Row 3: Invalid catalog code', 'Row 5: Duplicate name'] 
        }
        ;(global.fetch as jest.Mock).mockResolvedValueOnce({
          ok: true,
          json: async () => mockResponse
        })

        const file = new File(['mock data'], 'catalogs.xlsx')
        const result = await catalogService.importCatalogs(file)

        expect(result.success).toBe(3)
        expect(result.failed).toBe(2)
        expect(result.errors).toHaveLength(2)
      })

      it('should handle import failure', async () => {
        ;(global.fetch as jest.Mock).mockResolvedValueOnce({
          ok: false,
          status: 400
        })

        const file = new File([''], 'invalid.txt')
        await expect(catalogService.importCatalogs(file)).rejects.toThrow('Failed to import catalogs')
      })
    })
  })

  describe('Equipment Methods', () => {
    describe('getEquipments', () => {
      it('should fetch equipments with filters', async () => {
        const mockResponse = createMockEquipmentsResponse()
        ;(global.fetch as jest.Mock).mockResolvedValueOnce(
          createSuccessResponse(mockResponse)
        )

        const filters = {
          search: 'máy',
          catalogId: 'catalog-1',
          status: 'ACTIVE' as const,
          page: 1,
          limit: 10
        }

        const result = await catalogService.getEquipments(filters)

        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/equipments?search=m%C3%A1y&catalogId=catalog-1&status=ACTIVE&page=1&limit=10'),
          expect.any(Object)
        )
        expect(result).toEqual(mockResponse)
      })
    })

    describe('checkEquipmentCode', () => {
      it('should check if equipment code exists', async () => {
        const mockResponse = { exists: false }
        ;(global.fetch as jest.Mock).mockResolvedValueOnce(
          createSuccessResponse(mockResponse)
        )

        const result = await catalogService.checkEquipmentCode('EQ999')

        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/equipments/check-code'),
          expect.objectContaining({
            method: 'POST',
            body: JSON.stringify({ code: 'EQ999' })
          })
        )
        expect(result.exists).toBe(false)
      })
    })
  })
})