'use client'

import React, { useState } from 'react'
import { Dialog } from '@/components/ui/Dialog'
import { Button } from '@/components/ui/Button'
import { useDeleteCatalog } from '@/hooks/queries/useCatalogs'
import type { Catalog } from '@/types/catalog'
import { AlertTriangle, Info } from 'lucide-react'

interface DeleteCatalogDialogProps {
  isOpen: boolean
  onClose: () => void
  catalog: Catalog | null
}

export function DeleteCatalogDialog({ isOpen, onClose, catalog }: DeleteCatalogDialogProps) {
  const deleteMutation = useDeleteCatalog()
  const [forceDelete, setForceDelete] = useState(false)

  if (!catalog) return null

  const hasEquipment = (catalog.equipmentCount || 0) > 0

  const handleDelete = async () => {
    try {
      await deleteMutation.mutateAsync({ 
        id: catalog.id, 
        force: forceDelete 
      })
      onClose()
      setForceDelete(false)
    } catch (error) {
      // Error is handled by mutation hook
    }
  }

  const handleClose = () => {
    onClose()
    setForceDelete(false)
  }

  return (
    <Dialog
      isOpen={isOpen}
      onClose={handleClose}
      title={hasEquipment && !forceDelete ? "Cảnh báo xóa danh mục" : "Xác nhận xóa danh mục"}
      className="sm:max-w-md"
    >
      <div className="p-6">
        {hasEquipment && !forceDelete ? (
          <>
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-6 w-6 text-yellow-500" />
              </div>
              <div className="flex-1">
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Danh mục <span className="font-semibold">{catalog.catalogName}</span> hiện có{' '}
                  <span className="font-semibold text-red-600">{catalog.equipmentCount} thiết bị</span> đang liên kết.
                </p>
                <div className="mt-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-3">
                  <div className="flex items-start gap-2">
                    <Info className="h-5 w-5 text-yellow-600 dark:text-yellow-400 flex-shrink-0 mt-0.5" />
                    <div className="text-sm text-yellow-800 dark:text-yellow-200">
                      <p className="font-medium mb-1">Bạn có thể:</p>
                      <ul className="list-disc list-inside space-y-1">
                        <li>Di chuyển hoặc xóa các thiết bị trước</li>
                        <li>Hoặc vô hiệu hóa danh mục (chuyển trạng thái không hoạt động)</li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div className="mt-4">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={forceDelete}
                      onChange={(e) => setForceDelete(e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Tôi muốn vô hiệu hóa danh mục này
                    </span>
                  </label>
                </div>
              </div>
            </div>
          </>
        ) : (
          <>
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-6 w-6 text-red-500" />
              </div>
              <div className="flex-1">
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Bạn có chắc chắn muốn {forceDelete ? 'vô hiệu hóa' : 'xóa'} danh mục{' '}
                  <span className="font-semibold">{catalog.catalogName}</span>?
                </p>
                {forceDelete ? (
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    Danh mục sẽ được chuyển sang trạng thái không hoạt động. Các thiết bị vẫn được giữ nguyên.
                  </p>
                ) : (
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    Hành động này không thể hoàn tác.
                  </p>
                )}
                <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                  <dl className="text-sm space-y-1">
                    <div className="flex justify-between">
                      <dt className="text-gray-500 dark:text-gray-400">Mã danh mục:</dt>
                      <dd className="font-mono text-gray-900 dark:text-gray-100">{catalog.catalogCode}</dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-gray-500 dark:text-gray-400">Số thiết bị:</dt>
                      <dd className="text-gray-900 dark:text-gray-100">{catalog.equipmentCount || 0}</dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-gray-500 dark:text-gray-400">Trạng thái:</dt>
                      <dd>
                        <span className={`inline-flex px-2 py-0.5 text-xs font-semibold rounded-full ${
                          catalog.status === 'ACTIVE'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                        }`}>
                          {catalog.status === 'ACTIVE' ? 'Hoạt động' : 'Không hoạt động'}
                        </span>
                      </dd>
                    </div>
                  </dl>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Actions */}
        <div className="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <Button
            type="button"
            variant="secondary"
            onClick={handleClose}
            disabled={deleteMutation.isPending}
          >
            Hủy
          </Button>
          {(hasEquipment && forceDelete) || !hasEquipment ? (
            <Button
              type="button"
              variant="danger"
              onClick={handleDelete}
              disabled={deleteMutation.isPending}
            >
              {deleteMutation.isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Đang xử lý...
                </>
              ) : forceDelete ? (
                'Vô hiệu hóa'
              ) : (
                'Xóa'
              )}
            </Button>
          ) : null}
        </div>
      </div>
    </Dialog>
  )
}