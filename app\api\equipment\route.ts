import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { verifySession } from '@/lib/auth-server';
import { Prisma } from '@prisma/client';

export async function GET(request: NextRequest) {
  try {
    console.log('Equipment GET request received');
    
    const session = await verifySession(request);
    console.log('Session verification result:', session ? 'authenticated' : 'not authenticated');
    
    if (!session) {
      console.log('No valid session found');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search') || '';
    const catalogId = searchParams.get('catalogId');
    const manufacturer = searchParams.get('manufacturer');
    const status = searchParams.get('status') as 'ACTIVE' | 'INACTIVE' | null;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const skip = (page - 1) * limit;

    const where: Prisma.EquipmentWhereInput = {
      ...(search && {
        OR: [
          { equipmentCode: { contains: search, mode: 'insensitive' } },
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { model: { contains: search, mode: 'insensitive' } }
        ]
      }),
      ...(catalogId && { catalogId }),
      ...(manufacturer && { manufacturer: { contains: manufacturer, mode: 'insensitive' } }),
      ...(status && { status })
    };

    console.log('Fetching equipment with params:', { page, limit, sortBy, sortOrder, where });
    
    const [equipments, total] = await Promise.all([
      prisma.equipment.findMany({
        where,
        skip,
        take: limit,
        orderBy: { [sortBy]: sortOrder },
        include: {
          catalog: {
            select: {
              id: true,
              catalogCode: true,
              catalogName: true
            }
          },
          creator: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      }),
      prisma.equipment.count({ where })
    ]);
    
    console.log(`Found ${equipments.length} equipment items, total: ${total}`);

    return NextResponse.json({
      data: equipments,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Error fetching equipment:', error);
    
    // Check for specific error types
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      console.error('Prisma error code:', error.code);
      console.error('Prisma error message:', error.message);
      
      if (error.code === 'P2002') {
        return NextResponse.json(
          { error: 'Database constraint violation' },
          { status: 400 }
        );
      }
      
      if (error.code === 'P2025') {
        return NextResponse.json(
          { error: 'Record not found' },
          { status: 404 }
        );
      }
    }
    
    if (error instanceof Prisma.PrismaClientInitializationError) {
      console.error('Database connection error:', error.message);
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 503 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to fetch equipment', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await verifySession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Both ADMIN and USER can create equipment
    // Remove this check to allow all authenticated users

    const body = await request.json();
    const equipmentData = body;

    // Check if equipment code already exists
    const existing = await prisma.equipment.findUnique({
      where: { equipmentCode: equipmentData.equipmentCode }
    });

    if (existing) {
      return NextResponse.json(
        { error: 'Equipment code already exists' },
        { status: 400 }
      );
    }

    // Verify catalog exists
    const catalog = await prisma.catalog.findUnique({
      where: { id: equipmentData.catalogId }
    });

    if (!catalog) {
      return NextResponse.json(
        { error: 'Catalog not found' },
        { status: 400 }
      );
    }

    // Create equipment
    const equipment = await prisma.equipment.create({
      data: {
        ...equipmentData,
        createdBy: session.userId
      },
      include: {
        catalog: {
          select: {
            id: true,
            catalogCode: true,
            catalogName: true
          }
        },
        creator: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });


    // Log audit
    await prisma.auditLog.create({
      data: {
        userId: session.userId,
        action: 'CREATE_EQUIPMENT',
        details: {
          equipmentId: equipment.id,
          equipmentCode: equipment.equipmentCode,
          name: equipment.name
        }
      }
    });

    return NextResponse.json(equipment, { status: 201 });
  } catch (error) {
    console.error('Error creating equipment:', error);
    return NextResponse.json(
      { error: 'Failed to create equipment' },
      { status: 500 }
    );
  }
}