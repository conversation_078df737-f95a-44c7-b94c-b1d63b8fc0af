import { apiClient } from '@/lib/apiClient'
import { localApiClient } from '@/lib/localApiClient'

interface BaseServiceConfig {
  baseUrl?: string
  useLocalApi?: boolean
}

export abstract class BaseService {
  protected baseUrl: string
  private client: typeof apiClient | typeof localApiClient

  constructor(config: BaseServiceConfig = {}) {
    this.baseUrl = config.baseUrl || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api/v1'
    // Use localApiClient if baseUrl is empty or useLocalApi is true
    this.client = (config.useLocalApi || config.baseUrl === '') ? localApiClient : apiClient
  }

  protected async get<T>(endpoint: string, signal?: AbortSignal): Promise<T> {
    return this.client.get<T>(endpoint, signal ? { signal } : undefined)
  }

  protected async post<T>(endpoint: string, data?: any, options?: { signal?: AbortSignal }): Promise<T> {
    return this.client.post<T>(endpoint, data, options ? { signal: options.signal } : undefined)
  }

  protected async put<T>(endpoint: string, data?: any, options?: { signal?: AbortSignal }): Promise<T> {
    return this.client.put<T>(endpoint, data, options ? { signal: options.signal } : undefined)
  }

  protected async patch<T>(endpoint: string, data?: any, options?: { signal?: AbortSignal }): Promise<T> {
    return this.client.patch<T>(endpoint, data, options ? { signal: options.signal } : undefined)
  }

  protected async delete<T>(endpoint: string, options?: { signal?: AbortSignal }): Promise<T> {
    return this.client.delete<T>(endpoint, options ? { signal: options.signal } : undefined)
  }
}