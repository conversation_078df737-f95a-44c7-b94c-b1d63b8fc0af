'use client'

import React, { useState, useEffect } from 'react'
import { User, Mail, Phone, Briefcase, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { useProfile, useUpdateProfile } from '@/hooks/queries/useProfile'
import type { UpdateProfileInput } from '@/services/profileService'

export function ProfileInfoTab() {
  const { data: profile, isLoading } = useProfile()
  const updateMutation = useUpdateProfile()
  const [isEditing, setIsEditing] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [formData, setFormData] = useState<UpdateProfileInput>({
    name: '',
    department: '',
    phone: '',
  })

  useEffect(() => {
    if (profile) {
      setFormData({
        name: profile.name || '',
        department: profile.department || '',
        phone: profile.phone || '',
      })
    }
  }, [profile])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name || formData.name.trim().length === 0) {
      newErrors.name = 'Tên là bắt buộc'
    }

    if (formData.phone && !/^[0-9+\-\s()]+$/.test(formData.phone)) {
      newErrors.phone = 'Số điện thoại không hợp lệ'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    try {
      await updateMutation.mutateAsync(formData)
      setIsEditing(false)
      setErrors({})
    } catch (error) {
      console.error('Update profile error:', error)
    }
  }

  const handleCancel = () => {
    setIsEditing(false)
    setErrors({})
    if (profile) {
      setFormData({
        name: profile.name || '',
        department: profile.department || '',
        phone: profile.phone || '',
      })
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">
          Không thể tải thông tin người dùng
        </p>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Username (Read-only) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          <User className="inline w-4 h-4 mr-1" />
          Tên người dùng
        </label>
        <input
          type="text"
          value={profile.username}
          disabled
          className="w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed"
        />
      </div>

      {/* Email (Read-only) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          <Mail className="inline w-4 h-4 mr-1" />
          Email
        </label>
        <input
          type="email"
          value={profile.email}
          disabled
          className="w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed"
        />
      </div>

      {/* Name (Editable) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Tên đầy đủ *
        </label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value.trim() })}
          disabled={!isEditing}
          className={`w-full h-10 px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 ${
            errors.name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
          } ${!isEditing ? 'bg-gray-100 dark:bg-gray-700' : 'bg-white dark:bg-gray-800'}`}
        />
        {errors.name && (
          <p className="mt-1 text-sm text-red-500 flex items-center">
            <AlertCircle className="w-4 h-4 mr-1" />
            {errors.name}
          </p>
        )}
      </div>

      {/* Department (Editable) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          <Briefcase className="inline w-4 h-4 mr-1" />
          Phòng ban
        </label>
        <input
          type="text"
          value={formData.department}
          onChange={(e) => setFormData({ ...formData, department: e.target.value.trim() })}
          disabled={!isEditing}
          className={`w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:text-gray-100 ${
            !isEditing ? 'bg-gray-100 dark:bg-gray-700' : 'bg-white dark:bg-gray-800'
          }`}
        />
      </div>

      {/* Phone (Editable) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          <Phone className="inline w-4 h-4 mr-1" />
          Số điện thoại
        </label>
        <input
          type="text"
          value={formData.phone}
          onChange={(e) => setFormData({ ...formData, phone: e.target.value.trim() })}
          disabled={!isEditing}
          className={`w-full h-10 px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 dark:text-gray-100 ${
            errors.phone ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
          } ${!isEditing ? 'bg-gray-100 dark:bg-gray-700' : 'bg-white dark:bg-gray-800'}`}
        />
        {errors.phone && (
          <p className="mt-1 text-sm text-red-500 flex items-center">
            <AlertCircle className="w-4 h-4 mr-1" />
            {errors.phone}
          </p>
        )}
      </div>

      {/* Role (Read-only) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Vai trò
        </label>
        <input
          type="text"
          value={profile.role === 'ADMIN' ? 'Quản trị viên' : 'Người dùng'}
          disabled
          className="w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed"
        />
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-3 pt-4">
        {!isEditing ? (
          <Button
            type="button"
            variant="primary"
            onClick={() => setIsEditing(true)}
          >
            Chỉnh sửa
          </Button>
        ) : (
          <>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={updateMutation.isPending}
            >
              Hủy
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={updateMutation.isPending}
            >
              {updateMutation.isPending ? 'Đang lưu...' : 'Lưu thay đổi'}
            </Button>
          </>
        )}
      </div>
    </form>
  )
}