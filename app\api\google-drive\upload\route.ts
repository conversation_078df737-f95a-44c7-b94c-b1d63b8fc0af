import { NextRequest, NextResponse } from 'next/server';
import { googleDriveServiceNoLogin } from '@/services/googleDriveServiceNoLogin';

export async function POST(request: NextRequest) {
  try {
    // Check if Google Drive is configured
    const isConfigured = await googleDriveServiceNoLogin.isAuthenticated();
    console.log('isConfigured', isConfigured);

    if (!isConfigured) {
      return NextResponse.json(
        { error: 'Google Drive not configured' },
        { status: 503 }
      );
    }
    
    // Get file from form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Convert File to Buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Upload to Google Drive using no-login service
    const result = await googleDriveServiceNoLogin.uploadFile(
      buffer,
      file.name,
      file.type
    );

    return NextResponse.json({
      success: true,
      ...result
    });

  } catch (error) {
    console.error('Google Drive upload error:', error);
    return NextResponse.json(
      { error: 'Failed to upload file to Google Drive' },
      { status: 500 }
    );
  }
}