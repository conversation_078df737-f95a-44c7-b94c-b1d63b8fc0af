'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useDebounce } from '@/hooks/useDebounce';
import { useEquipments, useDeleteEquipment } from '@/hooks/queries/useEquipment';
import { useAuth } from '@/hooks/queries/useAuth';
import { Button } from '@/components/ui/Button';
import { Dialog } from '@/components/ui/Dialog';
import { EquipmentFilters } from './EquipmentFilters';
import { Pencil, Trash2, Eye } from 'lucide-react';
import type { Equipment } from '@/types/equipment';

export function EquipmentList() {
  const router = useRouter();
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCatalog, setSelectedCatalog] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<'ACTIVE' | 'INACTIVE' | ''>('');
  const [selectedManufacturer, setSelectedManufacturer] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [equipmentToDelete, setEquipmentToDelete] = useState<Equipment | null>(null);

  const debouncedSearch = useDebounce(searchTerm, 500);

  const { data, isLoading, error } = useEquipments({
    search: debouncedSearch,
    catalogId: selectedCatalog,
    manufacturer: selectedManufacturer,
    status: selectedStatus || undefined,
    page: currentPage,
    limit: 10,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });

  const deleteEquipmentMutation = useDeleteEquipment();

  const canEdit = user?.role === 'ADMIN' || user?.role === 'USER';
  const canDelete = user?.role === 'ADMIN' || user?.role === 'USER';

  const handleView = (equipment: Equipment) => {
    router.push(`/equipment/${equipment.id}`);
  };

  const handleEdit = (equipment: Equipment) => {
    router.push(`/equipment/${equipment.id}/edit`);
  };

  const handleDelete = (equipment: Equipment) => {
    setEquipmentToDelete(equipment);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!equipmentToDelete) return;

    try {
      await deleteEquipmentMutation.mutateAsync(equipmentToDelete.id);
      setDeleteDialogOpen(false);
      setEquipmentToDelete(null);
    } catch (error) {
      console.error('Failed to delete equipment:', error);
    }
  };



  // Extract unique manufacturers from equipment list
  const manufacturers = Array.from(
    new Set(data?.data.filter(e => e.manufacturer).map(e => e.manufacturer!))
  );

  const handleClearFilters = () => {
    setSearchTerm('');
    setSelectedCatalog('');
    setSelectedManufacturer('');
    setSelectedStatus('');
    setCurrentPage(1);
  };

  return (
    <div className="space-y-4">
      {/* Filters */}
      <EquipmentFilters
        searchTerm={searchTerm}
        selectedCatalog={selectedCatalog}
        selectedManufacturer={selectedManufacturer}
        selectedStatus={selectedStatus}
        manufacturers={manufacturers}
        onSearchChange={setSearchTerm}
        onCatalogChange={setSelectedCatalog}
        onManufacturerChange={setSelectedManufacturer}
        onStatusChange={setSelectedStatus}
        onClearFilters={handleClearFilters}
      />

      {/* Equipment Table */}
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : error ? (
        <div className="text-center text-red-600 py-8">
          Lỗi khi tải danh sách thiết bị
        </div>
      ) : !data?.data.length ? (
        <div className="text-center text-gray-500 py-8">
          Không tìm thấy thiết bị nào
        </div>
      ) : (
        <>
          <div className="overflow-x-auto bg-white rounded-lg shadow">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Mã thiết bị
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tên thiết bị
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Danh mục
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Nhà sản xuất
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Giá
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Trạng thái
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Thao tác
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data.data.map((equipment) => (
                  <tr key={equipment.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {equipment.equipmentCode}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {equipment.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {equipment.catalog?.catalogName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {equipment.manufacturer || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {equipment.price ? new Intl.NumberFormat('vi-VN').format(equipment.price) + ' VNĐ' : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          equipment.status === 'ACTIVE'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {equipment.status === 'ACTIVE' ? 'Hoạt động' : 'Không hoạt động'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end gap-2">
                        <button
                          onClick={() => handleView(equipment)}
                          className="text-blue-600 hover:text-blue-900"
                          title="Xem chi tiết"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        {canEdit && (
                          <button
                            onClick={() => handleEdit(equipment)}
                            className="text-yellow-600 hover:text-yellow-900"
                            title="Chỉnh sửa"
                          >
                            <Pencil className="w-4 h-4" />
                          </button>
                        )}
                        {canDelete && (
                          <button
                            onClick={() => handleDelete(equipment)}
                            className="text-red-600 hover:text-red-900"
                            title="Xóa"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {data.totalPages > 1 && (
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-700">
                Hiển thị {(currentPage - 1) * 10 + 1} - {Math.min(currentPage * 10, data.total)} trong tổng số {data.total} thiết bị
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                  disabled={currentPage === 1}
                >
                  Trước
                </Button>
                {Array.from({ length: Math.min(5, data.totalPages) }, (_, i) => {
                  const page = i + 1;
                  return (
                    <Button
                      key={page}
                      variant={currentPage === page ? 'primary' : 'outline'}
                      onClick={() => setCurrentPage(page)}
                    >
                      {page}
                    </Button>
                  );
                })}
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage((p) => Math.min(data.totalPages, p + 1))}
                  disabled={currentPage === data.totalPages}
                >
                  Sau
                </Button>
              </div>
            </div>
          )}
        </>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        title="Xác nhận xóa"
        description={`Bạn có chắc chắn muốn xóa thiết bị "${equipmentToDelete?.name}"? Hành động này không thể hoàn tác.`}
      >
        <div className="flex justify-end gap-3 mt-4">
          <Button
            variant="outline"
            onClick={() => setDeleteDialogOpen(false)}
          >
            Hủy
          </Button>
          <Button
            variant="primary"
            onClick={confirmDelete}
            disabled={deleteEquipmentMutation.isPending}
          >
            {deleteEquipmentMutation.isPending ? 'Đang xóa...' : 'Xóa'}
          </Button>
        </div>
      </Dialog>


    </div>
  );
}