import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/db'
import { getServerSession } from '@/lib/auth-server'
import { AuditAction } from '@/lib/auth'

// Validation schemas
const updateCatalogSchema = z.object({
  catalogCode: z.string()
    .regex(/^[A-Z0-9_]+$/, 'Code must contain only uppercase letters, numbers and underscores')
    .max(50, 'Code must be less than 50 characters')
    .optional(),
  catalogName: z.string()
    .min(1, 'Catalog name is required')
    .max(100, 'Catalog name must be less than 100 characters'),
  description: z.string().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE']).optional(),
})

// GET /api/catalogs/[id] - Get catalog by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(request)
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = await params
    const catalog = await prisma.catalog.findUnique({
      where: { id },
      include: {
        equipments: {
          where: { status: 'ACTIVE' },
          orderBy: { name: 'asc' },
        },
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        },
        _count: {
          select: { equipments: true }
        }
      }
    })

    if (!catalog) {
      return NextResponse.json(
        { error: 'Catalog not found' },
        { status: 404 }
      )
    }

    // Transform the response
    const transformedCatalog = {
      ...catalog,
      equipmentCount: catalog._count.equipments,
      _count: undefined,
    }

    return NextResponse.json(transformedCatalog)
  } catch (error) {
    console.error('GET /api/catalogs/[id] error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch catalog' },
      { status: 500 }
    )
  }
}

// PUT /api/catalogs/[id] - Update catalog
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(request)
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is admin
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Forbidden: Only admins can update catalogs' },
        { status: 403 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validation = updateCatalogSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid data', details: validation.error.flatten() },
        { status: 400 }
      )
    }

    const { catalogCode, catalogName, description, status } = validation.data

    // Check if catalog exists
    const { id } = await params
    const existingCatalog = await prisma.catalog.findUnique({
      where: { id }
    })

    if (!existingCatalog) {
      return NextResponse.json(
        { error: 'Catalog not found' },
        { status: 404 }
      )
    }

    // Check if new name conflicts with other active catalogs
    if (catalogName !== existingCatalog.catalogName) {
      const nameConflict = await prisma.catalog.findFirst({
        where: {
          id: { not: id },
          catalogName: {
            equals: catalogName,
            mode: 'insensitive'
          },
          status: 'ACTIVE'
        }
      })

      if (nameConflict) {
        return NextResponse.json(
          { error: 'Tên danh mục đã tồn tại', message: 'Tên danh mục đã tồn tại' },
          { status: 409 }
        )
      }
    }

    // Check if new code conflicts with other catalogs
    if (catalogCode && catalogCode !== existingCatalog.catalogCode) {
      const codeConflict = await prisma.catalog.findFirst({
        where: {
          id: { not: id },
          catalogCode: catalogCode
        }
      })

      if (codeConflict) {
        return NextResponse.json(
          { error: 'Mã danh mục đã tồn tại', message: 'Mã danh mục đã tồn tại' },
          { status: 409 }
        )
      }
    }

    // Update catalog
    const catalog = await prisma.catalog.update({
      where: { id },
      data: {
        ...(catalogCode && { catalogCode }),
        catalogName,
        description,
        status,
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        },
        _count: {
          select: { equipments: true }
        }
      }
    })

    // Log audit
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: AuditAction.UPDATE,
        details: {
          entity: 'Catalog',
          catalogId: catalog.id,
          changes: {
            before: {
              catalogCode: existingCatalog.catalogCode,
              catalogName: existingCatalog.catalogName,
              description: existingCatalog.description,
              status: existingCatalog.status,
            },
            after: {
              catalogCode: catalogCode || existingCatalog.catalogCode,
              catalogName,
              description,
              status,
            }
          }
        },
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
      }
    })

    // Transform the response
    const transformedCatalog = {
      ...catalog,
      equipmentCount: catalog._count.equipments,
      _count: undefined,
    }

    return NextResponse.json(transformedCatalog)
  } catch (error) {
    console.error('PUT /api/catalogs/[id] error:', error)
    return NextResponse.json(
      { error: 'Failed to update catalog' },
      { status: 500 }
    )
  }
}

// DELETE /api/catalogs/[id] - Delete catalog
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(request)
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is admin
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Forbidden: Only admins can delete catalogs' },
        { status: 403 }
      )
    }

    // Check if catalog exists
    const { id } = await params
    const catalog = await prisma.catalog.findUnique({
      where: { id },
      include: {
        _count: {
          select: { equipments: true }
        }
      }
    })

    if (!catalog) {
      return NextResponse.json(
        { error: 'Catalog not found' },
        { status: 404 }
      )
    }

    // Check if catalog has equipment
    if (catalog._count.equipments > 0) {
      // Parse query params for force delete
      const searchParams = request.nextUrl.searchParams
      const force = searchParams.get('force') === 'true'

      if (!force) {
        return NextResponse.json(
          { 
            error: 'Cannot delete catalog with equipment',
            equipmentCount: catalog._count.equipments,
            message: `This catalog has ${catalog._count.equipments} equipment(s). Please move or delete them first, or use force delete.`
          },
          { status: 409 }
        )
      }

      // Force delete: soft delete by setting status to INACTIVE
      const updatedCatalog = await prisma.catalog.update({
        where: { id },
        data: { status: 'INACTIVE' }
      })

      // Log audit
      await prisma.auditLog.create({
        data: {
          userId: session.user.id,
          action: AuditAction.DELETE,
          details: {
            entity: 'Catalog',
            catalogId: catalog.id,
            catalogCode: catalog.catalogCode,
            type: 'soft_delete',
            equipmentCount: catalog._count.equipments,
          },
          ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
        }
      })

      return NextResponse.json({
        message: 'Catalog deactivated successfully',
        catalog: updatedCatalog
      })
    }

    // Hard delete if no equipment
    await prisma.catalog.delete({
      where: { id }
    })

    // Log audit
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: AuditAction.DELETE,
        details: {
          entity: 'Catalog',
          catalogId: catalog.id,
          catalogCode: catalog.catalogCode,
          type: 'hard_delete',
        },
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
      }
    })

    return NextResponse.json({
      message: 'Catalog deleted successfully'
    })
  } catch (error) {
    console.error('DELETE /api/catalogs/[id] error:', error)
    return NextResponse.json(
      { error: 'Failed to delete catalog' },
      { status: 500 }
    )
  }
}