// Load environment variables from .env.local
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') });

const { google } = require('googleapis');
const readline = require('readline');
const fs = require('fs').promises;
const http = require('http');
const url = require('url');

const PORT = 3002;

async function getRefreshToken() {
  console.log('╔════════════════════════════════════════════════╗');
  console.log('║     TOOL LẤY REFRESH TOKEN - CHẠY 1 LẦN DUY NHẤT     ║');
  console.log('╚════════════════════════════════════════════════╝\n');

  try {
    // Get credentials from environment variables
    const client_id = process.env.GOOGLE_CLIENT_ID;
    const client_secret = process.env.GOOGLE_CLIENT_SECRET;
    const redirect_uri = `http://localhost:${PORT}/auth/callback`;
    
    // Check if credentials are available
    if (!client_id || !client_secret) {
      console.error('❌ Lỗi: Không tìm thấy Google OAuth credentials!');
      console.error('\nVui lòng kiểm tra file .env.local có chứa:');
      console.error('- GOOGLE_CLIENT_ID');
      console.error('- GOOGLE_CLIENT_SECRET');
      console.error('\nNếu chưa có, copy từ .env.example và thêm credentials của bạn.');
      process.exit(1);
    }
    
    console.log('✅ Đã load Google credentials từ .env.local')

    // Create OAuth2 client
    const oauth2Client = new google.auth.OAuth2(
      client_id,
      client_secret,
      redirect_uri
    );

    // Generate auth URL
    const authUrl = oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: [
        'https://www.googleapis.com/auth/drive.file',
        'https://www.googleapis.com/auth/drive'
      ],
      prompt: 'consent' // Force consent screen to get refresh token
    });

    console.log('📌 Bước 1: Đang mở trình duyệt để đăng nhập...');
    console.log('📌 URL đăng nhập:', authUrl);
    
    // Create temporary server to receive callback
    const server = http.createServer(async (req, res) => {
      const queryObject = url.parse(req.url, true).query;
      
      if (queryObject.code) {
        console.log('\n✅ Đã nhận được authorization code!');
        
        try {
          // Get tokens
          const { tokens } = await oauth2Client.getToken(queryObject.code);
          
          console.log('\n🎉 LẤY REFRESH TOKEN THÀNH CÔNG!\n');
          console.log('═══════════════════════════════════════════════════');
          console.log('REFRESH TOKEN CỦA BẠN:');
          console.log('═══════════════════════════════════════════════════');
          console.log(tokens.refresh_token);
          console.log('═══════════════════════════════════════════════════');
          
          // Save to tokens.json
          await fs.writeFile(path.join(__dirname, '..', 'google-tokens.json'), JSON.stringify(tokens, null, 2));
          console.log('\n✅ Đã lưu vào file google-tokens.json');
          
          // Update .env.example
          console.log('\n📌 Cập nhật GOOGLE_REFRESH_TOKEN trong file .env:');
          console.log(`GOOGLE_REFRESH_TOKEN=${tokens.refresh_token}`);
          
          // Send success response
          res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
          res.end(`
            <html>
              <head>
                <title>Thành công!</title>
                <style>
                  body {
                    font-family: Arial, sans-serif;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                    margin: 0;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                  }
                  .container {
                    background: white;
                    padding: 40px;
                    border-radius: 10px;
                    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                    text-align: center;
                    max-width: 500px;
                  }
                  h1 { color: #4caf50; }
                  .token-box {
                    background: #f5f5f5;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 20px 0;
                    word-break: break-all;
                    font-family: monospace;
                    font-size: 12px;
                  }
                  .instruction {
                    background: #e3f2fd;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 20px 0;
                    text-align: left;
                  }
                </style>
              </head>
              <body>
                <div class="container">
                  <h1>✅ Lấy Refresh Token Thành Công!</h1>
                  <div class="token-box">
                    ${tokens.refresh_token}
                  </div>
                  <div class="instruction">
                    <strong>Hướng dẫn sử dụng:</strong><br><br>
                    1. Token đã được lưu vào <code>google-tokens.json</code><br>
                    2. Cập nhật <code>GOOGLE_REFRESH_TOKEN</code> trong file <code>.env</code><br>
                    3. Khởi động lại server<br>
                    4. Không cần đăng nhập lại nữa!
                  </div>
                  <p>Bạn có thể đóng cửa sổ này.</p>
                </div>
              </body>
            </html>
          `);
          
          // Close server
          setTimeout(() => {
            server.close();
            process.exit(0);
          }, 3000);
          
        } catch (error) {
          console.error('❌ Lỗi khi lấy token:', error);
          res.writeHead(500);
          res.end('Error getting tokens');
        }
      } else if (queryObject.error) {
        console.error('❌ Lỗi authorization:', queryObject.error);
        res.writeHead(400);
        res.end('Authorization failed');
        server.close();
      }
    });

    server.listen(PORT, () => {
      console.log(`\n📌 Server tạm thời đang chạy tại: http://localhost:${PORT}`);
      console.log('📌 Đang mở trình duyệt...\n');
      
      // Try to open browser automatically
      try {
         const opener =
   process.platform === 'win32'
     ? `start "" "${authUrl}"`
     : process.platform === 'darwin'
       ? `open "${authUrl}"`
       : `xdg-open "${authUrl}"`;
 require('child_process').exec(opener);
      } catch (e) {
        console.log('⚠️  Không thể mở trình duyệt tự động.');
        console.log('📌 Vui lòng mở link sau trong trình duyệt:');
        console.log(authUrl);
      }
    });

  } catch (error) {
    console.error('❌ Lỗi:', error.message);
  }
}

// Run
getRefreshToken();