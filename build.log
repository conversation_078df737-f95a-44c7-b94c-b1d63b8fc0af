
> bidding-system@1.0.0 build
> next build

 ⚠ Warning: Found multiple lockfiles. Selecting /mnt/d/WorkSpace/package-lock.json.
   Consider removing the lockfiles at:
   * /mnt/d/WorkSpace/viet-my-medical/package-lock.json

   ▲ Next.js 15.4.6
   - Environments: .env.local

   Creating an optimized production build ...
 ✓ Compiled successfully in 26.0s
   Skipping linting
   Checking validity of types ...
   Collecting page data ...
   Generating static pages (0/41) ...
   Generating static pages (10/41) 
   Generating static pages (20/41) 
   Generating static pages (30/41) 
 ✓ Generating static pages (41/41)
   Finalizing page optimization ...
   Collecting build traces ...

Route (app)                                                    Size  First Load JS
┌ ○ /                                                         458 B         100 kB
├ ○ /_not-found                                               995 B         101 kB
├ ƒ /api/auth/google                                          245 B        99.8 kB
├ ƒ /api/auth/google/callback                                 245 B        99.8 kB
├ ƒ /api/auth/google/logout                                   245 B        99.8 kB
├ ƒ /api/auth/google/status                                   245 B        99.8 kB
├ ƒ /api/auth/login                                           245 B        99.8 kB
├ ƒ /api/auth/logout                                          245 B        99.8 kB
├ ƒ /api/auth/refresh                                         245 B        99.8 kB
├ ƒ /api/auth/session                                         245 B        99.8 kB
├ ƒ /api/bidding-documents                                    245 B        99.8 kB
├ ƒ /api/bidding-documents/[id]                               245 B        99.8 kB
├ ƒ /api/bidding-documents/[id]/attachments                   245 B        99.8 kB
├ ƒ /api/bidding-documents/[id]/attachments/[attachmentId]    245 B        99.8 kB
├ ƒ /api/bidding-documents/[id]/attachments/batch             245 B        99.8 kB
├ ƒ /api/bidding-documents/[id]/attachments/google-drive      245 B        99.8 kB
├ ƒ /api/bidding-documents/ai-profile                         245 B        99.8 kB
├ ƒ /api/bidding-documents/check-code                         245 B        99.8 kB
├ ƒ /api/catalogs                                             245 B        99.8 kB
├ ƒ /api/catalogs/[id]                                        245 B        99.8 kB
├ ƒ /api/catalogs/check-code                                  245 B        99.8 kB
├ ƒ /api/documents                                            245 B        99.8 kB
├ ƒ /api/documents/[id]                                       245 B        99.8 kB
├ ƒ /api/documents/download/[id]                              245 B        99.8 kB
├ ƒ /api/documents/tags                                       245 B        99.8 kB
├ ƒ /api/equipment                                            245 B        99.8 kB
├ ƒ /api/equipment/[id]                                       245 B        99.8 kB
├ ƒ /api/equipment/[id]/documents                             245 B        99.8 kB
├ ƒ /api/equipment/[id]/documents/[docId]                     245 B        99.8 kB
├ ƒ /api/equipment/[id]/documents/batch                       245 B        99.8 kB
├ ƒ /api/equipment/export                                     245 B        99.8 kB
├ ƒ /api/equipment/import                                     245 B        99.8 kB
├ ƒ /api/google-drive/client-token                            245 B        99.8 kB
├ ƒ /api/google-drive/export                                  245 B        99.8 kB
├ ƒ /api/google-drive/file/[id]                               245 B        99.8 kB
├ ƒ /api/google-drive/files                                   245 B        99.8 kB
├ ƒ /api/google-drive/files/batch                             245 B        99.8 kB
├ ƒ /api/google-drive/upload                                  245 B        99.8 kB
├ ƒ /api/uploads/[...path]                                    245 B        99.8 kB
├ ƒ /api/users                                                245 B        99.8 kB
├ ƒ /api/users/[id]                                           245 B        99.8 kB
├ ƒ /api/users/[id]/reset-password                            245 B        99.8 kB
├ ƒ /api/users/[id]/toggle-status                             245 B        99.8 kB
├ ƒ /api/users/change-password                                245 B        99.8 kB
├ ƒ /api/users/check-email                                    245 B        99.8 kB
├ ƒ /api/users/check-username                                 245 B        99.8 kB
├ ƒ /api/users/profile                                        245 B        99.8 kB
├ ○ /bidding-documents                                      5.09 kB         140 kB
├ ƒ /bidding-documents/[id]                                 6.42 kB         158 kB
├ ƒ /bidding-documents/[id]/edit                            1.23 kB         150 kB
├ ○ /bidding-documents/new                                  1.06 kB         150 kB
├ ○ /catalogs                                               6.98 kB         143 kB
├ ○ /dashboard                                              2.85 kB         136 kB
├ ○ /documents                                              7.64 kB         149 kB
├ ○ /equipment                                              7.32 kB         143 kB
├ ƒ /equipment/[id]                                         4.39 kB         141 kB
├ ƒ /equipment/[id]/edit                                    1.21 kB         148 kB
├ ○ /equipment/new                                          1.05 kB         148 kB
├ ○ /login                                                  5.31 kB         114 kB
├ ƒ /uploads/[...path]                                        245 B        99.8 kB
└ ○ /users                                                  8.58 kB         142 kB
+ First Load JS shared by all                               99.6 kB
  ├ chunks/4bd1b696-223acfa09453497a.js                     54.1 kB
  ├ chunks/5964-cbdecf18eb56210b.js                         43.5 kB
  └ other shared chunks (total)                             1.93 kB


ƒ Middleware                                                50.3 kB

○  (Static)   prerendered as static content
ƒ  (Dynamic)  server-rendered on demand

