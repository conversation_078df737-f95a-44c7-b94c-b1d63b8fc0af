'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Select } from '@/components/ui/Select'
import { Card } from '@/components/ui/card'
import { useBiddingDocuments } from '@/hooks/queries/useBiddingDocuments'
import { useEquipments } from '@/hooks/queries/useEquipment'
import { useToast } from '@/hooks/useToast'
import { tokenManager } from '@/lib/tokenManager'
import { 
  FileText, 
  Package, 
  Sparkles, 
  Upload, 
  AlertCircle, 
  Loader2, 
  CheckCircle,
  ArrowRight,
  FileSpreadsheet,
  Plus,
  Trash2
} from 'lucide-react'
import type { BiddingDocument } from '@/types/biddingDocument'

interface EquipmentItem {
  equipmentId: string
  pageRange?: {
    from: number
    to: number
  }
}

interface AIGenerationPanelProps {
  onGenerationComplete?: () => void
}

export function AIGenerationPanel({ onGenerationComplete }: AIGenerationPanelProps) {
  const [selectedDocument, setSelectedDocument] = useState<string>('')
  const [selectedFile, setSelectedFile] = useState<string>('')
  const [equipmentItems, setEquipmentItems] = useState<EquipmentItem[]>([{ equipmentId: '' }])
  const [isGenerating, setIsGenerating] = useState(false)
  const [generationStep, setGenerationStep] = useState<'idle' | 'generating' | 'uploading' | 'saving' | 'complete'>('idle')
  
  const toast = useToast()
  
  // Fetch bidding documents
  const { data: documentsData } = useBiddingDocuments({ page: 1, limit: 100 })
  const documents = documentsData?.items || []
  
  // Fetch equipment
  const { data: equipmentsData } = useEquipments({ status: 'ACTIVE' })
  const equipments = equipmentsData?.data || []
  
  // Get selected document details
  const selectedDocumentData = documents.find(doc => doc.id === selectedDocument)
  const pdfFiles = selectedDocumentData?.attachments?.filter(att => att.mimeType === 'application/pdf') || []

  const handleAddEquipment = () => {
    setEquipmentItems([...equipmentItems, { equipmentId: '' }])
  }

  const handleRemoveEquipment = (index: number) => {
    setEquipmentItems(equipmentItems.filter((_, i) => i !== index))
  }

  const handleEquipmentChange = (index: number, equipmentId: string) => {
    const newItems = [...equipmentItems]
    newItems[index] = { ...newItems[index], equipmentId }
    setEquipmentItems(newItems)
  }

  const handlePageRangeChange = (index: number, field: 'from' | 'to', value: string) => {
    const newItems = [...equipmentItems]
    const pageValue = parseInt(value) || 0
    
    if (!newItems[index].pageRange) {
      newItems[index].pageRange = { from: 0, to: 0 }
    }
    
    newItems[index].pageRange![field] = pageValue
    setEquipmentItems(newItems)
  }

  const handleGenerate = async () => {
    if (!selectedDocument || !selectedFile || equipmentItems.every(item => !item.equipmentId)) {
      toast.error('Vui lòng chọn đầy đủ thông tin')
      return
    }

    setIsGenerating(true)
    setGenerationStep('generating')

    try {
      const token = tokenManager.getAccessToken()
      if (!token) {
        throw new Error('Không tìm thấy token xác thực')
      }

      // Step 1: Generate AI document
      toast.info('Đang tạo hồ sơ bằng AI...')
      const validEquipmentItems = equipmentItems.filter(item => item.equipmentId)
      
      const formData = new FormData()
      formData.append('fileId', selectedFile)
      formData.append('equipmentItems', JSON.stringify(validEquipmentItems))

      const aiResponse = await fetch('/api/bidding-documents/ai-profile', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      })

      if (!aiResponse.ok) {
        throw new Error('Lỗi khi tạo hồ sơ AI')
      }

      const aiResult = await aiResponse.json()
      
      if (!aiResult.success || !aiResult.result) {
        throw new Error('Không thể tạo hồ sơ AI')
      }

      // Step 2: Download and upload to Google Drive
      setGenerationStep('uploading')
      toast.info('Đang tải file lên Google Drive...')
      
      const downloadResponse = await fetch(aiResult.result.downloadUrl)
      if (!downloadResponse.ok) {
        throw new Error('Không thể tải file Excel')
      }
      
      const blob = await downloadResponse.blob()
      const file = new File([blob], aiResult.result.excelFileName, {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      
      const uploadFormData = new FormData()
      uploadFormData.append('file', file)
      
      const uploadResponse = await fetch('/api/google-drive/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: uploadFormData
      })
      
      if (!uploadResponse.ok) {
        throw new Error('Không thể tải lên Google Drive')
      }
      
      const uploadResult = await uploadResponse.json()
      
      // Step 3: Save as attachment
      setGenerationStep('saving')
      toast.info('Đang lưu hồ sơ...')
      
      const attachmentData = {
        fileName: aiResult.result.excelFileName,
        fileUrl: uploadResult.webViewLink || '',
        fileSize: file.size,
        mimeType: uploadResult.mimeType || 'application/vnd.google-apps.spreadsheet',
        source: 'google_drive' as const,
        googleDriveId: uploadResult.fileId,
        googleDriveUrl: uploadResult.editUrl || uploadResult.webViewLink
      }
      
      const attachmentResponse = await fetch(`/api/bidding-documents/${selectedDocument}/attachments/batch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ attachments: [attachmentData] })
      })
      
      if (!attachmentResponse.ok) {
        throw new Error('Không thể lưu hồ sơ')
      }
      
      setGenerationStep('complete')
      toast.success('Tạo hồ sơ AI thành công!')
      
      // Reset form
      setTimeout(() => {
        setSelectedDocument('')
        setSelectedFile('')
        setEquipmentItems([{ equipmentId: '' }])
        setGenerationStep('idle')
        onGenerationComplete?.()
      }, 2000)
      
    } catch (error) {
      console.error('Error generating AI document:', error)
      toast.error(error instanceof Error ? error.message : 'Có lỗi xảy ra')
      setGenerationStep('idle')
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Instructions */}
      <Card className="p-6 bg-purple-50 border-purple-200">
        <h3 className="text-lg font-semibold text-purple-900 mb-3">Hướng dẫn tạo hồ sơ AI</h3>
        <ol className="space-y-2 text-sm text-purple-700">
          <li className="flex items-start gap-2">
            <span className="font-semibold">1.</span>
            <span>Chọn hồ sơ dự thầu có chứa file PDF nguồn</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="font-semibold">2.</span>
            <span>Chọn file PDF từ danh sách tài liệu đính kèm</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="font-semibold">3.</span>
            <span>Thêm danh sách thiết bị và phạm vi trang (tùy chọn)</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="font-semibold">4.</span>
            <span>Nhấn "Tạo hồ sơ AI" và chờ hệ thống xử lý</span>
          </li>
        </ol>
      </Card>

      {/* Form */}
      <div className="space-y-6">
        {/* Step 1: Select Document */}
        <Card className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="font-semibold text-blue-600">1</span>
            </div>
            <h3 className="text-lg font-medium">Chọn hồ sơ dự thầu</h3>
          </div>
          
          <Select
            value={selectedDocument}
            onChange={(e) => {
              setSelectedDocument(e.target.value)
              setSelectedFile('') // Reset file selection
            }}
            className="w-full h-11 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
          >
            <option value="">-- Chọn hồ sơ dự thầu --</option>
            {documents.map(doc => (
              <option key={doc.id} value={doc.id}>
                {doc.code} - {doc.name} {doc.customerName && `(${doc.customerName})`}
              </option>
            ))}
          </Select>
        </Card>

        {/* Step 2: Select PDF File */}
        {selectedDocument && (
          <Card className="p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="font-semibold text-blue-600">2</span>
              </div>
              <h3 className="text-lg font-medium">Chọn file PDF</h3>
            </div>
            
            {pdfFiles.length === 0 ? (
              <div className="text-center py-8 bg-gray-50 rounded-lg">
                <FileText className="w-12 h-12 mx-auto text-gray-400 mb-3" />
                <p className="text-gray-600">Hồ sơ này chưa có file PDF nào</p>
                <p className="text-sm text-gray-500 mt-1">Vui lòng tải lên file PDF trước khi tạo hồ sơ AI</p>
              </div>
            ) : (
              <Select
                value={selectedFile}
                onChange={(e) => setSelectedFile(e.target.value)}
                className="w-full h-11 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
              >
                <option value="">-- Chọn file PDF --</option>
                {pdfFiles.map(file => (
                  <option key={file.id} value={file.id}>
                    {file.fileName}
                  </option>
                ))}
              </Select>
            )}
          </Card>
        )}

        {/* Step 3: Select Equipment */}
        {selectedFile && (
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="font-semibold text-blue-600">3</span>
                </div>
                <h3 className="text-lg font-medium">Danh sách thiết bị</h3>
              </div>
              <Button
                type="button"
                size="sm"
                variant="outline"
                onClick={handleAddEquipment}
                className="flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Thêm thiết bị
              </Button>
            </div>
            
            <div className="space-y-3">
              {equipmentItems.map((item, index) => (
                <div key={index} className="flex gap-2 items-start border rounded-lg p-3">
                  <div className="flex-1">
                    <Select
                      value={item.equipmentId}
                      onChange={(e) => handleEquipmentChange(index, e.target.value)}
                      className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500"
                    >
                      <option value="">Chọn thiết bị</option>
                      {equipments.map((equipment) => (
                        <option key={equipment.id} value={equipment.id}>
                          {equipment.name} - {equipment.equipmentCode}
                        </option>
                      ))}
                    </Select>
                  </div>

                  <div className="flex gap-2 items-center">
                    <input
                      type="number"
                      placeholder="Từ trang"
                      className="w-24 h-10 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500"
                      min="1"
                      value={item.pageRange?.from || ''}
                      onChange={(e) => handlePageRangeChange(index, 'from', e.target.value)}
                    />
                    <span className="text-sm">-</span>
                    <input
                      type="number"
                      placeholder="Đến trang"
                      className="w-24 h-10 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500"
                      min="1"
                      value={item.pageRange?.to || ''}
                      onChange={(e) => handlePageRangeChange(index, 'to', e.target.value)}
                    />
                  </div>

                  <Button
                    type="button"
                    size="sm"
                    variant="ghost"
                    onClick={() => handleRemoveEquipment(index)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
          </Card>
        )}

        {/* Generate Button */}
        {selectedFile && equipmentItems.some(item => item.equipmentId) && (
          <Card className="p-6 bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
            <div className="text-center">
              <Button
                onClick={handleGenerate}
                disabled={isGenerating}
                size="lg"
                className="w-full md:w-auto px-8 py-4 bg-purple-600 hover:bg-purple-700 text-white font-medium"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                    Đang xử lý...
                  </>
                ) : (
                  <>
                    <Sparkles className="w-5 h-5 mr-2" />
                    Tạo hồ sơ AI
                  </>
                )}
              </Button>
              
              {/* Progress Steps */}
              {isGenerating && (
                <div className="mt-6 flex items-center justify-center gap-2">
                  <Step 
                    label="Tạo AI" 
                    isActive={generationStep === 'generating'} 
                    isComplete={['uploading', 'saving', 'complete'].includes(generationStep)} 
                  />
                  <ArrowRight className="w-4 h-4 text-gray-400" />
                  <Step 
                    label="Tải lên" 
                    isActive={generationStep === 'uploading'} 
                    isComplete={['saving', 'complete'].includes(generationStep)} 
                  />
                  <ArrowRight className="w-4 h-4 text-gray-400" />
                  <Step 
                    label="Lưu" 
                    isActive={generationStep === 'saving'} 
                    isComplete={generationStep === 'complete'} 
                  />
                </div>
              )}
              
              {generationStep === 'complete' && (
                <div className="mt-6 p-4 bg-green-100 rounded-lg">
                  <CheckCircle className="w-8 h-8 text-green-600 mx-auto mb-2" />
                  <p className="text-green-800 font-medium">Tạo hồ sơ AI thành công!</p>
                </div>
              )}
            </div>
          </Card>
        )}
      </div>
    </div>
  )
}

function Step({ label, isActive, isComplete }: { label: string; isActive: boolean; isComplete: boolean }) {
  return (
    <div className="flex items-center gap-2">
      <div className={`
        w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
        ${isComplete ? 'bg-green-500 text-white' : isActive ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-600'}
      `}>
        {isComplete ? <CheckCircle className="w-4 h-4" /> : isActive ? <Loader2 className="w-4 h-4 animate-spin" /> : '•'}
      </div>
      <span className={`text-sm ${isActive ? 'text-purple-600 font-medium' : isComplete ? 'text-green-600' : 'text-gray-500'}`}>
        {label}
      </span>
    </div>
  )
}