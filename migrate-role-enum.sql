-- Step 1: Create new enum type with only ADMIN and USER
CREATE TYPE "Role_new" AS ENUM ('ADMIN', 'USER');

-- Step 2: Update existing data - convert old roles to appropriate new roles
-- First, let's update all non-ADMIN users to have a temporary NULL value
ALTER TABLE "User" ALTER COLUMN role DROP DEFAULT;
ALTER TABLE "User" ALTER COLUMN role TYPE VARCHAR(50) USING role::text;

-- Step 3: Update the role values
UPDATE "User" 
SET role = 'USER' 
WHERE role IN ('MANAGER', 'STAFF', 'CONTRACTOR');

-- Keep ADMIN as ADMIN (no change needed)

-- Step 4: Drop the old enum type
DROP TYPE "Role";

-- Step 5: Rename the new enum to the original name
ALTER TYPE "Role_new" RENAME TO "Role";

-- Step 6: Convert column back to use the new enum
ALTER TABLE "User" 
ALTER COLUMN role TYPE "Role" 
USING role::"Role";

-- Step 7: Set default value back
ALTER TABLE "User" 
ALTER COLUMN role SET DEFAULT 'USER'::"Role";

-- Verify the changes
SELECT role, COUNT(*) 
FROM "User" 
GROUP BY role;