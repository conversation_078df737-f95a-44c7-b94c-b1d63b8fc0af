import { NextRequest } from 'next/server'
import { cookies } from 'next/headers'

export async function getTokenFromRequest(request: NextRequest): Promise<string | null> {
  // First, check the Authorization header
  const authHeader = request.headers.get('Authorization')
  if (authHeader?.startsWith('Bearer ')) {
    return authHeader.substring(7)
  }

  // Then check cookies
  const cookieStore = await cookies()
  const token = cookieStore.get('auth-token')?.value
  
  return token || null
}