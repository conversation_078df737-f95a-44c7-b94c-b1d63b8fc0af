import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/db'
import { getServerSession } from '@/lib/auth-server'

// Validation schema
const checkCodeSchema = z.object({
  code: z.string()
    .min(1, 'Code is required')
    .regex(/^[A-Z0-9_]+$/, 'Code must contain only uppercase letters, numbers, and underscores'),
})

// POST /api/catalogs/check-code - Check if catalog code exists
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(request)
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validation = checkCodeSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid data', details: validation.error.flatten() },
        { status: 400 }
      )
    }

    const { code } = validation.data

    // Check if code exists
    const catalog = await prisma.catalog.findUnique({
      where: { catalogCode: code },
      select: {
        id: true,
        catalogCode: true,
        catalogName: true,
        status: true,
      }
    })

    return NextResponse.json({
      exists: !!catalog,
      catalog: catalog || undefined,
    })
  } catch (error) {
    console.error('POST /api/catalogs/check-code error:', error)
    return NextResponse.json(
      { error: 'Failed to check catalog code' },
      { status: 500 }
    )
  }
}