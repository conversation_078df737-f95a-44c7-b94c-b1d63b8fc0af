'use client'

import React from 'react'
import { AlertTriangle } from 'lucide-react'
import { Dialog } from '@/components/ui/Dialog'
import { Button } from '@/components/ui/Button'
import { useDeleteUser } from '@/hooks/queries/useUsers'
import type { User } from '@/types/user'

interface DeleteUserDialogProps {
  isOpen: boolean
  onClose: () => void
  user: User | null
}

export function DeleteUserDialog({ isOpen, onClose, user }: DeleteUserDialogProps) {
  const deleteMutation = useDeleteUser()

  const handleDelete = async () => {
    if (!user) return

    try {
      await deleteMutation.mutateAsync(user.id)
      onClose()
    } catch (error) {
      console.error('Deactivate error:', error)
    }
  }

  if (!user) return null

  return (
    <Dialog isOpen={isOpen} onClose={onClose}>
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full">
        <div className="flex items-center mb-4">
          <div className="flex-shrink-0 w-12 h-12 rounded-full bg-orange-100 dark:bg-orange-900 flex items-center justify-center">
            <AlertTriangle className="h-6 w-6 text-orange-600 dark:text-orange-400" />
          </div>
          <h3 className="ml-3 text-lg font-medium text-gray-900 dark:text-gray-100">
            Vô hiệu hóa người dùng
          </h3>
        </div>
        
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
          Bạn có chắc chắn muốn vô hiệu hóa người dùng <strong>{user.name}</strong> (@{user.username})? 
          Người dùng sẽ không thể đăng nhập vào hệ thống.
        </p>

        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-3 mb-4">
          <p className="text-sm text-yellow-800 dark:text-yellow-200">
            <strong>Lưu ý:</strong> Người dùng có thể được kích hoạt lại bất cứ lúc nào.
          </p>
        </div>

        <div className="flex justify-end space-x-3">
          <Button
            variant="secondary"
            onClick={onClose}
            disabled={deleteMutation.isPending}
          >
            Hủy
          </Button>
          <Button
            variant="danger"
            onClick={handleDelete}
            disabled={deleteMutation.isPending}
          >
            {deleteMutation.isPending ? 'Đang xử lý...' : 'Vô hiệu hóa'}
          </Button>
        </div>
      </div>
    </Dialog>
  )
}