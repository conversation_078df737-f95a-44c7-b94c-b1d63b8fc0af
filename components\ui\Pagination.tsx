'use client';

import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, ChevronUp } from 'lucide-react';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalRecords: number;
  pageSize: number;
  pageSizeOptions?: number[];
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  className?: string;
}

export const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  totalRecords,
  pageSize,
  pageSizeOptions = [10, 20, 50, 100],
  onPageChange,
  onPageSizeChange,
  className = '',
}) => {
  const [isPageSizeOpen, setIsPageSizeOpen] = useState(false);
  
  // Safety checks for props
  const safeCurrentPage = currentPage || 1;
  const safePageSize = pageSize || 10;
  const safeTotalRecords = totalRecords || 0;
  const safeTotalPages = totalPages || 1;

  const getVisiblePages = () => {
    if (safeTotalPages <= 1) {
      return [1];
    }

    const delta = 2;
    const rangeWithDots = [];

    // Tính toán range các trang hiển thị
    const start = Math.max(1, safeCurrentPage - delta);
    const end = Math.min(safeTotalPages, safeCurrentPage + delta);

    // Thêm trang đầu nếu cần
    if (start > 1) {
      rangeWithDots.push(1);
      if (start > 2) {
        rangeWithDots.push('...');
      }
    }

    // Thêm các trang trong range
    for (let i = start; i <= end; i++) {
      rangeWithDots.push(i);
    }

    // Thêm trang cuối nếu cần
    if (end < safeTotalPages) {
      if (end < safeTotalPages - 1) {
        rangeWithDots.push('...');
      }
      rangeWithDots.push(safeTotalPages);
    }

    return rangeWithDots;
  };

  const handlePageSizeChange = (newPageSize: number) => {
    onPageSizeChange(newPageSize);
    setIsPageSizeOpen(false);
  };

  // Tính toán thông tin hiển thị
  const startRecord = safeTotalRecords > 0 ? (safeCurrentPage - 1) * safePageSize + 1 : 0;
  const endRecord = Math.min(safeCurrentPage * safePageSize, safeTotalRecords);

  return (
    <div className={`flex items-center justify-between px-6 py-4 bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Left side - Total records and page size selector */}
      <div className="flex items-center space-x-4">
        <span className="text-sm text-gray-600 dark:text-gray-400">
          Hiển thị {startRecord.toLocaleString()} - {endRecord.toLocaleString()} của {safeTotalRecords.toLocaleString()} bản ghi
        </span>
        
        <div className="relative">
          <button
            onClick={() => setIsPageSizeOpen(!isPageSizeOpen)}
            className="flex items-center space-x-1 px-3 py-1 text-sm text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <span>{safePageSize} / Trang</span>
            <ChevronUp className={`w-4 h-4 transition-transform ${isPageSizeOpen ? 'rotate-180' : ''}`} />
          </button>
          
          {isPageSizeOpen && (
            <div className="absolute bottom-full mb-1 left-0 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg z-10 min-w-[80px]">
              {pageSizeOptions.map((option) => (
                <button
                  key={option}
                  onClick={() => handlePageSizeChange(option)}
                  className={`w-full px-3 py-2 text-sm text-left hover:bg-gray-50 dark:hover:bg-gray-700 ${
                    safePageSize === option ? 'bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-400' : 'text-gray-700 dark:text-gray-300'
                  }`}
                >
                  {option} / Trang
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Right side - Pagination controls */}
      {safeTotalPages > 1 && (
        <div className="flex items-center space-x-1">
          {/* Previous button */}
          <button
            onClick={() => onPageChange(safeCurrentPage - 1)}
            disabled={safeCurrentPage === 1}
            className="min-w-[36px] h-9 px-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 disabled:opacity-50 disabled:cursor-not-allowed rounded-md border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center justify-center"
          >
            <ChevronLeft className="w-4 h-4" />
          </button>

          {/* Page numbers */}
          <div className="flex items-center space-x-1">
            {getVisiblePages().map((page, index) => (
              <React.Fragment key={index}>
                {page === '...' ? (
                  <span className="min-w-[36px] h-9 px-3 py-2 text-gray-500 dark:text-gray-400 flex items-center justify-center">...</span>
                ) : (
                  <button
                    onClick={() => onPageChange(page as number)}
                    className={`min-w-[36px] h-9 px-3 py-2 text-sm font-medium rounded-md transition-colors flex items-center justify-center ${
                      safeCurrentPage === page
                        ? 'bg-blue-600 text-white shadow-sm'
                        : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600'
                    }`}
                  >
                    {page}
                  </button>
                )}
              </React.Fragment>
            ))}
          </div>

          {/* Next button */}
          <button
            onClick={() => onPageChange(safeCurrentPage + 1)}
            disabled={safeCurrentPage === safeTotalPages}
            className="min-w-[36px] h-9 px-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 disabled:opacity-50 disabled:cursor-not-allowed rounded-md border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center justify-center"
          >
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
      )}
    </div>
  );
};

export default Pagination; 