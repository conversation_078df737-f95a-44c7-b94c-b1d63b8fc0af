import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { catalogService } from '@/services/catalogService'
import { useToast } from '@/hooks/useToast'
import type { 
  CatalogFilters, 
  CreateCatalogRequest, 
  UpdateCatalogRequest,
  Equipment,
  EquipmentFilters,
  CreateEquipmentRequest,
  UpdateEquipmentRequest
} from '@/types/catalog'

// Query key factory
export const catalogKeys = {
  all: ['catalogs'] as const,
  lists: () => [...catalogKeys.all, 'list'] as const,
  list: (filters: CatalogFilters) => [...catalogKeys.lists(), filters] as const,
  details: () => [...catalogKeys.all, 'detail'] as const,
  detail: (id: string) => [...catalogKeys.details(), id] as const,
  checkCode: (code: string) => [...catalogKeys.all, 'check-code', code] as const,
  equipments: (catalogId: string) => [...catalogKeys.all, catalogId, 'equipments'] as const,
}

export const equipmentKeys = {
  all: ['equipments'] as const,
  lists: () => [...equipmentKeys.all, 'list'] as const,
  list: (filters: EquipmentFilters) => [...equipmentKeys.lists(), filters] as const,
  details: () => [...equipmentKeys.all, 'detail'] as const,
  detail: (id: string) => [...equipmentKeys.details(), id] as const,
}

// Catalog hooks
export function useCatalogs(filters: CatalogFilters = {}, enabled = true) {
  return useQuery({
    queryKey: catalogKeys.list(filters),
    queryFn: ({ signal }) => catalogService.getCatalogs(filters, signal),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useCatalog(id: string, enabled = true) {
  return useQuery({
    queryKey: catalogKeys.detail(id),
    queryFn: ({ signal }) => catalogService.getCatalogById(id, signal),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  })
}

export function useCheckCatalogCode(code: string, enabled = false) {
  return useQuery({
    queryKey: catalogKeys.checkCode(code),
    queryFn: () => catalogService.checkCatalogCode(code),
    enabled: enabled && !!code && code.length > 0,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 60 * 1000, // 1 minute
  })
}

export function useCatalogEquipments(catalogId: string, enabled = true) {
  return useQuery({
    queryKey: catalogKeys.equipments(catalogId),
    queryFn: ({ signal }) => catalogService.getCatalogEquipments(catalogId, signal),
    enabled: enabled && !!catalogId,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  })
}

export function useCreateCatalog() {
  const queryClient = useQueryClient()
  const { success, error } = useToast()

  return useMutation({
    mutationFn: (data: CreateCatalogRequest) => catalogService.createCatalog(data),
    onSuccess: (newCatalog) => {
      queryClient.invalidateQueries({ queryKey: catalogKeys.lists() })
      success('Catalog created successfully')
    },
    onError: (err: any) => {
      const message = err?.message || 'Failed to create catalog'
      error(message)
      console.error('Create catalog error:', err)
    },
  })
}

export function useUpdateCatalog() {
  const queryClient = useQueryClient()
  const { success, error } = useToast()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateCatalogRequest }) => 
      catalogService.updateCatalog(id, data),
    onSuccess: (updatedCatalog) => {
      queryClient.invalidateQueries({ queryKey: catalogKeys.lists() })
      queryClient.invalidateQueries({ queryKey: catalogKeys.detail(updatedCatalog.id) })
      success('Catalog updated successfully')
    },
    onError: (err: any) => {
      const message = err?.message || 'Failed to update catalog'
      error(message)
      console.error('Update catalog error:', err)
    },
  })
}

export function useDeleteCatalog() {
  const queryClient = useQueryClient()
  const { success, error } = useToast()

  return useMutation({
    mutationFn: ({ id, force = false }: { id: string; force?: boolean }) => 
      catalogService.deleteCatalog(id, force),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: catalogKeys.lists() })
      success(variables.force ? 'Catalog disabled successfully' : 'Catalog deleted successfully')
    },
    onError: (err: any) => {
      const message = err?.message || 'Failed to delete catalog'
      error(message)
      console.error('Delete catalog error:', err)
    },
  })
}

// Equipment hooks
export function useEquipments(filters: EquipmentFilters = {}, enabled = true) {
  return useQuery({
    queryKey: equipmentKeys.list(filters),
    queryFn: ({ signal }) => catalogService.getEquipments(filters, signal),
    enabled,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  })
}

export function useEquipment(id: string, enabled = true) {
  return useQuery({
    queryKey: equipmentKeys.detail(id),
    queryFn: ({ signal }) => catalogService.getEquipmentById(id, signal),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  })
}

export function useCreateEquipment() {
  const queryClient = useQueryClient()
  const { success, error } = useToast()

  return useMutation({
    mutationFn: (data: CreateEquipmentRequest) => catalogService.createEquipment(data),
    onSuccess: (newEquipment) => {
      queryClient.invalidateQueries({ queryKey: equipmentKeys.lists() })
      queryClient.invalidateQueries({ queryKey: catalogKeys.equipments(newEquipment.catalogId) })
      queryClient.invalidateQueries({ queryKey: catalogKeys.lists() }) // Update equipment count
      success('Equipment created successfully')
    },
    onError: (err: any) => {
      const message = err?.message || 'Failed to create equipment'
      error(message)
      console.error('Create equipment error:', err)
    },
  })
}

export function useUpdateEquipment() {
  const queryClient = useQueryClient()
  const { success, error } = useToast()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateEquipmentRequest }) => 
      catalogService.updateEquipment(id, data),
    onSuccess: (updatedEquipment) => {
      queryClient.invalidateQueries({ queryKey: equipmentKeys.lists() })
      queryClient.invalidateQueries({ queryKey: equipmentKeys.detail(updatedEquipment.id) })
      if (updatedEquipment.catalogId) {
        queryClient.invalidateQueries({ queryKey: catalogKeys.equipments(updatedEquipment.catalogId) })
      }
      success('Equipment updated successfully')
    },
    onError: (err: any) => {
      const message = err?.message || 'Failed to update equipment'
      error(message)
      console.error('Update equipment error:', err)
    },
  })
}

export function useDeleteEquipment() {
  const queryClient = useQueryClient()
  const { success, error } = useToast()

  return useMutation({
    mutationFn: (id: string) => catalogService.deleteEquipment(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: equipmentKeys.lists() })
      queryClient.invalidateQueries({ queryKey: catalogKeys.lists() }) // Update equipment count
      success('Equipment deleted successfully')
    },
    onError: (err: any) => {
      const message = err?.message || 'Failed to delete equipment'
      error(message)
      console.error('Delete equipment error:', err)
    },
  })
}

// Bulk operations
export function useBulkDeleteCatalogs() {
  const queryClient = useQueryClient()
  const { success, error } = useToast()

  return useMutation({
    mutationFn: (ids: string[]) => catalogService.bulkDeleteCatalogs(ids),
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: catalogKeys.lists() })
      success(`Deleted ${result.success} catalogs${result.failed > 0 ? `, ${result.failed} failed` : ''}`)
    },
    onError: (err: any) => {
      const message = err?.message || 'Failed to delete catalogs'
      error(message)
      console.error('Bulk delete catalogs error:', err)
    },
  })
}

// Export/Import operations
export function useExportCatalogs() {
  const { success, error } = useToast()

  return useMutation({
    mutationFn: (filters: CatalogFilters = {}) => catalogService.exportCatalogs(filters),
    onSuccess: (blob) => {
      // Create download link
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `catalogs_${new Date().toISOString().split('T')[0]}.xlsx`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      
      success('Catalogs exported successfully')
    },
    onError: (err: any) => {
      const message = err?.message || 'Failed to export catalogs'
      error(message)
      console.error('Export catalogs error:', err)
    },
  })
}

export function useImportCatalogs() {
  const queryClient = useQueryClient()
  const { success, error } = useToast()

  return useMutation({
    mutationFn: (file: File) => catalogService.importCatalogs(file),
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: catalogKeys.lists() })
      const message = `Successfully imported ${result.success} catalogs`
      if (result.failed > 0) {
        error(`${message}, ${result.failed} failed`)
      } else {
        success(message)
      }
    },
    onError: (err: any) => {
      const message = err?.message || 'Failed to import catalogs'
      error(message)
      console.error('Import catalogs error:', err)
    },
  })
}