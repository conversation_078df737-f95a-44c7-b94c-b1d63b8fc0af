import type { User } from './auth'

export interface Catalog {
  id: string
  catalogCode: string
  catalogName: string
  description?: string
  status: 'ACTIVE' | 'INACTIVE'
  equipmentCount?: number
  createdBy?: string
  creator?: User
  createdAt: string
  updatedAt: string
}

export interface Equipment {
  id: string
  equipmentCode: string
  name: string
  description?: string
  model?: string
  manufacturer?: string
  unit?: string
  price?: number
  specifications?: any
  catalogId: string
  catalog?: Catalog
  status: 'ACTIVE' | 'INACTIVE'
  createdBy?: string
  creator?: User
  createdAt: string
  updatedAt: string
}

export interface CatalogFilters {
  search?: string
  status?: 'ACTIVE' | 'INACTIVE'
  page?: number
  limit?: number
  sortBy?: 'catalogCode' | 'catalogName' | 'createdAt'
  sortOrder?: 'asc' | 'desc'
}

export interface CatalogsResponse {
  catalogs: Catalog[]
  total: number
  page: number
  totalPages: number
}

export interface CreateCatalogRequest {
  catalogCode: string
  catalogName: string
  description?: string
}

export interface UpdateCatalogRequest {
  catalogName: string
  description?: string
  status?: 'ACTIVE' | 'INACTIVE'
}

export interface CheckCodeResponse {
  exists: boolean
  catalog?: Catalog
}

export interface EquipmentFilters {
  search?: string
  catalogId?: string
  status?: 'ACTIVE' | 'INACTIVE'
  page?: number
  limit?: number
  sortBy?: 'equipmentCode' | 'name' | 'createdAt'
  sortOrder?: 'asc' | 'desc'
}

export interface EquipmentsResponse {
  equipments: Equipment[]
  total: number
  page: number
  totalPages: number
}

export interface CreateEquipmentRequest {
  equipmentCode: string
  name: string
  description?: string
  model?: string
  manufacturer?: string
  unit?: string
  price?: number
  specifications?: any
  catalogId: string
}

export interface UpdateEquipmentRequest {
  name?: string
  description?: string
  model?: string
  manufacturer?: string
  unit?: string
  price?: number
  specifications?: any
  catalogId?: string
  status?: 'ACTIVE' | 'INACTIVE'
}