import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { prisma } from '@/lib/db'
import {
  verifyRefreshToken,
  generateAccessToken,
  generateRefreshToken,
  generateSessionToken,
  getExpirationDate,
  getClientIp,
  getUserAgent,
  AuditAction,
  AUTH_COOKIE_OPTIONS,
  REFRESH_COOKIE_OPTIONS,
} from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const refreshTokenCookie = cookieStore.get('refresh-token')?.value
    const sessionId = cookieStore.get('session-id')?.value

    if (!refreshTokenCookie || !sessionId) {
      return NextResponse.json(
        { error: 'No refresh token found' },
        { status: 401 }
      )
    }

    // Verify the refresh token
    const payload = await verifyRefreshToken(refreshTokenCookie)
    
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid or expired refresh token' },
        { status: 401 }
      )
    }

    // Find the session (only active ones)
    const session = await prisma.session.findFirst({
      where: { 
        id: sessionId,
        status: 'ACTIVE',
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            email: true,
            name: true,
            role: true,
            department: true,
            phone: true,
            avatar: true,
            status: true,
            emailVerified: true,
            twoFactorEnabled: true,
          },
        },
      },
    })

    if (!session) {
      // Clear invalid cookies
      const response = NextResponse.json(
        { error: 'Session not found' },
        { status: 401 }
      )
      
      response.cookies.delete('refresh-token')
      response.cookies.delete('session-id')
      
      return response
    }

    // Check if session is expired
    if (session.expiresAt < new Date()) {
      // Mark expired session instead of deleting
      await prisma.session.update({
        where: { id: sessionId },
        data: {
          status: 'EXPIRED',
          updatedAt: new Date(),
        },
      })

      const response = NextResponse.json(
        { error: 'Session expired' },
        { status: 401 }
      )
      
      response.cookies.delete('refresh-token')
      response.cookies.delete('session-id')
      
      return response
    }

    // Verify the refresh token matches the session
    if (session.refreshToken !== refreshTokenCookie) {
      // Token mismatch - mark session as expired
      await prisma.session.update({
        where: { id: sessionId },
        data: {
          status: 'EXPIRED',
          updatedAt: new Date(),
        },
      })

      await prisma.auditLog.create({
        data: {
          userId: session.userId,
          action: AuditAction.SESSION_EXPIRED,
          details: { reason: 'Refresh token mismatch' },
          ipAddress: getClientIp(request),
        },
      })

      const response = NextResponse.json(
        { error: 'Invalid refresh token' },
        { status: 401 }
      )
      
      response.cookies.delete('refresh-token')
      response.cookies.delete('session-id')
      
      return response
    }

    // Check if user is still active
    if (session.user.status !== 'ACTIVE') {
      await prisma.session.update({
        where: { id: sessionId },
        data: {
          status: 'EXPIRED',
          updatedAt: new Date(),
        },
      })

      const response = NextResponse.json(
        { error: 'Account is inactive' },
        { status: 403 }
      )
      
      response.cookies.delete('refresh-token')
      response.cookies.delete('session-id')
      
      return response
    }

    // Generate new tokens (token rotation)
    const newSessionToken = generateSessionToken()
    const sessionDuration = process.env.SESSION_DURATION || '8h'
    const refreshDuration = process.env.REFRESH_DURATION || '7d'

    const newAccessToken = await generateAccessToken({
      userId: session.user.id,
      username: session.user.username,
      role: session.user.role,
      sessionId: session.id,
    })

    const newRefreshToken = await generateRefreshToken({
      userId: session.user.id,
      sessionId: session.id,
    })

    // Update session with new tokens
    await prisma.session.update({
      where: { id: sessionId },
      data: {
        token: newSessionToken,
        refreshToken: newRefreshToken, // Store the actual new refresh token
        expiresAt: getExpirationDate(sessionDuration),
        ipAddress: getClientIp(request),
        userAgent: getUserAgent(request),
        updatedAt: new Date(),
      },
    })

    // Log token refresh
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: AuditAction.TOKEN_REFRESHED,
        details: { sessionId: session.id },
        ipAddress: getClientIp(request),
      },
    })

    // Set only new refresh token as HttpOnly cookie (token rotation)
    cookieStore.set('refresh-token', newRefreshToken, REFRESH_COOKIE_OPTIONS)

    // Return only access token in response body
    return NextResponse.json({
      user: session.user,
      accessToken: newAccessToken,
      expiresIn: 600, // 10 minutes in seconds
    })
  } catch (error) {
    console.error('Token refresh error:', error)
    return NextResponse.json(
      { error: 'Failed to refresh token' },
      { status: 500 }
    )
  }
}