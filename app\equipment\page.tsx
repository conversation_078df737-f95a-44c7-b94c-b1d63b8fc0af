'use client'

import React, { useState } from 'react'
import { Plus, Download, Upload } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/MainLayout'
import { EquipmentTable } from '@/components/equipment/EquipmentTable'
import { EquipmentFilters } from '@/components/equipment/EquipmentFilters'
import { Pagination } from '@/components/ui/Pagination'
import { Button } from '@/components/ui/Button'
import { Dialog } from '@/components/ui/Dialog'
import { useAuth } from '@/hooks/queries/useAuth'
import { useEquipments, useExportEquipment, useImportEquipment } from '@/hooks/queries/useEquipment'
import { useDebounce } from '@/hooks/useDebounce'
import type { EquipmentFilter } from '@/types/equipment'

export default function EquipmentPage() {
  const router = useRouter()
  const { user } = useAuth()
  const canEdit = user?.role === 'ADMIN' || user?.role === 'USER'
  
  const [filters, setFilters] = useState<EquipmentFilter>({
    page: 1,
    limit: 10,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  })
  
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCatalog, setSelectedCatalog] = useState('')
  const [selectedStatus, setSelectedStatus] = useState<'ACTIVE' | 'INACTIVE' | ''>('')
  const [selectedManufacturer, setSelectedManufacturer] = useState('')
  
  const debouncedSearch = useDebounce(searchTerm, 500)
  
  const [importDialogOpen, setImportDialogOpen] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)

  const { data, isLoading } = useEquipments({
    ...filters,
    search: debouncedSearch,
    catalogId: selectedCatalog,
    manufacturer: selectedManufacturer,
    status: selectedStatus || undefined
  })
  
  const exportEquipmentMutation = useExportEquipment()
  const importEquipmentMutation = useImportEquipment()

  const handlePageChange = (page: number) => {
    setFilters({ ...filters, page })
  }
  
  const handlePageSizeChange = (limit: number) => {
    setFilters({ ...filters, limit, page: 1 })
  }
  
  const handleSort = (column: 'equipmentCode' | 'name' | 'createdAt') => {
    setFilters(prev => ({
      ...prev,
      sortBy: column,
      sortOrder: prev.sortBy === column && prev.sortOrder === 'asc' ? 'desc' : 'asc',
      page: 1
    }))
  }
  
  const handleClearFilters = () => {
    setSearchTerm('')
    setSelectedCatalog('')
    setSelectedManufacturer('')
    setSelectedStatus('')
    setFilters({
      page: 1,
      limit: 10,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    })
  }
  
  const handleCreate = () => {
    router.push('/equipment/new')
  }

  const handleExport = async () => {
    try {
      const blob = await exportEquipmentMutation.mutateAsync({})
      
      // Create download link
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      const timestamp = new Date().toISOString().split('T')[0].replace(/-/g, '')
      a.href = url
      a.download = `DanhSachThietBi_${timestamp}.xlsx`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Failed to export equipment:', error)
    }
  }

  const handleImport = () => {
    setImportDialogOpen(true)
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setSelectedFile(file)
    }
  }

  const handleImportConfirm = async () => {
    if (!selectedFile) return

    try {
      const result = await importEquipmentMutation.mutateAsync(selectedFile)
      setImportDialogOpen(false)
      setSelectedFile(null)
      
      // Show success message
      let message = `Đã import thành công ${result.imported} thiết bị`
      if (result.failed > 0) {
        message += `, thất bại ${result.failed} thiết bị`
      }
      alert(message)
    } catch (error) {
      console.error('Failed to import equipment:', error)
      alert('Lỗi khi import thiết bị')
    }
  }

  const handleDownloadTemplate = async () => {
    try {
      const response = await fetch('/api/equipment/import', {
        method: 'GET',
        credentials: 'include'
      })
      
      if (!response.ok) throw new Error('Failed to download template')
      
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'Template_Import_ThietBi.xlsx'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Failed to download template:', error)
      alert('Lỗi khi tải template')
    }
  }

  return (
    <MainLayout>
      <div className="p-6">
        {/* Page Header */}
        <div className="mb-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-semibold text-gray-900 dark:text-gray-100">
                Quản lý thiết bị
              </h1>
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                Quản lý danh sách thiết bị và phiên bản thông số kỹ thuật
              </p>
            </div>
            <div className="flex items-center gap-3">
              {canEdit && (
                <>
                  <Button
                    variant="secondary"
                    onClick={handleImport}
                    className="flex items-center gap-2"
                  >
                    <Upload className="w-5 h-5" />
                    Nhập
                  </Button>
                  <Button
                    variant="secondary"
                    onClick={handleExport}
                    disabled={exportEquipmentMutation.isPending}
                    className="flex items-center gap-2"
                  >
                    <Download className="w-5 h-5" />
                    Xuất
                  </Button>
                  <Button
                    variant="primary"
                    onClick={handleCreate}
                    className="flex items-center gap-2"
                  >
                    <Plus className="w-5 h-5" />
                    Thêm thiết bị
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Filters */}
        <EquipmentFilters
          searchTerm={searchTerm}
          selectedCatalog={selectedCatalog}
          selectedManufacturer={selectedManufacturer}
          selectedStatus={selectedStatus}
          manufacturers={data ? Array.from(new Set(data.data.filter(e => e.manufacturer).map(e => e.manufacturer!))) : []}
          onSearchChange={setSearchTerm}
          onCatalogChange={setSelectedCatalog}
          onManufacturerChange={setSelectedManufacturer}
          onStatusChange={setSelectedStatus}
          onClearFilters={handleClearFilters}
        />
        
        {/* Table */}
        <EquipmentTable
          equipments={data?.data || []}
          isLoading={isLoading}
          sortBy={filters.sortBy}
          sortOrder={filters.sortOrder}
          onSort={handleSort}
        />
        
        {/* Pagination */}
        {data && (
          <div className="mt-6">
            <Pagination
              currentPage={data.page}
              totalPages={data.totalPages}
              totalRecords={data.total}
              pageSize={filters.limit || 10}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
            />
          </div>
        )}

        {/* Import Dialog */}
        <Dialog
          open={importDialogOpen}
          onClose={() => {
            setImportDialogOpen(false)
            setSelectedFile(null)
          }}
          title="Nhập thiết bị từ Excel"
          description="Chọn file Excel để nhập danh sách thiết bị"
        >
          <div className="space-y-4 mt-4">
            <div>
              <input
                type="file"
                accept=".xlsx,.xls"
                onChange={handleFileSelect}
                className="w-full"
              />
              {selectedFile && (
                <p className="text-sm text-gray-600 mt-2">
                  Đã chọn: {selectedFile.name}
                </p>
              )}
            </div>
            
            <div className="flex justify-between">
              <Button
                variant="outline"
                onClick={handleDownloadTemplate}
                className="text-sm"
              >
                Tải template mẫu
              </Button>
              
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => {
                    setImportDialogOpen(false)
                    setSelectedFile(null)
                  }}
                >
                  Hủy
                </Button>
                <Button
                  variant="primary"
                  onClick={handleImportConfirm}
                  disabled={!selectedFile || importEquipmentMutation.isPending}
                >
                  {importEquipmentMutation.isPending ? 'Đang nhập...' : 'Nhập'}
                </Button>
              </div>
            </div>
          </div>
        </Dialog>
      </div>
    </MainLayout>
  )
}