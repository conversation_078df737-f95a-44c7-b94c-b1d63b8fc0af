'use client'

import React, { useState } from 'react'
import { X, User, Lock } from 'lucide-react'
import { Dialog } from '@/components/ui/Dialog'
import { ProfileInfoTab } from './ProfileInfoTab'
import { ChangePasswordTab } from './ChangePasswordTab'

interface ProfileDialogProps {
  isOpen: boolean
  onClose: () => void
}

type TabType = 'info' | 'password'

export function ProfileDialog({ isOpen, onClose }: ProfileDialogProps) {
  const [activeTab, setActiveTab] = useState<TabType>('info')

  const tabs = [
    {
      id: 'info' as TabType,
      label: 'Thông tin cá nhân',
      icon: User,
    },
    {
      id: 'password' as TabType,
      label: 'Đổi mật khẩu',
      icon: Lock,
    },
  ]

  return (
    <Dialog isOpen={isOpen} onClose={onClose}>
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Thông tin tài khoản
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <div className="flex">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-6 py-3 text-sm font-medium transition-colors relative ${
                    activeTab === tab.id
                      ? 'text-blue-600 dark:text-blue-400'
                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.label}
                  {activeTab === tab.id && (
                    <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600 dark:bg-blue-400" />
                  )}
                </button>
              )
            })}
          </div>
        </div>

        {/* Tab Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {activeTab === 'info' && <ProfileInfoTab />}
          {activeTab === 'password' && <ChangePasswordTab />}
        </div>
      </div>
    </Dialog>
  )
}