'use client'

import { useState, useRef, useEffect } from 'react'
import { Calendar, ChevronDown, X } from 'lucide-react'
import DatePicker from 'react-datepicker'
import 'react-datepicker/dist/react-datepicker.css'
import { format, subDays, startOfMonth, endOfMonth, startOfWeek, endOfWeek } from 'date-fns'
import { vi } from 'date-fns/locale'

interface DateRange {
  startDate: Date | null
  endDate: Date | null
}

interface EnhancedDateRangePickerProps {
  startDate: Date | null
  endDate: Date | null
  onChange: (dates: [Date | null, Date | null]) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

interface QuickSelectOption {
  label: string
  getValue: () => DateRange
}

const quickSelectOptions: QuickSelectOption[] = [
  {
    label: 'Hôm nay',
    getValue: () => ({
      startDate: new Date(),
      endDate: new Date()
    })
  },
  {
    label: 'Hôm qua',
    getValue: () => {
      const yesterday = subDays(new Date(), 1)
      return {
        startDate: yesterday,
        endDate: yesterday
      }
    }
  },
  {
    label: '7 ngày qua',
    getValue: () => ({
      startDate: subDays(new Date(), 6),
      endDate: new Date()
    })
  },
  {
    label: '30 ngày qua',
    getValue: () => ({
      startDate: subDays(new Date(), 29),
      endDate: new Date()
    })
  },
  {
    label: 'Tháng này',
    getValue: () => ({
      startDate: startOfMonth(new Date()),
      endDate: endOfMonth(new Date())
    })
  },
  {
    label: 'Tuần này',
    getValue: () => ({
      startDate: startOfWeek(new Date(), { locale: vi }),
      endDate: endOfWeek(new Date(), { locale: vi })
    })
  }
]

export function EnhancedDateRangePicker({
  startDate,
  endDate,
  onChange,
  placeholder = "Chọn khoảng thời gian",
  className = "",
  disabled = false
}: EnhancedDateRangePickerProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [activePreset, setActivePreset] = useState<string | null>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }
    
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  const handleDateChange = (dates: [Date | null, Date | null]) => {
    setActivePreset(null)
    onChange(dates)
  }

  const handlePresetClick = (preset: QuickSelectOption) => {
    const { startDate: newStartDate, endDate: newEndDate } = preset.getValue()
    setActivePreset(preset.label)
    onChange([newStartDate, newEndDate])
    setIsOpen(false)
  }

  const clearDates = () => {
    setActivePreset(null)
    onChange([null, null])
  }

  const formatDateRange = () => {
    if (startDate && endDate) {
      if (startDate.toDateString() === endDate.toDateString()) {
        return format(startDate, 'dd/MM/yyyy', { locale: vi })
      }
      return `${format(startDate, 'dd/MM/yyyy', { locale: vi })} - ${format(endDate, 'dd/MM/yyyy', { locale: vi })}`
    } else if (startDate) {
      return `Từ ${format(startDate, 'dd/MM/yyyy', { locale: vi })}`
    }
    return placeholder
  }

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* Main Input Button */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`
          w-full h-10 px-3 py-2 text-left border rounded-lg transition-all duration-200 
          ${disabled 
            ? 'bg-gray-100 dark:bg-gray-700 cursor-not-allowed text-gray-400' 
            : 'bg-white dark:bg-gray-800 hover:border-gray-400 dark:hover:border-gray-500 cursor-pointer'
          }
          ${isOpen 
            ? 'border-blue-500 ring-2 ring-blue-100 dark:ring-blue-900/30' 
            : 'border-gray-300 dark:border-gray-600'
          }
          ${(startDate || endDate) 
            ? 'text-gray-900 dark:text-gray-100' 
            : 'text-gray-500 dark:text-gray-400'
          }
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
        `}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4 text-gray-400" />
            <span className="text-sm font-medium">
              {formatDateRange()}
            </span>
          </div>
          <div className="flex items-center gap-1">
            {(startDate || endDate) && !disabled && (
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation()
                  clearDates()
                }}
                className="p-0.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <X className="w-3 h-3 text-gray-400 hover:text-gray-600" />
              </button>
            )}
            <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
          </div>
        </div>
      </button>

      {/* Dropdown Content */}
      {isOpen && !disabled && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-xl z-50 overflow-hidden">
          <div className="p-4">
            {/* Quick Select Buttons */}
            <div className="mb-4">
              <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                Lựa chọn nhanh
              </h4>
              <div className="grid grid-cols-2 gap-2">
                {quickSelectOptions.map((preset) => (
                  <button
                    key={preset.label}
                    type="button"
                    onClick={() => handlePresetClick(preset)}
                    className={`
                      px-3 py-2 text-sm rounded-lg transition-all duration-200
                      ${activePreset === preset.label
                        ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700'
                        : 'bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 border border-transparent'
                      }
                      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1
                    `}
                  >
                    {preset.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Separator */}
            <div className="border-t border-gray-200 dark:border-gray-700 mb-4"></div>

            {/* Calendar */}
            <div className="enhanced-date-picker">
              <DatePicker
                selectsRange
                startDate={startDate}
                endDate={endDate}
                onChange={handleDateChange}
                monthsShown={2}
                inline
                locale={vi}
                calendarClassName="enhanced-calendar"
                dayClassName={(date) => {
                  const isToday = date.toDateString() === new Date().toDateString()
                  const isInRange = startDate && endDate && date >= startDate && date <= endDate
                  const isStartOrEnd = (startDate && date.toDateString() === startDate.toDateString()) || 
                                      (endDate && date.toDateString() === endDate.toDateString())
                  
                  return `
                    relative transition-all duration-200 cursor-pointer
                    ${isToday ? 'font-bold' : ''}
                    ${isInRange && !isStartOrEnd ? 'bg-blue-50 dark:bg-blue-900/20' : ''}
                    ${isStartOrEnd ? 'bg-blue-500 text-white' : ''}
                    hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg
                  `
                }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}