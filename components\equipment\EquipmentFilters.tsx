'use client'

import React, { useState, useEffect } from 'react'
import { Search, X } from 'lucide-react'
import { useDebounce } from '@/hooks/useDebounce'
import { useCatalogs } from '@/hooks/queries/useCatalogs'
import { Select } from '@/components/ui/Select'
import { Button } from '@/components/ui/Button'

interface EquipmentFiltersProps {
  searchTerm: string
  selectedCatalog: string
  selectedManufacturer: string
  selectedStatus: 'ACTIVE' | 'INACTIVE' | ''
  manufacturers: string[]
  onSearchChange: (value: string) => void
  onCatalogChange: (value: string) => void
  onManufacturerChange: (value: string) => void
  onStatusChange: (value: 'ACTIVE' | 'INACTIVE' | '') => void
  onClearFilters: () => void
}

export function EquipmentFilters({
  searchTerm,
  selectedCatalog,
  selectedManufacturer,
  selectedStatus,
  manufacturers,
  onSearchChange,
  onCatalogChange,
  onManufacturerChange,
  onStatusChange,
  onClearFilters
}: EquipmentFiltersProps) {
  const [searchValue, setSearchValue] = useState(searchTerm)
  const debouncedSearch = useDebounce(searchValue, 500)

  const { data: catalogsData } = useCatalogs({ status: 'ACTIVE' })
  const catalogs = catalogsData?.catalogs || []

  useEffect(() => {
    if (debouncedSearch !== searchTerm) {
      onSearchChange(debouncedSearch)
    }
  }, [debouncedSearch, onSearchChange, searchTerm])

  useEffect(() => {
    setSearchValue(searchTerm)
  }, [searchTerm])

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value)
  }

  const handleCatalogChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onCatalogChange(e.target.value)
  }

  const handleManufacturerChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onManufacturerChange(e.target.value)
  }

  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onStatusChange(e.target.value as 'ACTIVE' | 'INACTIVE' | '')
  }

  const clearFilters = () => {
    setSearchValue('')
    onClearFilters()
  }

  const hasActiveFilters = !!(searchTerm || selectedCatalog || selectedManufacturer || selectedStatus)

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-6">
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {/* Search input */}
        <div className="md:col-span-2">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={searchValue}
              onChange={handleSearchChange}
              placeholder="Tìm kiếm theo mã, tên, mô tả..."
              className="w-full h-10 pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md hover:border-gray-400 dark:hover:border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 transition-colors"
            />
          </div>
        </div>

        {/* Catalog filter */}
        <div>
          <Select
            value={selectedCatalog}
            onChange={handleCatalogChange}
          >
            <option value="">Tất cả danh mục</option>
            {catalogs.map((catalog) => (
              <option key={catalog.id} value={catalog.id}>
                {catalog.catalogName}
              </option>
            ))}
          </Select>
        </div>

        {/* Manufacturer filter */}
        <div>
          <Select
            value={selectedManufacturer}
            onChange={handleManufacturerChange}
          >
            <option value="">Tất cả nhà sản xuất</option>
            {manufacturers.map((manufacturer) => (
              <option key={manufacturer} value={manufacturer}>
                {manufacturer}
              </option>
            ))}
          </Select>
        </div>

        {/* Status filter */}
        <div>
          <Select
            value={selectedStatus}
            onChange={handleStatusChange}
          >
            <option value="">Tất cả trạng thái</option>
            <option value="ACTIVE">Hoạt động</option>
            <option value="INACTIVE">Không hoạt động</option>
          </Select>
        </div>
      </div>

      {/* Clear filters button */}
      {hasActiveFilters && (
        <div className="mt-4 flex items-center justify-between border-t pt-4">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Đang áp dụng bộ lọc
          </div>
          <Button
            type="button"
            variant="secondary"
            size="sm"
            onClick={clearFilters}
            className="flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            Xóa bộ lọc
          </Button>
        </div>
      )}
    </div>
  )
}