# Google Drive OAuth Scopes Configuration

## Overview
This document outlines the OAuth scope requirements for the Google Drive integration in the equipment management system.

## Scope Requirements

### View Mode (Read-Only)
When users click "Xem" (View), the application enforces read-only access:
- **Required Scope**: `https://www.googleapis.com/auth/drive.readonly`
- **Behavior**: 
  - Opens documents in `/preview` mode
  - No editing toolbar visible
  - Cannot make changes to documents
  - Embedded iframe uses restricted sandbox attributes

### Edit Mode
When users click "Chỉnh sửa" (Edit), the application requires full edit permissions:
- **Required Scope**: `https://www.googleapis.com/auth/drive.file` or `https://www.googleapis.com/auth/drive`
- **Behavior**:
  - Opens documents directly in Google Drive with `/edit` URL
  - Full editing capabilities available
  - Changes are automatically saved to Google Drive
  - Opens in new tab/window

## Implementation Details

### URL Patterns
- **View Mode**: 
  - Documents: `https://docs.google.com/document/d/{fileId}/preview`
  - Spreadsheets: `https://docs.google.com/spreadsheets/d/{fileId}/preview`
  - Presentations: `https://docs.google.com/presentation/d/{fileId}/preview`
  - Other files: `https://drive.google.com/file/d/{fileId}/preview`

- **Edit Mode**:
  - Documents: `https://docs.google.com/document/d/{fileId}/edit`
  - Spreadsheets: `https://docs.google.com/spreadsheets/d/{fileId}/edit`
  - Presentations: `https://docs.google.com/presentation/d/{fileId}/edit`
  - Other files: `https://drive.google.com/file/d/{fileId}/edit`

### Security Considerations
1. The view mode uses embedded iframes with restricted sandbox attributes
2. Edit mode opens in a new window/tab to ensure full Google Drive functionality
3. OAuth tokens should be validated to ensure they have the appropriate scopes
4. The application should handle cases where users don't have edit permissions gracefully

## Configuration
Update the Google OAuth configuration in your environment:
```env
# For read-only access
GOOGLE_OAUTH_SCOPE_READONLY=https://www.googleapis.com/auth/drive.readonly

# For edit access
GOOGLE_OAUTH_SCOPE_EDIT=https://www.googleapis.com/auth/drive.file
```