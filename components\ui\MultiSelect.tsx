'use client';

import { useState, useRef, useEffect } from 'react';
import { ChevronDown, X, Check } from 'lucide-react';

interface Option {
  value: string | number;
  label: string;
}

interface MultiSelectProps {
  value: (string | number)[];
  onChange: (value: (string | number)[]) => void;
  options: Option[];
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  loading?: boolean;
  error?: boolean;
  searchPlaceholder?: string;
  selectAllLabel?: string;
  clearAllLabel?: string;
  selectedLabel?: string;
}

export function MultiSelect({
  value = [],
  onChange,
  options,
  placeholder = 'Select options...',
  className = '',
  disabled = false,
  loading = false,
  error = false,
  searchPlaceholder = 'Search...',
  selectAllLabel = 'Select All',
  clearAllLabel = 'Clear All',
  selectedLabel = 'selected'
}: MultiSelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchQuery('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const toggleOption = (optionValue: string | number) => {
    // Convert optionValue to string for consistent comparison
    const optionValueStr = String(optionValue);
    const isCurrentlySelected = value.some(v => String(v) === optionValueStr);
    const newValue = isCurrentlySelected
      ? value.filter(v => String(v) !== optionValueStr)
      : [...value, optionValue];
    onChange(newValue);
  };

  const selectAll = () => {
    const allValues = filteredOptions.map(option => option.value);
    const newValues = Array.from(new Set([...value, ...allValues]));
    onChange(newValues);
  };

  const clearAll = () => {
    if (searchQuery) {
      const filteredValues = filteredOptions.map(option => option.value);
      onChange(value.filter(v => !filteredValues.some(fv => String(fv) === String(v))));
    } else {
      onChange([]);
    }
  };

  const getDisplayText = () => {
    if (value.length === 0) return placeholder;
    if (value.length === 1) {
      const option = options.find(opt => opt.value === value[0]);
      return option?.label || '';
    }
    return `${value.length} ${selectedLabel}`;
  };

  const baseClasses = 'w-full h-10 px-3 py-2 pr-10 border rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 transition-colors appearance-none';
  const stateClasses = error
    ? 'border-red-300 dark:border-red-700'
    : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500';
  const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer';

  return (
    <div ref={dropdownRef} className="relative">
      <div
        onClick={() => !disabled && !loading && setIsOpen(!isOpen)}
        className={`${baseClasses} ${stateClasses} ${disabledClasses} ${className} flex items-center justify-between`}
      >
        <span className={value.length === 0 ? 'text-gray-400' : ''}>
          {loading ? 'Loading...' : getDisplayText()}
        </span>
        <ChevronDown className={`absolute right-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none transition-transform ${isOpen ? 'transform rotate-180' : ''}`} />
      </div>

      {isOpen && !disabled && !loading && (
        <div className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-auto">
          <div className="sticky top-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-2">
            <input
              ref={inputRef}
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder={searchPlaceholder}
              className="w-full h-8 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              onClick={(e) => e.stopPropagation()}
            />
            <div className="flex gap-2 mt-2">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  selectAll();
                }}
                className="flex-1 px-2 py-1 text-xs text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded"
              >
                {selectAllLabel}
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  clearAll();
                }}
                className="flex-1 px-2 py-1 text-xs text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded"
              >
                {clearAllLabel}
              </button>
            </div>
          </div>

          <div className="py-1">
            {filteredOptions.length === 0 ? (
              <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                No options found
              </div>
            ) : (
              filteredOptions.map((option) => {
                const isSelected = value.some(v => String(v) === String(option.value));
                return (
                  <div
                    key={option.value}
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleOption(option.value);
                    }}
                    className={`px-3 py-2 cursor-pointer flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                      isSelected ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                    }`}
                  >
                    <span className={`text-sm ${isSelected ? 'font-medium text-blue-900 dark:text-blue-100' : 'text-gray-900 dark:text-gray-100'}`}>
                      {option.label}
                    </span>
                    {isSelected && (
                      <Check className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    )}
                  </div>
                );
              })
            )}
          </div>
        </div>
      )}
    </div>
  );
}