import { NextRequest, NextResponse } from 'next/server'
import { readFile } from 'fs/promises'
import path from 'path'
import mime from 'mime-types'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  let errorDetails: any = {}
  
  try {
    const { path: pathSegments } = await params
    const filePath = path.join(process.cwd(), 'uploads', ...pathSegments)
    
    console.log('[FILE_SERVE] Serving file request', {
      timestamp: new Date().toISOString(),
      requestPath: pathSegments.join('/'),
      fullPath: filePath,
      userAgent: request.headers.get('user-agent'),
      referer: request.headers.get('referer'),
    })

    // Enhanced security checks
    if (pathSegments.some(segment => segment.includes('..'))) {
      console.warn('[FILE_SERVE] Path traversal attempt detected:', pathSegments)
      return new NextResponse('Forbidden - Path traversal detected', { status: 403 })
    }

    // Additional security: only allow serving from specific subdirectories
    const allowedPaths = ['bidding-documents', 'equipment', 'documents']
    if (!allowedPaths.some(allowedPath => pathSegments[0] === allowedPath)) {
      console.warn('[FILE_SERVE] Access to unauthorized directory:', pathSegments[0])
      return new NextResponse('Forbidden - Unauthorized directory', { status: 403 })
    }

    // Check if file exists before attempting to read
    const fs = await import('fs/promises')
    let fileStats: any
    try {
      fileStats = await fs.stat(filePath)
      if (!fileStats.isFile()) {
        console.warn('[FILE_SERVE] Path is not a file:', filePath)
        return new NextResponse('Not Found - Path is not a file', { status: 404 })
      }
    } catch (statError: any) {
      console.warn('[FILE_SERVE] File does not exist:', {
        path: filePath,
        error: statError.code,
      })
      return new NextResponse('File not found', { status: 404 })
    }

    // Read file with error handling
    let fileBuffer: Buffer
    try {
      fileBuffer = await readFile(filePath)
      console.log('[FILE_SERVE] File read successfully', {
        path: filePath,
        size: fileBuffer.length,
        lastModified: fileStats.mtime,
      })
    } catch (readError: any) {
      console.error('[FILE_SERVE] Error reading file:', readError)
      errorDetails.readError = {
        operation: 'readFile',
        path: filePath,
        error: readError.message,
        code: readError.code,
        errno: readError.errno,
      }
      
      if (readError.code === 'EACCES') {
        return new NextResponse('Permission denied', { status: 403 })
      }
      
      if (readError.code === 'EMFILE' || readError.code === 'ENFILE') {
        return new NextResponse('Server temporarily unavailable - too many open files', { status: 503 })
      }
      
      throw new Error(`Failed to read file: ${readError.message}`)
    }
    
    // Get MIME type with fallback
    const mimeType = mime.lookup(filePath) || 'application/octet-stream'
    const fileName = path.basename(filePath)

    console.log('[FILE_SERVE] Serving file', {
      fileName,
      mimeType,
      size: fileBuffer.length,
    })

    // Return file with proper headers including security headers
    return new NextResponse(fileBuffer as unknown as BodyInit, {
      headers: {
        'Content-Type': mimeType,
        'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
        'Content-Disposition': `inline; filename="${fileName}"`,
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'Content-Length': fileBuffer.length.toString(),
        'Last-Modified': fileStats.mtime.toUTCString(),
      },
    })
  } catch (error: any) {
    // Comprehensive error logging
    const errorLog = {
      timestamp: new Date().toISOString(),
      error: error.message,
      stack: error.stack,
      environment: process.env.NODE_ENV,
      workingDirectory: process.cwd(),
      userAgent: request.headers.get('user-agent'),
      referer: request.headers.get('referer'),
      ...errorDetails,
    }
    
    console.error('[FILE_SERVE] Operation failed:', errorLog)

    // Return appropriate error response
    const isProduction = process.env.NODE_ENV === 'production'
    return new NextResponse(
      isProduction ? 'Internal server error' : error.message,
      { 
        status: 500,
        headers: {
          'Content-Type': 'text/plain',
          'X-Error-Timestamp': new Date().toISOString(),
        }
      }
    )
  }
}