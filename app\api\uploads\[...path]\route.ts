import { NextRequest, NextResponse } from 'next/server'
import { readFile } from 'fs/promises'
import path from 'path'
import mime from 'mime-types'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const { path: pathSegments } = await params
    const filePath = path.join(process.cwd(), 'uploads', ...pathSegments)

    // Security check - prevent path traversal
    if (filePath.includes('..')) {
      return new NextResponse('Forbidden', { status: 403 })
    }

    // Read file
    const fileBuffer = await readFile(filePath)
    
    // Get MIME type
    const mimeType = mime.lookup(filePath) || 'application/octet-stream'

    // Return file with proper headers
    return new NextResponse(fileBuffer as unknown as BodyInit, {
      headers: {
        'Content-Type': mimeType,
        'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
      },
    })
  } catch (error) {
    console.error('Error serving file:', error)
    return new NextResponse('File not found', { status: 404 })
  }
}