import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { documentService } from '@/services/documentService';
import type { DocumentFilters, Document } from '@/types/document';
import { useToast } from '@/hooks/useToast';

export function useDocuments(filters: DocumentFilters = {}) {
  return useQuery({
    queryKey: ['documents', filters],
    queryFn: ({ signal }) => documentService.getDocuments(filters, signal),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useDocument(id: string, enabled = true) {
  return useQuery({
    queryKey: ['document', id],
    queryFn: ({ signal }) => documentService.getDocumentById(id, signal),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useUploadDocument() {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (formData: FormData) => documentService.uploadDocument(formData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documents'] });
      queryClient.invalidateQueries({ queryKey: ['document-tags'] });
      toast.success('Tải lên tài liệu thành công');
    },
    onError: (error: any) => {
      const message = error.response?.data?.error || 'Lỗi khi tải lên tài liệu';
      toast.error(message);
    },
  });
}

export function useDeleteDocument() {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (id: string) => documentService.deleteDocument(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documents'] });
      queryClient.invalidateQueries({ queryKey: ['document-tags'] });
      toast.success('Xóa tài liệu thành công');
    },
    onError: (error: any) => {
      const message = error.response?.data?.error || 'Lỗi khi xóa tài liệu';
      toast.error(message);
    },
  });
}

export function useDownloadDocument() {
  const toast = useToast();
  
  return useMutation({
    mutationFn: (id: string) => documentService.downloadDocument(id),
    onError: (error: any) => {
      const message = error.response?.data?.error || 'Lỗi khi tải xuống tài liệu';
      toast.error(message);
    },
  });
}

export function useDocumentTags() {
  return useQuery({
    queryKey: ['document-tags'],
    queryFn: ({ signal }) => documentService.getTags(signal),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useBulkDeleteDocuments() {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (ids: string[]) => documentService.bulkDelete(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documents'] });
      queryClient.invalidateQueries({ queryKey: ['document-tags'] });
      toast.success('Xóa tài liệu thành công');
    },
    onError: (error: any) => {
      const message = error.response?.data?.error || 'Lỗi khi xóa tài liệu';
      toast.error(message);
    },
  });
}