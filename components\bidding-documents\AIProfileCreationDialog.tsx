'use client';

import React, { useState, useEffect } from 'react';
import { Dialog } from '@/components/ui/Dialog';
import { Button } from '@/components/ui/Button';
import { Select } from '@/components/ui/Select';
import { useEquipments } from '@/hooks/queries/useEquipment';
import { useToast } from '@/hooks/useToast';
import { Equipment } from '@/types/equipment';
import { tokenManager } from '@/lib/tokenManager';
import { Trash2, Plus, AlertCircle, Download, FileSpreadsheet, Loader2, Sparkles, ExternalLink } from 'lucide-react';
import type { BiddingDocumentAttachment } from '@/types/biddingDocument';

interface EquipmentItem {
  equipmentId: string;
  pageRange?: {
    from: number;
    to: number;
  };
}

interface AIProfileCreationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm?: (selectedFile: string, equipmentItems: EquipmentItem[], aiGeneratedAttachment?: BiddingDocumentAttachment) => void;
  existingFiles?: { id: string; name: string; url: string }[];
  existingEquipmentItems?: EquipmentItem[];
  biddingDocumentId?: string;
}

interface AIGeneratedResult {
  excelFileUrl: string;
  excelFileName: string;
  gcsBucket?: string;
  gcsFilename?: string;
  downloadUrl: string;
}

export const AIProfileCreationDialog: React.FC<AIProfileCreationDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  existingFiles = [],
  existingEquipmentItems = [],
  biddingDocumentId
}) => {
  const [selectedFile, setSelectedFile] = useState<string>('');
  const [equipmentItems, setEquipmentItems] = useState<EquipmentItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [aiResult, setAiResult] = useState<AIGeneratedResult | null>(null);
  const [processing, setProcessing] = useState(false);
  const [showManualDownload, setShowManualDownload] = useState(false);
  const [downloadUrl, setDownloadUrl] = useState<string>('');
  const toast = useToast();

  // Fetch equipment from current inventory
  const { data: equipmentsData, isLoading: isLoadingEquipments, error: equipmentsError } = useEquipments({ status: 'ACTIVE' });
  const equipments = equipmentsData?.data || [];

  // Get currently selected equipment IDs (only from current selection)
  const getCurrentlySelectedEquipmentIds = () => {
    return equipmentItems.map(item => item.equipmentId).filter(id => id);
  };

  // Check if equipment is already selected in current list
  const isEquipmentCurrentlySelected = (equipmentId: string) => {
    return getCurrentlySelectedEquipmentIds().includes(equipmentId);
  };
  
  // Debug logging
  useEffect(() => {
    if (equipmentsData) {
      console.log('Equipment data received:', equipmentsData);
    }
    if (equipmentsError) {
      console.error('Error fetching equipment:', equipmentsError);
    }
  }, [equipmentsData, equipmentsError]);

  useEffect(() => {
    if (!isOpen) {
      setSelectedFile('');
      setEquipmentItems([]);
      setAiResult(null);
      setProcessing(false);
      setShowManualDownload(false);
      setDownloadUrl('');
    }
  }, [isOpen]);

  const handleAddEquipment = () => {
    setEquipmentItems([...equipmentItems, { equipmentId: '' }]);
  };

  const handleRemoveEquipment = (index: number) => {
    setEquipmentItems(equipmentItems.filter((_, i) => i !== index));
  };

  const handleEquipmentChange = (index: number, equipmentId: string) => {
    // Only check if this equipment is already selected in the current list
    if (equipmentId && isEquipmentCurrentlySelected(equipmentId)) {
      // Check if it's the same item being changed
      const currentItemId = equipmentItems[index]?.equipmentId;
      if (currentItemId !== equipmentId) {
        toast.error('Thiết bị này đã được chọn');
        return;
      }
    }

    const newItems = [...equipmentItems];
    newItems[index] = { ...newItems[index], equipmentId };
    setEquipmentItems(newItems);
  };

  const handlePageRangeChange = (index: number, field: 'from' | 'to', value: string) => {
    const newItems = [...equipmentItems];
    const pageValue = parseInt(value) || 0;
    
    if (!newItems[index].pageRange) {
      newItems[index].pageRange = { from: 0, to: 0 };
    }
    
    newItems[index].pageRange![field] = pageValue;
    setEquipmentItems(newItems);
  };

  const handleGenerateAI = async () => {
    if (!selectedFile) {
      toast.error('Vui lòng chọn tệp tin');
      return;
    }

    if (equipmentItems.length === 0 || equipmentItems.every(item => !item.equipmentId)) {
      toast.error('Vui lòng chọn ít nhất một thiết bị');
      return;
    }

    setProcessing(true);
    setAiResult(null);

    try {
      // Get the access token
      const token = tokenManager.getAccessToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      // Filter out empty equipment items
      const validEquipmentItems = equipmentItems.filter(item => item.equipmentId);

      // Prepare form data
      const formData = new FormData();
      formData.append('fileId', selectedFile);
      formData.append('equipmentItems', JSON.stringify(validEquipmentItems));

      // Call AI API
      const response = await fetch('/api/bidding-documents/ai-profile', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'AI processing failed');
      }

      const data = await response.json();
      
      if (data.success && data.result) {
        setAiResult(data.result);
        toast.success('Đã tạo hồ sơ thành công!');
        
        // If we have a bidding document ID, download the file and upload to Google Drive
        if (biddingDocumentId) {
          let isCorsError = false;
          
          try {
            // Step 1: Download the Excel file from AI result
            toast.info('Đang tải file Excel...');
            
            // Try to fetch the file
            let blob: Blob | null = null;
            try {
              // First try direct fetch
              const downloadResponse = await fetch(data.result.downloadUrl, {
                mode: 'cors',
                credentials: 'omit'
              });
              
              if (!downloadResponse.ok) {
                throw new Error('Failed to download Excel file');
              }
              
              blob = await downloadResponse.blob();
            } catch (fetchError: any) {
              console.error('Direct fetch failed:', fetchError);
              
              // Try using proxy endpoint as fallback
              try {
                toast.info('Thử phương thức tải khác...');
                const proxyResponse = await fetch('/api/bidding-documents/download-proxy', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                  },
                  body: JSON.stringify({
                    url: data.result.downloadUrl,
                    fileName: data.result.excelFileName
                  })
                });
                
                if (proxyResponse.ok) {
                  blob = await proxyResponse.blob();
                } else {
                  throw new Error('Proxy download failed');
                }
              } catch (proxyError) {
                console.error('Proxy fetch also failed:', proxyError);
                // Check if it's a CORS error
                if (fetchError.message?.includes('CORS') || fetchError.message?.includes('Failed to fetch')) {
                  isCorsError = true;
                  throw new Error('CORS');
                }
                throw fetchError;
              }
            }
            
            if (!blob) {
              throw new Error('Failed to get file blob');
            }
            
            const file = new File([blob], data.result.excelFileName, {
              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            });
            
            // Step 2: Upload to Google Drive
            toast.info('Đang tải lên Google Drive...');
            const uploadFormData = new FormData();
            uploadFormData.append('file', file);
            
            const uploadResponse = await fetch('/api/google-drive/upload', {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${token}`
              },
              body: uploadFormData
            });
            
            if (!uploadResponse.ok) {
              throw new Error('Failed to upload to Google Drive');
            }
            
            const uploadResult = await uploadResponse.json();
            
            // Step 3: Save as attachment
            const attachmentData = {
              fileName: data.result.excelFileName,
              fileUrl: uploadResult.webViewLink || '',
              fileSize: file.size,
              mimeType: uploadResult.mimeType || 'application/vnd.google-apps.spreadsheet',
              source: 'google_drive' as const,
              googleDriveId: uploadResult.fileId,
              googleDriveUrl: uploadResult.editUrl || uploadResult.webViewLink
            };
            
            const attachmentResponse = await fetch(`/api/bidding-documents/${biddingDocumentId}/attachments/batch`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify({ attachments: [attachmentData] })
            });
            
            if (attachmentResponse.ok) {
              toast.success('Đã lưu file vào Google Drive thành công!');
              // Success - close dialog and notify parent
              const validEquipmentItems = equipmentItems.filter(item => item.equipmentId);
              if (onConfirm) {
                onConfirm(selectedFile, validEquipmentItems, attachmentData as BiddingDocumentAttachment);
              }
              onClose();
              return;
            } else {
              throw new Error('Failed to save attachment record');
            }
          } catch (error: any) {
            console.error('Error processing AI-generated file:', error);
            
            // Handle CORS error specifically
            if (error.message === 'CORS' || isCorsError) {
              toast.warning('Không thể tự động tải file do hạn chế CORS. Vui lòng tải xuống thủ công.');
              setDownloadUrl(data.result.downloadUrl);
              setShowManualDownload(true);
            } else {
              toast.error('Lỗi khi xử lý file: ' + error.message);
              // Still show manual download as fallback
              setDownloadUrl(data.result.downloadUrl);
              setShowManualDownload(true);
            }
          }
        } else {
          // No bidding document ID, just show download link
          setDownloadUrl(data.result.downloadUrl);
          setShowManualDownload(true);
        }
      } else {
        throw new Error('Invalid response from AI');
      }
    } catch (error) {
      console.error('AI generation error:', error);
      toast.error(error instanceof Error ? error.message : 'Lỗi khi tạo hồ sơ bằng AI');
    } finally {
      setProcessing(false);
    }
  };

  const handleDownload = () => {
    if (aiResult?.downloadUrl) {
      window.open(aiResult.downloadUrl, '_blank');
    }
  };

  const handleClose = () => {
    if (processing) {
      toast.warning('Đang xử lý, vui lòng chờ...');
      return;
    }
    
    onClose();
  };

  return (
    <Dialog
      isOpen={isOpen}
      onClose={handleClose}
      title="Tạo hồ sơ bằng AI"
      description="Chọn tệp tin hiện có và danh sách thiết bị để tạo hồ sơ bằng AI"
      className="max-w-3xl"
    >
      <div className="p-6">
        <div className="space-y-6">
          {/* File Picker Section */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Chọn tệp tin PDF</label>
            <Select
              value={selectedFile}
              onChange={(e) => setSelectedFile(e.target.value)}
              className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Chọn tệp tin từ danh sách</option>
              {existingFiles.map((file) => (
                <option key={file.id} value={file.id}>
                  {file.name}
                </option>
              ))}
            </Select>
            <p className="text-xs text-gray-500 mt-1">
              <AlertCircle className="w-3 h-3 inline mr-1" />
              Chỉ hỗ trợ file PDF được lưu trên server
            </p>
          </div>

          {/* Equipment Items Section */}
          <div className="space-y-2">
            <div className="flex justify-between items-center mb-2">
              <label className="text-sm font-medium">Danh sách thiết bị</label>
              <Button
                type="button"
                size="sm"
                variant="outline"
                onClick={handleAddEquipment}
                className="gap-2"
              >
                <Plus className="w-4 h-4" />
                Thêm thiết bị
              </Button>
            </div>

            {isLoadingEquipments ? (
              <div className="border border-dashed rounded-lg p-8 text-center">
                <p className="text-sm text-gray-500">Đang tải danh sách thiết bị...</p>
              </div>
            ) : equipmentsError ? (
              <div className="border border-dashed rounded-lg p-8 text-center">
                <p className="text-sm text-red-500">Lỗi khi tải danh sách thiết bị</p>
              </div>
            ) : equipmentItems.length === 0 ? (
              <div className="border border-dashed rounded-lg p-8 text-center">
                <p className="text-sm text-gray-500">
                  Chưa có thiết bị nào. Nhấn "Thêm thiết bị" để bắt đầu.
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {equipmentItems.map((item, index) => (
                  <div key={index} className="flex gap-2 items-start border rounded-lg p-3">
                    <div className="flex-1">
                      <Select
                        value={item.equipmentId}
                        onChange={(e) => handleEquipmentChange(index, e.target.value)}
                        className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500"
                        disabled={isLoadingEquipments}
                      >
                        <option value="">Chọn thiết bị</option>
                        {equipments.map((equipment) => {
                          const isCurrentlySelected = isEquipmentCurrentlySelected(equipment.id);
                          const isCurrentItem = item.equipmentId === equipment.id;
                          const isDisabled = isCurrentlySelected && !isCurrentItem;
                          
                          return (
                            <option 
                              key={equipment.id} 
                              value={equipment.id}
                              disabled={isDisabled}
                            >
                              {equipment.name} - {equipment.equipmentCode}
                              {isCurrentlySelected && !isCurrentItem && ' (Đã chọn)'}
                            </option>
                          );
                        })}
                      </Select>
                    </div>

                    <div className="flex gap-2 items-center">
                      <input
                        type="number"
                        placeholder="Từ trang"
                        className="w-24 h-10 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                        min="1"
                        value={item.pageRange?.from || ''}
                        onChange={(e) => handlePageRangeChange(index, 'from', e.target.value)}
                      />
                      <span className="text-sm">-</span>
                      <input
                        type="number"
                        placeholder="Đến trang"
                        className="w-24 h-10 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                        min="1"
                        value={item.pageRange?.to || ''}
                        onChange={(e) => handlePageRangeChange(index, 'to', e.target.value)}
                      />
                    </div>

                    <Button
                      type="button"
                      size="sm"
                      variant="ghost"
                      onClick={() => handleRemoveEquipment(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Manual Download Section - Show when CORS error occurs */}
        {showManualDownload && downloadUrl && (
          <div className="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
            <div className="flex items-start gap-3">
              <AlertCircle className="w-5 h-5 text-amber-600 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <h4 className="text-sm font-semibold text-amber-800 mb-2">
                  Tải xuống thủ công
                </h4>
                <p className="text-sm text-amber-700 mb-3">
                  Do hạn chế CORS, file không thể được tải tự động. 
                  Vui lòng nhấp vào nút bên dưới để tải file Excel đã tạo:
                </p>
                <div className="flex items-center gap-4">
                  <Button
                    type="button"
                    variant="primary"
                    onClick={() => window.open(downloadUrl, '_blank')}
                    className="gap-2 bg-amber-600 hover:bg-amber-700"
                  >
                    <Download className="w-4 h-4" />
                    Tải xuống file Excel
                  </Button>
                  <a 
                    href={downloadUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-amber-700 hover:text-amber-800 underline flex items-center gap-1"
                  >
                    <ExternalLink className="w-3 h-3" />
                    Mở trong tab mới
                  </a>
                </div>
                <p className="text-xs text-amber-600 mt-3">
                  Sau khi tải xuống, bạn có thể tải file lên Google Drive thủ công 
                  để lưu vào hệ thống.
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="mt-6 flex justify-end gap-3">
          <Button 
            type="button"
            variant="outline" 
            onClick={handleClose} 
            disabled={processing}
          >
            {showManualDownload ? 'Đóng' : 'Hủy'}
          </Button>
          {!showManualDownload && (
            <Button 
              type="button"
              onClick={handleGenerateAI} 
              disabled={processing || !selectedFile || equipmentItems.length === 0}
              className="gap-2"
            >
              {processing ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Đang xử lý...
                </>
              ) : (
                <>
                  <Sparkles className="w-4 h-4" />
                  Tạo hồ sơ với AI
                </>
              )}
            </Button>
          )}
        </div>
      </div>
    </Dialog>
  );
};