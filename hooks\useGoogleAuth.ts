'use client';

import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/hooks/useToast';

interface GoogleAuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  userEmail?: string;
}

export function useGoogleAuth() {
  const [authState, setAuthState] = useState<GoogleAuthState>({
    isAuthenticated: false,
    isLoading: true,
  });
  const toast = useToast();

  const checkAuthStatus = useCallback(async () => {
    try {
      const response = await fetch('/api/auth/google/status');
      const data = await response.json();
      
      setAuthState({
        isAuthenticated: data.authenticated && data.mode === 'OAuth',
        isLoading: false,
        userEmail: data.userEmail,
      });
    } catch (error) {
      console.error('Error checking auth status:', error);
      setAuthState({
        isAuthenticated: false,
        isLoading: false,
      });
    }
  }, []);

  const signIn = useCallback(async (redirect?: string) => {
    try {
      // Open OAuth flow in popup window
      const width = 500;
      const height = 600;
      const left = window.screenX + (window.outerWidth - width) / 2;
      const top = window.screenY + (window.outerHeight - height) / 2;
      
      const authUrl = `/api/auth/google?popup=true&redirect=${encodeURIComponent(redirect || window.location.pathname)}`;
      const authWindow = window.open(
        authUrl,
        'google-auth',
        `width=${width},height=${height},left=${left},top=${top}`
      );

      // Check if auth window closed and refresh auth status
      const checkInterval = setInterval(() => {
        if (authWindow?.closed) {
          clearInterval(checkInterval);
          checkAuthStatus();
        }
      }, 1000);

      // Listen for message from auth callback
      const handleMessage = (event: MessageEvent) => {
        if (event.origin === window.location.origin && event.data.type === 'google-auth-success') {
          clearInterval(checkInterval);
          authWindow?.close();
          checkAuthStatus();
          toast.success('Đăng nhập Google thành công');
          window.removeEventListener('message', handleMessage);
        }
      };

      window.addEventListener('message', handleMessage);
    } catch (error) {
      console.error('Error during sign in:', error);
      toast.error('Lỗi khi đăng nhập Google');
    }
  }, [checkAuthStatus, toast]);

  const signOut = useCallback(async () => {
    try {
      await fetch('/api/auth/google/logout', { method: 'POST' });
      setAuthState({
        isAuthenticated: false,
        isLoading: false,
      });
      toast.success('Đăng xuất Google thành công');
    } catch (error) {
      console.error('Error during sign out:', error);
      toast.error('Lỗi khi đăng xuất Google');
    }
  }, [toast]);

  useEffect(() => {
    checkAuthStatus();
  }, [checkAuthStatus]);

  return {
    ...authState,
    signIn,
    signOut,
    checkAuthStatus,
  };
}