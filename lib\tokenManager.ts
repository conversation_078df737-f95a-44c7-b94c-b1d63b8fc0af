/**
 * In-Memory Token Manager
 * Stores access token in memory only to prevent XSS attacks
 * Token is lost on page refresh and will be automatically refreshed
 */

class TokenManager {
  private accessToken: string | null = null
  private expiresAt: Date | null = null

  setAccessToken(token: string, expiresIn: number) {
    this.accessToken = token
    // expiresIn is in seconds, convert to milliseconds
    this.expiresAt = new Date(Date.now() + expiresIn * 1000)
  }

  getAccessToken(): string | null {
    // Check if token exists and is not expired
    if (!this.accessToken || !this.expiresAt) {
      return null
    }

    // Add 30 seconds buffer to handle clock skew
    const now = new Date()
    const bufferTime = 30 * 1000 // 30 seconds
    const expirationWithBuffer = new Date(this.expiresAt.getTime() - bufferTime)

    if (now >= expirationWithBuffer) {
      this.clear()
      return null
    }

    return this.accessToken
  }

  isExpired(): boolean {
    if (!this.expiresAt) {
      return true
    }

    // Check if token will expire in the next 30 seconds
    const now = new Date()
    const bufferTime = 30 * 1000
    const expirationWithBuffer = new Date(this.expiresAt.getTime() - bufferTime)

    return now >= expirationWithBuffer
  }

  clear() {
    this.accessToken = null
    this.expiresAt = null
  }

  getExpiresAt(): Date | null {
    return this.expiresAt
  }
}

// Singleton instance
export const tokenManager = new TokenManager()