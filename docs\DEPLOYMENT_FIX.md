# Bidding Document Upload Error Fix

## Problem
The bidding document creation works locally but fails on the production server with:
- Error: "Failed to create bidding document"
- Message: "Internal server error"

## Root Causes
1. **Missing Upload Directories**: Upload directories are gitignored and not created on the server
2. **File System Permissions**: Server process may lack write permissions
3. **Directory Creation Failures**: The API cannot create directories at runtime due to permissions

## Solution Implemented

### 1. Automatic Directory Creation Script
Created `scripts/ensure-upload-dirs.js` that:
- Creates all required upload directories
- Adds .gitkeep files to track directory structure
- Runs automatically on build, start, and install

### 2. Updated package.json Scripts
- `build`: Now runs directory creation before building
- `start`: Ensures directories exist before starting
- `postinstall`: Creates directories after npm install

### 3. Docker Support (Optional)
Created Dockerfile that:
- Builds the application in a multi-stage process
- Creates upload directories with proper permissions
- Runs as non-root user for security

## Deployment Steps

### Option 1: Standard Deployment
1. Deploy the updated code
2. Run `npm install` (will create directories via postinstall)
3. Start the application normally

### Option 2: Manual Fix (Immediate)
SSH into the server and run:
```bash
cd /path/to/your/app
node scripts/ensure-upload-dirs.js
# Or manually create:
mkdir -p uploads/bidding-documents uploads/equipment uploads/documents
mkdir -p public/uploads/bidding-documents public/uploads/equipment public/uploads/documents
chmod -R 755 uploads public/uploads
```

### Option 3: Docker Deployment
```bash
docker build -t bidding-system .
docker run -p 3002:3002 -e DATABASE_URL="your-db-url" bidding-system
```

## Verification
1. Check if directories exist: `ls -la uploads/`
2. Test file upload through the application
3. Monitor logs for any permission errors

## Additional Considerations
- Ensure the user running the Node.js process has write permissions
- Consider using a dedicated file storage service (S3, GCS) for production
- Monitor disk space on the server
- Set up log rotation for uploaded files