export interface Bidding {
  id: number
  biddingCode: string
  title: string
  description: string
  projectId: number
  project?: Project
  category: BiddingCategory
  estimatedValue: number
  startDate: string
  endDate: string
  openingDate: string
  status: BiddingStatus
  method: BiddingMethod
  documents: BiddingDocument[]
  participants: BiddingParticipant[]
  winner?: BiddingParticipant
  createdBy: number
  createdAt: string
  updatedAt: string
}

export type BiddingStatus = 
  | 'DRAFT'
  | 'PUBLISHED'
  | 'OPEN'
  | 'EVALUATING'
  | 'COMPLETED'
  | 'CANCELLED'

export type BiddingMethod = 
  | 'OPEN'
  | 'LIMITED'
  | 'DIRECT'
  | 'COMPETITIVE'
  | 'ONLINE'

export type BiddingCategory = 
  | 'CONSTRUCTION'
  | 'GOODS'
  | 'SERVICES'
  | 'CONSULTING'
  | 'MIXED'

export interface BiddingDocument {
  id: number
  name: string
  type: 'INVITATION' | 'REQUIREMENT' | 'FORM' | 'OTHER'
  url: string
  uploadedAt: string
}

export interface BiddingParticipant {
  id: number
  contractorId: number
  contractor?: Contractor
  proposalValue: number
  technicalScore?: number
  financialScore?: number
  totalScore?: number
  ranking?: number
  status: 'REGISTERED' | 'SUBMITTED' | 'QUALIFIED' | 'DISQUALIFIED' | 'WINNER'
  submittedAt?: string
  documents: ParticipantDocument[]
}

export interface ParticipantDocument {
  id: number
  name: string
  type: 'PROPOSAL' | 'FINANCIAL' | 'TECHNICAL' | 'LICENSE' | 'OTHER'
  url: string
  uploadedAt: string
}

export interface Project {
  id: number
  projectCode: string
  name: string
  description: string
  budget: number
  startDate: string
  endDate: string
  status: 'PLANNING' | 'BIDDING' | 'EXECUTING' | 'COMPLETED' | 'SUSPENDED'
}

export interface Contractor {
  id: number
  code: string
  name: string
  taxCode: string
  address: string
  phone: string
  email: string
  representative: string
  businessLicense: string
  rating?: number
  status: 'ACTIVE' | 'SUSPENDED' | 'BLACKLISTED'
}

export interface BiddingFilters {
  search?: string
  status?: BiddingStatus
  method?: BiddingMethod
  category?: BiddingCategory
  projectId?: number
  startDate?: string
  endDate?: string
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface BiddingsResponse {
  biddings: Bidding[]
  total: number
  page: number
  totalPages: number
}

export interface CreateBiddingRequest {
  biddingCode: string
  title: string
  description: string
  projectId: number
  category: BiddingCategory
  estimatedValue: number
  startDate: string
  endDate: string
  openingDate: string
  method: BiddingMethod
}

export interface UpdateBiddingRequest extends Partial<CreateBiddingRequest> {
  status?: BiddingStatus
}