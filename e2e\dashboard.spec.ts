import { test, expect } from '@playwright/test'

test.describe('Dashboard', () => {
  // Login before each test
  test.beforeEach(async ({ page }) => {
    // Go to login page
    await page.goto('/login')
    
    // Perform login
    await page.getByLabel(/email/i).fill('<EMAIL>')
    await page.getByLabel(/password|mật khẩu/i).fill('password123')
    await page.getByRole('button', { name: /đăng nhập|login/i }).click()
    
    // Wait for dashboard to load
    await page.waitForURL('**/dashboard')
  })

  test('displays dashboard metrics', async ({ page }) => {
    // Check for main dashboard heading
    await expect(page.getByRole('heading', { name: /dashboard|bảng điều khiển|tổng quan/i })).toBeVisible()

    // Check for metric cards
    const metricCards = page.locator('[data-testid="metric-card"], .metric-card, .stat-card')
    await expect(metricCards).toHaveCount(4, { timeout: 10000 })

    // Check specific metrics if they have labels
    await expect(page.getByText(/tổng dự án|total projects/i)).toBeVisible()
    await expect(page.getByText(/dự án hoạt động|active projects/i)).toBeVisible()
    await expect(page.getByText(/tổng ngân sách|total budget/i)).toBeVisible()
    await expect(page.getByText(/tỷ lệ hoàn thành|completion rate/i)).toBeVisible()
  })

  test('displays trend chart', async ({ page }) => {
    // Check for chart container
    const chartContainer = page.locator('[data-testid="trend-chart"], .chart-container, canvas, svg')
    await expect(chartContainer.first()).toBeVisible({ timeout: 10000 })
  })

  test('displays activity feed', async ({ page }) => {
    // Check for activity feed section
    const activitySection = page.locator('[data-testid="activity-feed"], .activity-feed, .recent-activities')
    await expect(activitySection.first()).toBeVisible()

    // Check for activity items
    const activityItems = page.locator('[data-testid="activity-item"], .activity-item, .feed-item')
    await expect(activityItems.first()).toBeVisible()
  })

  test('sidebar navigation works', async ({ page }) => {
    // Check sidebar is visible
    const sidebar = page.locator('[data-testid="sidebar"], aside, .sidebar')
    await expect(sidebar).toBeVisible()

    // Navigate to Projects/Biddings
    const projectsLink = page.getByRole('link', { name: /dự án|projects|thầu|bidding/i })
    if (await projectsLink.count() > 0) {
      await projectsLink.first().click()
      await page.waitForLoadState('networkidle')
      
      // Check URL changed
      const url = page.url()
      expect(url).toMatch(/\/(projects|biddings|du-an|thau)/)
    }
  })

  test('user menu dropdown works', async ({ page }) => {
    // Find and click user menu
    const userMenuButton = page.locator('[data-testid="user-menu"], .user-menu, button:has-text("Admin")')
    
    if (await userMenuButton.count() > 0) {
      await userMenuButton.first().click()
      
      // Check dropdown items are visible
      await expect(page.getByText(/profile|hồ sơ|cài đặt|settings/i)).toBeVisible()
      await expect(page.getByText(/logout|đăng xuất/i)).toBeVisible()
    }
  })

  test('responsive design - mobile view', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Check if hamburger menu appears
    const hamburgerMenu = page.locator('[data-testid="mobile-menu"], .hamburger, button[aria-label*="menu"]')
    if (await hamburgerMenu.count() > 0) {
      await expect(hamburgerMenu.first()).toBeVisible()
      
      // Click to open mobile menu
      await hamburgerMenu.first().click()
      
      // Check sidebar/menu is visible
      const mobileNav = page.locator('[data-testid="mobile-nav"], .mobile-sidebar, .mobile-menu')
      await expect(mobileNav.first()).toBeVisible()
    }
  })

  test('responsive design - tablet view', async ({ page }) => {
    // Set tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 })
    
    // Dashboard should still be functional
    await expect(page.getByRole('heading', { name: /dashboard|bảng điều khiển/i })).toBeVisible()
    
    // Check layout adjustments
    const metricCards = page.locator('[data-testid="metric-card"], .metric-card, .stat-card')
    await expect(metricCards).toHaveCount(4)
  })

  test('dark mode toggle if available', async ({ page }) => {
    // Look for theme toggle
    const themeToggle = page.locator('[data-testid="theme-toggle"], button[aria-label*="theme"], button[aria-label*="dark"], button[aria-label*="light"]')
    
    if (await themeToggle.count() === 0) {
      test.skip()
      return
    }

    // Get initial theme
    const htmlElement = page.locator('html')
    const initialTheme = await htmlElement.getAttribute('class') || ''
    
    // Click theme toggle
    await themeToggle.first().click()
    
    // Wait for theme change
    await page.waitForTimeout(500)
    
    // Check theme changed
    const newTheme = await htmlElement.getAttribute('class') || ''
    expect(newTheme).not.toBe(initialTheme)
  })

  test('data refreshes when refresh button clicked', async ({ page }) => {
    // Look for refresh button
    const refreshButton = page.locator('[data-testid="refresh"], button[aria-label*="refresh"], button:has-text("Refresh")')
    
    if (await refreshButton.count() === 0) {
      test.skip()
      return
    }

    // Click refresh
    await refreshButton.first().click()
    
    // Check for loading indicator
    const loadingIndicator = page.locator('.loading, .spinner, [role="status"]')
    if (await loadingIndicator.count() > 0) {
      await expect(loadingIndicator.first()).toBeVisible()
      await expect(loadingIndicator.first()).not.toBeVisible({ timeout: 10000 })
    }
    
    // Data should still be visible after refresh
    await expect(page.getByText(/tổng dự án|total projects/i)).toBeVisible()
  })

  test('handles network errors gracefully', async ({ page, context }) => {
    // Intercept API calls and make them fail
    await context.route('**/api/**', route => route.abort())
    
    // Reload page
    await page.reload()
    
    // Should show error message or fallback UI
    const errorMessage = page.locator('[data-testid="error"], .error-message, .alert-danger')
    if (await errorMessage.count() > 0) {
      await expect(errorMessage.first()).toBeVisible()
    }
  })

  test('accessibility - keyboard navigation', async ({ page }) => {
    // Tab through interactive elements
    await page.keyboard.press('Tab')
    await page.waitForTimeout(100)
    
    // Check focus is visible
    const focusedElement = page.locator(':focus')
    await expect(focusedElement).toBeVisible()
    
    // Tab to next element
    await page.keyboard.press('Tab')
    await page.waitForTimeout(100)
    
    // Should move to next interactive element
    const newFocusedElement = page.locator(':focus')
    await expect(newFocusedElement).toBeVisible()
  })

  test('accessibility - ARIA labels', async ({ page }) => {
    // Check main navigation has proper ARIA labels
    const mainNav = page.locator('nav[aria-label], [role="navigation"]')
    await expect(mainNav.first()).toBeVisible()
    
    // Check buttons have accessible names
    const buttons = page.locator('button')
    const buttonCount = await buttons.count()
    
    for (let i = 0; i < Math.min(buttonCount, 5); i++) {
      const button = buttons.nth(i)
      const ariaLabel = await button.getAttribute('aria-label')
      const textContent = await button.textContent()
      
      // Button should have either aria-label or text content
      expect(ariaLabel || textContent?.trim()).toBeTruthy()
    }
  })
})