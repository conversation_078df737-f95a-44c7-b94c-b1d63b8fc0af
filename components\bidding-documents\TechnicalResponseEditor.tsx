'use client'

import { useState } from 'react'
import { Dialog } from '@/components/ui/Dialog'
import { Button } from '@/components/ui/Button'
import { FileText, Save, X } from 'lucide-react'
import { useUpdateTechnicalResponseData } from '@/hooks/queries/useBiddingDocuments'
import type { BiddingDocument } from '@/types/biddingDocument'

interface TechnicalResponseEditorProps {
  isOpen: boolean
  onClose: () => void
  document: BiddingDocument
}

export function TechnicalResponseEditor({
  isOpen,
  onClose,
  document
}: TechnicalResponseEditorProps) {
  const [editedData, setEditedData] = useState(
    document.technicalResponseDocument?.aiProcessedData || {}
  )
  
  const updateMutation = useUpdateTechnicalResponseData()

  const handleSave = async () => {
    try {
      await updateMutation.mutateAsync({
        id: document.id,
        data: editedData
      })
      onClose()
    } catch (error) {
      // Error handled by mutation
    }
  }

  return (
    <Dialog
      isOpen={isOpen}
      onClose={onClose}
      title="Chỉnh sửa tài liệu đáp <PERSON>ng kỹ thuật"
    >
      <div className="space-y-4">
        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="flex items-start gap-3">
            <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0" />
            <div>
              <p className="text-sm text-blue-800 dark:text-blue-300 font-medium">
                Chỉnh sửa dữ liệu kỹ thuật
              </p>
              <p className="text-sm text-blue-700 dark:text-blue-400 mt-1">
                Bạn có thể thêm, sửa hoặc xóa các trường dữ liệu kỹ thuật mà AI đã tạo.
              </p>
            </div>
          </div>
        </div>

        {/* Technical Data Editor */}
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-4">
            Dữ liệu kỹ thuật
          </h3>
          
          {/* This is a simplified editor - in production, you'd want a more sophisticated JSON editor */}
          <textarea
            value={JSON.stringify(editedData, null, 2)}
            onChange={(e) => {
              try {
                const parsed = JSON.parse(e.target.value)
                setEditedData(parsed)
              } catch (error) {
                // Invalid JSON, ignore
              }
            }}
            rows={15}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100 font-mono text-sm"
          />
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={updateMutation.isPending}
          >
            <X className="w-4 h-4 mr-2" />
            Hủy
          </Button>
          <Button
            type="button"
            onClick={handleSave}
            disabled={updateMutation.isPending}
          >
            <Save className="w-4 h-4 mr-2" />
            {updateMutation.isPending ? 'Saving...' : 'Lưu thay đổi'}
          </Button>
        </div>
      </div>
    </Dialog>
  )
}