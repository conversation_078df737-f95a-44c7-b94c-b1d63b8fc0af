'use client';

import { useParams, useRouter } from 'next/navigation';
import { useEquipment } from '@/hooks/queries/useEquipment';
import { useAuth } from '@/hooks/queries/useAuth';
import { Button } from '@/components/ui/Button';
import { MainLayout } from '@/components/layout/MainLayout';
import { GoogleDriveDocumentList } from '@/components/equipment/GoogleDriveDocumentList';
import { ArrowLeft, Edit, FileText } from 'lucide-react';

export default function EquipmentDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const equipmentId = params.id as string;

  const { data: equipment, isLoading, error } = useEquipment(equipmentId);

  const canEdit = user?.role === 'ADMIN' || user?.role === 'USER';

  if (isLoading) {
    return (
      <MainLayout>
        <div className="p-6">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error || !equipment) {
    return (
      <MainLayout>
        <div className="p-6">
          <div className="text-center text-red-600">
            Không tìm thấy thiết bị hoặc có lỗi xảy ra
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="p-6">
        <div className="mb-6">
          <div className="flex items-center gap-4 mb-4">
            <Button
            type="button"
            variant="outline"
            onClick={() => router.push('/equipment')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Quay lại
          </Button>
          {canEdit && (
            <Button
              type="button"
              onClick={() => router.push(`/equipment/${equipmentId}/edit`)}
              className="flex items-center gap-2"
            >
              <Edit className="w-4 h-4" />
              Chỉnh sửa
            </Button>
          )}
          </div>
          
          <h1 className="text-2xl font-bold text-gray-900">{equipment.name}</h1>
          <p className="text-gray-600 mt-1">Mã thiết bị: {equipment.equipmentCode}</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-4">Thông tin cơ bản</h2>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-500">Danh mục</label>
                <p className="mt-1 text-gray-900">{equipment.catalog?.catalogName}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-500">Trạng thái</label>
                <p className="mt-1">
                  <span
                    className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      equipment.status === 'ACTIVE'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {equipment.status === 'ACTIVE' ? 'Hoạt động' : 'Không hoạt động'}
                  </span>
                </p>
              </div>
              
              {equipment.model && (
                <div>
                  <label className="block text-sm font-medium text-gray-500">Model</label>
                  <p className="mt-1 text-gray-900">{equipment.model}</p>
                </div>
              )}
              
              {equipment.manufacturer && (
                <div>
                  <label className="block text-sm font-medium text-gray-500">Nhà sản xuất</label>
                  <p className="mt-1 text-gray-900">{equipment.manufacturer}</p>
                </div>
              )}
              
              {equipment.unit && (
                <div>
                  <label className="block text-sm font-medium text-gray-500">Đơn vị tính</label>
                  <p className="mt-1 text-gray-900">{equipment.unit}</p>
                </div>
              )}
              
              {equipment.description && (
                <div className="col-span-2">
                  <label className="block text-sm font-medium text-gray-500">Mô tả</label>
                  <p className="mt-1 text-gray-900">{equipment.description}</p>
                </div>
              )}
            </div>
          </div>

          {/* Specifications and Price */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-4">Thông số kỹ thuật và giá</h2>
            
            <div className="space-y-4">
              {equipment.price !== undefined && equipment.price !== null && (
                <div>
                  <label className="block text-sm font-medium text-gray-500">Giá</label>
                  <p className="mt-1 text-gray-900 text-lg font-semibold">
                    {new Intl.NumberFormat('vi-VN').format(equipment.price)} VNĐ
                  </p>
                </div>
              )}
              
              {equipment.specifications && Object.keys(equipment.specifications).length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-2">Thông số kỹ thuật</label>
                  <div className="bg-gray-50 rounded p-3">
                    {Object.entries(equipment.specifications as Record<string, string | number | boolean>).map(([key, value]) => (
                      <div key={key} className="flex justify-between py-2 border-b border-gray-200 last:border-0">
                        <span className="text-gray-600">{key}:</span>
                        <span className="font-medium text-gray-900">{String(value)}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {!equipment.specifications || Object.keys(equipment.specifications).length === 0 && (
                <p className="text-gray-500 italic">Chưa có thông số kỹ thuật</p>
              )}
            </div>
          </div>

          {/* Documents Section */}
          {equipment.documents && equipment.documents.length > 0 && (
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="flex items-center justify-center w-8 h-8 bg-green-100 rounded-lg">
                  <FileText className="w-5 h-5 text-green-600" />
                </div>
                <h2 className="text-lg font-semibold">Tài liệu đính kèm</h2>
              </div>
              <GoogleDriveDocumentList
                documents={equipment.documents}
                canDelete={false}
                canEdit={false}
              />
            </div>
          )}
        </div>

        {/* Sidebar Information */}
        <div className="space-y-6">
          {/* Metadata */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold mb-4">Thông tin hệ thống</h3>
            
            <div className="space-y-3 text-sm">
              {equipment.creator && (
                <div>
                  <label className="block font-medium text-gray-500">Người tạo</label>
                  <p className="mt-1 text-gray-900">{equipment.creator.name}</p>
                </div>
              )}
              
              <div>
                <label className="block font-medium text-gray-500">Ngày tạo</label>
                <p className="mt-1 text-gray-900">
                  {new Date(equipment.createdAt).toLocaleString('vi-VN')}
                </p>
              </div>
              
              <div>
                <label className="block font-medium text-gray-500">Cập nhật lần cuối</label>
                <p className="mt-1 text-gray-900">
                  {new Date(equipment.updatedAt).toLocaleString('vi-VN')}
                </p>
              </div>
            </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}