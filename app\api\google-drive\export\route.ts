import { NextRequest, NextResponse } from 'next/server';
import { googleDriveServiceNoLogin } from '@/services/googleDriveServiceNoLogin';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const fileId = searchParams.get('fileId');
    const mimeType = searchParams.get('mimeType');

    if (!fileId) {
      return NextResponse.json(
        { error: 'File ID is required' },
        { status: 400 }
      );
    }

    // Determine export format based on mime type
    let exportMimeType = 'application/pdf'; // Default to PDF
    let extension = 'pdf';

    if (mimeType) {
      switch (mimeType) {
        case 'application/vnd.google-apps.document':
          exportMimeType = searchParams.get('format') === 'docx' 
            ? 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            : 'application/pdf';
          extension = searchParams.get('format') === 'docx' ? 'docx' : 'pdf';
          break;
        case 'application/vnd.google-apps.spreadsheet':
          exportMimeType = searchParams.get('format') === 'xlsx'
            ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            : 'application/pdf';
          extension = searchParams.get('format') === 'xlsx' ? 'xlsx' : 'pdf';
          break;
        case 'application/vnd.google-apps.presentation':
          exportMimeType = searchParams.get('format') === 'pptx'
            ? 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
            : 'application/pdf';
          extension = searchParams.get('format') === 'pptx' ? 'pptx' : 'pdf';
          break;
        default:
          // For non-Google Workspace files, use direct download
          const fileData = await googleDriveServiceNoLogin.getFile(fileId);
          
          if (fileData.webContentLink) {
            return NextResponse.redirect(fileData.webContentLink);
          } else {
            // If no webContentLink, try to get the file content directly
            const response = await googleDriveServiceNoLogin.downloadFile(fileId);
            
            return new NextResponse(response, {
              headers: {
                'Content-Type': fileData.mimeType || 'application/octet-stream',
                'Content-Disposition': `attachment; filename="${fileData.name}"`,
              },
            });
          }
      }
    }

    // Export Google Workspace file
    const { data, fileName } = await googleDriveServiceNoLogin.exportFile(fileId, exportMimeType);

    return new NextResponse(data, {
      headers: {
        'Content-Type': exportMimeType,
        'Content-Disposition': `attachment; filename="${fileName || `export.${extension}`}"`,
      },
    });
  } catch (error) {
    console.error('Error exporting file:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to export file' },
      { status: 500 }
    );
  }
}