import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from '@/lib/auth-server';
import { prisma } from '@/lib/db';
import { unlink } from 'fs/promises';
import path from 'path';

// DELETE: Delete a document
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; docId: string }> }
) {
  try {
    const { id: equipmentId, docId } = await params;
    
    // Get user session
    const session = await getServerSession(request);
    if (!session?.userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user has permission (ADMIN or USER role)
    const user = await prisma.user.findUnique({
      where: { id: session.userId }
    });

    if (!user || (user.role !== 'ADMIN' && user.role !== 'USER')) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    // Find the document
    const document = await prisma.equipmentDocument.findUnique({
      where: { id: docId }
    });

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    // Verify document belongs to the equipment
    if (document.equipmentId !== equipmentId) {
      return NextResponse.json(
        { error: 'Document does not belong to this equipment' },
        { status: 400 }
      );
    }

    // Delete file from disk
    try {
      const filePath = path.join(process.cwd(), 'public', document.fileUrl);
      await unlink(filePath);
    } catch (error) {
      console.error('Error deleting file from disk:', error);
      // Continue with database deletion even if file deletion fails
    }

    // Delete from database
    await prisma.equipmentDocument.delete({
      where: { id: docId }
    });

    return NextResponse.json({
      success: true,
      message: 'Document deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting document:', error);
    return NextResponse.json(
      { error: 'Failed to delete document' },
      { status: 500 }
    );
  }
}