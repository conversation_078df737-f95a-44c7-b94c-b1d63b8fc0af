import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { EquipmentForm } from '@/components/equipment/EquipmentForm';
import { EquipmentList } from '@/components/equipment/EquipmentList';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/queries/useAuth';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useParams: jest.fn(),
  usePathname: jest.fn(),
}));

// Mock auth hook
jest.mock('@/hooks/queries/useAuth', () => ({
  useAuth: jest.fn(),
}));

// Mock equipment hooks
jest.mock('@/hooks/queries/useEquipment', () => ({
  useEquipments: jest.fn(() => ({
    data: {
      data: [
        {
          id: '1',
          equipmentCode: 'TB001',
          name: 'Test Equipment',
          description: 'Test Description',
          manufacturer: 'Test Manufacturer',
          catalogId: 'cat1',
          catalog: { catalogName: 'Test Catalog' },
          status: 'ACTIVE',
          price: 1000000,
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01',
        },
      ],
      total: 1,
      page: 1,
      limit: 10,
      totalPages: 1,
    },
    isLoading: false,
    error: null,
  })),
  useEquipment: jest.fn((id) => ({
    data: {
      id,
      equipmentCode: 'TB001',
      name: 'Test Equipment',
      description: 'Test Description',
      manufacturer: 'Test Manufacturer',
      catalogId: 'cat1',
      status: 'ACTIVE',
      price: 1000000,
      specifications: { power: '100W', voltage: '220V' },
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
    },
    isLoading: false,
    error: null,
  })),
  useCreateEquipment: jest.fn(() => ({
    mutateAsync: jest.fn(),
    isPending: false,
  })),
  useUpdateEquipment: jest.fn(() => ({
    mutateAsync: jest.fn(),
    isPending: false,
  })),
  useDeleteEquipment: jest.fn(() => ({
    mutateAsync: jest.fn(),
    isPending: false,
  })),
  useImportEquipment: jest.fn(() => ({
    mutateAsync: jest.fn(),
    isPending: false,
  })),
  useExportEquipment: jest.fn(() => ({
    mutateAsync: jest.fn(),
    isPending: false,
  })),
}));

// Mock catalog hooks
jest.mock('@/hooks/queries/useCatalogs', () => ({
  useCatalogs: jest.fn(() => ({
    data: {
      catalogs: [
        { id: 'cat1', catalogCode: 'CAT001', catalogName: 'Test Catalog' },
        { id: 'cat2', catalogCode: 'CAT002', catalogName: 'Another Catalog' },
      ],
    },
    isLoading: false,
    error: null,
  })),
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('EquipmentForm', () => {
  const mockPush = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue({ push: mockPush });
    (useAuth as jest.Mock).mockReturnValue({ user: { role: 'ADMIN' } });
  });

  it('renders equipment form correctly', () => {
    render(<EquipmentForm />, { wrapper: createWrapper() });

    expect(screen.getByLabelText(/Mã thiết bị/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Tên thiết bị/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Danh mục/i)).toBeInTheDocument();
    expect(screen.getByText(/Thông số kỹ thuật/i)).toBeInTheDocument();
  });

  it('displays equipment data when editing', () => {
    const equipment = {
      id: '1',
      equipmentCode: 'TB001',
      name: 'Test Equipment',
      description: 'Test Description',
      manufacturer: 'Test Manufacturer',
      catalogId: 'cat1',
      status: 'ACTIVE' as const,
      price: 1000000,
      specifications: { power: '100W' },
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
    };

    render(<EquipmentForm equipment={equipment} />, { wrapper: createWrapper() });

    expect(screen.getByDisplayValue('TB001')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Test Equipment')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Test Description')).toBeInTheDocument();
  });

  it('handles form submission correctly', async () => {
    const { useCreateEquipment } = require('@/hooks/queries/useEquipment');
    const mockMutate = jest.fn().mockResolvedValue({});
    useCreateEquipment.mockReturnValue({
      mutateAsync: mockMutate,
      isPending: false,
    });

    render(<EquipmentForm />, { wrapper: createWrapper() });

    fireEvent.change(screen.getByLabelText(/Mã thiết bị/i), {
      target: { value: 'TB002' },
    });
    fireEvent.change(screen.getByLabelText(/Tên thiết bị/i), {
      target: { value: 'New Equipment' },
    });

    const submitButton = screen.getByRole('button', { name: /Tạo mới/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockMutate).toHaveBeenCalled();
    });
  });

  it('handles cancel button correctly', () => {
    render(<EquipmentForm />, { wrapper: createWrapper() });

    const cancelButton = screen.getByRole('button', { name: /Hủy/i });
    fireEvent.click(cancelButton);

    expect(mockPush).toHaveBeenCalledWith('/equipment');
  });
});

describe('EquipmentList', () => {
  const mockPush = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue({ push: mockPush });
    (useAuth as jest.Mock).mockReturnValue({ user: { role: 'ADMIN' } });
  });

  it('renders equipment list correctly', () => {
    render(<EquipmentList />, { wrapper: createWrapper() });

    expect(screen.getByPlaceholderText(/Tìm kiếm/i)).toBeInTheDocument();
    expect(screen.getByText('TB001')).toBeInTheDocument();
    expect(screen.getByText('Test Equipment')).toBeInTheDocument();
  });

  it('displays loading state', () => {
    const { useEquipments } = require('@/hooks/queries/useEquipment');
    useEquipments.mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
    });

    render(<EquipmentList />, { wrapper: createWrapper() });

    expect(screen.getByRole('status')).toBeInTheDocument();
  });

  it('displays error state', () => {
    const { useEquipments } = require('@/hooks/queries/useEquipment');
    useEquipments.mockReturnValue({
      data: null,
      isLoading: false,
      error: new Error('Failed to load'),
    });

    render(<EquipmentList />, { wrapper: createWrapper() });

    expect(screen.getByText(/Lỗi khi tải danh sách thiết bị/i)).toBeInTheDocument();
  });

  it('displays empty state', () => {
    const { useEquipments } = require('@/hooks/queries/useEquipment');
    useEquipments.mockReturnValue({
      data: { data: [], total: 0, page: 1, limit: 10, totalPages: 0 },
      isLoading: false,
      error: null,
    });

    render(<EquipmentList />, { wrapper: createWrapper() });

    expect(screen.getByText(/Không tìm thấy thiết bị nào/i)).toBeInTheDocument();
  });

  it('handles view action correctly', () => {
    render(<EquipmentList />, { wrapper: createWrapper() });

    const viewButton = screen.getByTitle('Xem chi tiết');
    fireEvent.click(viewButton);

    expect(mockPush).toHaveBeenCalledWith('/equipment/1');
  });

  it('handles edit action correctly', () => {
    render(<EquipmentList />, { wrapper: createWrapper() });

    const editButton = screen.getByTitle('Chỉnh sửa');
    fireEvent.click(editButton);

    expect(mockPush).toHaveBeenCalledWith('/equipment/1/edit');
  });

  it('handles delete action correctly', async () => {
    render(<EquipmentList />, { wrapper: createWrapper() });

    const deleteButton = screen.getByTitle('Xóa');
    fireEvent.click(deleteButton);

    // Check if delete dialog is shown
    expect(screen.getByText(/Bạn có chắc chắn muốn xóa/i)).toBeInTheDocument();

    // Confirm delete
    const confirmButton = screen.getByRole('button', { name: /^Xóa$/i });
    fireEvent.click(confirmButton);

    const { useDeleteEquipment } = require('@/hooks/queries/useEquipment');
    const mockDeleteMutate = useDeleteEquipment().mutateAsync;

    await waitFor(() => {
      expect(mockDeleteMutate).toHaveBeenCalledWith('1');
    });
  });

  it('handles add new equipment correctly', () => {
    render(<EquipmentList />, { wrapper: createWrapper() });

    const addButton = screen.getByRole('button', { name: /Thêm mới/i });
    fireEvent.click(addButton);

    expect(mockPush).toHaveBeenCalledWith('/equipment/new');
  });

  it('filters equipment by search term', async () => {
    render(<EquipmentList />, { wrapper: createWrapper() });

    const searchInput = screen.getByPlaceholderText(/Tìm kiếm/i);
    fireEvent.change(searchInput, { target: { value: 'Test' } });

    // Wait for debounce
    await waitFor(() => {
      const { useEquipments } = require('@/hooks/queries/useEquipment');
      expect(useEquipments).toHaveBeenCalledWith(
        expect.objectContaining({
          search: 'Test',
        })
      );
    }, { timeout: 1000 });
  });

  it('respects user permissions for actions', () => {
    (useAuth as jest.Mock).mockReturnValue({ user: { role: 'VIEWER' } });

    render(<EquipmentList />, { wrapper: createWrapper() });

    // Should not show edit and delete buttons for viewers
    expect(screen.queryByTitle('Chỉnh sửa')).not.toBeInTheDocument();
    expect(screen.queryByTitle('Xóa')).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /Thêm mới/i })).not.toBeInTheDocument();

    // Should still show view button
    expect(screen.getByTitle('Xem chi tiết')).toBeInTheDocument();
  });
});