import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from '@/lib/auth-server';
import { prisma } from '@/lib/db';
import { readFile, access, constants } from 'fs/promises';
import { join, resolve } from 'path';
import { existsSync, statSync } from 'fs';

const AI_API_URL = process.env.AI_API_URL || 'https://dev.vietmy.lifesup.ai/service/query/query-from-pdf';
const AI_API_TIMEOUT = parseInt(process.env.AI_API_TIMEOUT || '120000'); // 2 minutes default
const MAX_FILE_SIZE = parseInt(process.env.MAX_PDF_SIZE || '52428800'); // 50MB default

// Enhanced logging function
function logError(context: string, error: any, additionalInfo?: any) {
  const timestamp = new Date().toISOString();
  console.error(`[${timestamp}] AI_PROFILE_ERROR [${context}]:`, {
    message: error?.message || 'Unknown error',
    stack: error?.stack,
    code: error?.code,
    ...additionalInfo
  });
}

function logInfo(context: string, message: string, data?: any) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] AI_PROFILE_INFO [${context}]: ${message}`, data || '');
}

export async function POST(request: NextRequest) {
  const requestStartTime = Date.now();
  let fileBuffer: Buffer | null = null;
  
  try {
    logInfo('REQUEST_START', 'AI profile creation request started');
    
    // Check authentication
    const session = await getServerSession(request);
    if (!session?.userId) {
      logInfo('AUTH_FAILED', 'No valid session found');
      return NextResponse.json(
        { error: 'Không có quyền truy cập' },
        { status: 401 }
      );
    }
    
    logInfo('AUTH_SUCCESS', `User authenticated: ${session.userId}`);

    // Get form data
    const formData = await request.formData();
    const fileId = formData.get('fileId') as string;
    const equipmentItems = formData.get('equipmentItems') as string;

    logInfo('FORM_DATA', 'Form data received', { fileId, equipmentItemsLength: equipmentItems?.length });

    if (!fileId || !equipmentItems) {
      logError('VALIDATION_FAILED', new Error('Missing required fields'), { fileId, equipmentItems: !!equipmentItems });
      return NextResponse.json(
        { error: 'Thiếu thông tin bắt buộc' },
        { status: 400 }
      );
    }

    // Parse equipment items
    let parsedEquipmentItems;
    try {
      parsedEquipmentItems = JSON.parse(equipmentItems);
      logInfo('EQUIPMENT_PARSED', `Successfully parsed ${parsedEquipmentItems.length} equipment items`);
    } catch (error) {
      logError('JSON_PARSE_FAILED', error, { equipmentItemsRaw: equipmentItems });
      return NextResponse.json(
        { error: 'Định dạng danh sách thiết bị không hợp lệ' },
        { status: 400 }
      );
    }

    // Get the attachment file info from database
    logInfo('DB_QUERY', 'Fetching attachment from database');
    const attachment = await prisma.biddingDocumentAttachment.findUnique({
      where: { id: fileId },
      include: {
        biddingDocument: true
      }
    });

    if (!attachment) {
      logError('ATTACHMENT_NOT_FOUND', new Error('Attachment not found'), { fileId });
      return NextResponse.json(
        { error: 'Không tìm thấy tệp đính kèm' },
        { status: 404 }
      );
    }

    logInfo('ATTACHMENT_FOUND', 'Attachment retrieved from database', {
      id: attachment.id,
      fileName: attachment.fileName,
      fileUrl: attachment.fileUrl,
      source: attachment.source,
      mimeType: attachment.mimeType,
      fileSize: attachment.fileSize
    });

    // Check if the attachment is a PDF from server (not Google Drive)
    if (attachment.source !== 'local' || !attachment.fileUrl.endsWith('.pdf')) {
      logError('FILE_TYPE_INVALID', new Error('Invalid file type or source'), {
        source: attachment.source,
        fileUrl: attachment.fileUrl,
        fileName: attachment.fileName
      });
      return NextResponse.json(
        { error: 'Chỉ hỗ trợ tệp PDF từ máy chủ' },
        { status: 400 }
      );
    }

    // Get the actual file path with comprehensive path validation
    let cleanFileUrl = attachment.fileUrl;
    if (cleanFileUrl.startsWith('/')) {
      cleanFileUrl = cleanFileUrl.slice(1);
    }
    
    // Construct and validate file path
    const basePath = process.cwd();
    const fullPath = resolve(join(basePath, cleanFileUrl));
    
    logInfo('FILE_PATH', 'File path resolution', {
      originalFileUrl: attachment.fileUrl,
      cleanFileUrl,
      basePath,
      fullPath,
      cwd: process.cwd(),
      nodeEnv: process.env.NODE_ENV
    });
    
    // Security check: ensure the resolved path is within the expected directory
    if (!fullPath.startsWith(basePath)) {
      logError('SECURITY_PATH_TRAVERSAL', new Error('Path traversal attempt detected'), {
        fullPath,
        basePath
      });
      return NextResponse.json(
        { error: 'Đường dẫn tệp không hợp lệ' },
        { status: 400 }
      );
    }
    
    // Check if file exists and get file stats
    let fileStats;
    try {
      await access(fullPath, constants.F_OK | constants.R_OK);
      fileStats = statSync(fullPath);
      logInfo('FILE_ACCESS', 'File accessibility verified', {
        exists: true,
        size: fileStats.size,
        isFile: fileStats.isFile(),
        readable: true
      });
    } catch (error: any) {
      logError('FILE_ACCESS_FAILED', error, {
        fullPath,
        errorCode: error.code,
        errorMessage: error.message
      });
      
      const errorMsg = error.code === 'ENOENT' 
        ? 'Không tìm thấy tệp trên máy chủ' 
        : error.code === 'EACCES'
        ? 'Không có quyền đọc tệp'
        : `Lỗi truy cập tệp: ${error.message}`;
        
      return NextResponse.json(
        { 
          error: 'Không thể truy cập tệp',
          details: errorMsg
        },
        { status: 500 }
      );
    }
    
    // Check file size
    if (fileStats.size > MAX_FILE_SIZE) {
      logError('FILE_SIZE_TOO_LARGE', new Error('File size exceeds limit'), {
        fileSize: fileStats.size,
        maxSize: MAX_FILE_SIZE
      });
      return NextResponse.json(
        { 
          error: 'Tệp quá lớn',
          details: `Kích thước tệp ${(fileStats.size / 1024 / 1024).toFixed(2)}MB vượt quá giới hạn ${(MAX_FILE_SIZE / 1024 / 1024).toFixed(2)}MB`
        },
        { status: 400 }
      );
    }
    
    // Read the file with better error handling
    try {
      logInfo('FILE_READ_START', `Reading file of size ${fileStats.size} bytes`);
      fileBuffer = await readFile(fullPath);
      logInfo('FILE_READ_SUCCESS', `Successfully read ${fileBuffer.length} bytes`);
    } catch (error: any) {
      logError('FILE_READ_FAILED', error, {
        fullPath,
        fileSize: fileStats?.size,
        errorCode: error.code
      });
      
      const errorMsg = error.code === 'ENOENT' 
        ? 'Tệp không tồn tại'
        : error.code === 'EACCES'
        ? 'Không có quyền đọc tệp'
        : error.code === 'EMFILE' || error.code === 'ENFILE'
        ? 'Hệ thống quá tải, vui lòng thử lại sau'
        : `Lỗi đọc tệp: ${error.message}`;
        
      return NextResponse.json(
        { 
          error: 'Không thể đọc tệp',
          details: errorMsg
        },
        { status: 500 }
      );
    }

    // Create form data for AI API with validation
    let aiFormData: FormData;
    let blob: Blob;
    
    try {
      logInfo('FORM_DATA_CREATION', 'Creating form data for AI API');
      blob = new Blob([fileBuffer], { type: 'application/pdf' });
      aiFormData = new FormData();
      aiFormData.append('file', blob, attachment.fileName);
      
      // Convert equipment items to topic string
      const topic = JSON.stringify(parsedEquipmentItems);
      aiFormData.append('topic', topic);
      
      logInfo('FORM_DATA_CREATED', 'AI API form data created', {
        fileName: attachment.fileName,
        blobSize: blob.size,
        topicLength: topic.length
      });
    } catch (error: any) {
      logError('FORM_DATA_CREATION_FAILED', error);
      return NextResponse.json(
        { error: 'Không thể chuẩn bị dữ liệu cho AI API' },
        { status: 500 }
      );
    }

    // Call AI API with timeout and comprehensive error handling
    let aiResponse: Response;
    const aiApiStartTime = Date.now();
    
    try {
      logInfo('AI_API_CALL_START', `Calling AI API at ${AI_API_URL}`);
      
      // Create AbortController for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        controller.abort();
        logError('AI_API_TIMEOUT', new Error(`AI API call timed out after ${AI_API_TIMEOUT}ms`));
      }, AI_API_TIMEOUT);
      
      aiResponse = await fetch(AI_API_URL, {
        method: 'POST',
        headers: {
          'accept': 'application/json',
        },
        body: aiFormData,
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      const aiApiDuration = Date.now() - aiApiStartTime;
      logInfo('AI_API_RESPONSE', `AI API responded in ${aiApiDuration}ms`, {
        status: aiResponse.status,
        statusText: aiResponse.statusText,
        ok: aiResponse.ok
      });
      
    } catch (error: any) {
      const aiApiDuration = Date.now() - aiApiStartTime;
      
      if (error.name === 'AbortError') {
        logError('AI_API_TIMEOUT', error, { duration: aiApiDuration, timeout: AI_API_TIMEOUT });
        return NextResponse.json(
          { 
            error: 'AI API bị timeout',
            details: `Quá trình xử lý vượt quá ${AI_API_TIMEOUT / 1000} giây`
          },
          { status: 504 }
        );
      }
      
      logError('AI_API_NETWORK_ERROR', error, {
        duration: aiApiDuration,
        url: AI_API_URL,
        errorName: error.name,
        errorMessage: error.message
      });
      
      const errorMsg = error.message?.includes('fetch')
        ? 'Không thể kết nối đến AI API'
        : error.message?.includes('ENOTFOUND')
        ? 'Không tìm thấy máy chủ AI'
        : error.message?.includes('ECONNREFUSED')
        ? 'Máy chủ AI từ chối kết nối'
        : `Lỗi mạng: ${error.message}`;
        
      return NextResponse.json(
        { 
          error: 'Lỗi kết nối AI API',
          details: errorMsg
        },
        { status: 502 }
      );
    }

    // Handle AI API response
    if (!aiResponse.ok) {
      let errorText = '';
      let errorDetails = '';
      
      try {
        errorText = await aiResponse.text();
        logError('AI_API_ERROR_RESPONSE', new Error(`AI API returned ${aiResponse.status}`), {
          status: aiResponse.status,
          statusText: aiResponse.statusText,
          responseText: errorText
        });
        
        // Try to parse as JSON for better error details
        try {
          const errorJson = JSON.parse(errorText);
          errorDetails = errorJson.detail || errorJson.message || errorText;
        } catch {
          errorDetails = errorText;
        }
      } catch (textError) {
        logError('AI_API_ERROR_TEXT_READ_FAILED', textError);
        errorDetails = `HTTP ${aiResponse.status} ${aiResponse.statusText}`;
      }
      
      return NextResponse.json(
        { 
          error: 'Xử lý AI thất bại',
          details: errorDetails || `Server trả về lỗi ${aiResponse.status}`
        },
        { status: 500 }
      );
    }

    // Parse AI response
    let aiResult: any;
    try {
      aiResult = await aiResponse.json();
      logInfo('AI_API_SUCCESS', 'AI API response parsed successfully', {
        hasExcelFile: !!aiResult.excel_file_path,
        hasDownloadUrl: !!aiResult.download_url,
        hasFileName: !!aiResult.excel_file_name,
        responseKeys: Object.keys(aiResult)
      });
    } catch (error: any) {
      logError('AI_API_JSON_PARSE_FAILED', error);
      return NextResponse.json(
        { error: 'Phản hồi từ AI API không hợp lệ' },
        { status: 500 }
      );
    }

    // Prepare final result
    const result = {
      excelFileUrl: aiResult.excel_file_path || aiResult.download_url,
      excelFileName: aiResult.excel_file_name,
      gcsBucket: aiResult.gcs_bucket,
      gcsFilename: aiResult.gcs_filename,
      downloadUrl: aiResult.download_url || aiResult.excel_file_path
    };
    
    const totalDuration = Date.now() - requestStartTime;
    logInfo('REQUEST_SUCCESS', 'AI profile generation completed successfully', {
      totalDuration,
      result: {
        hasExcelFileUrl: !!result.excelFileUrl,
        hasFileName: !!result.excelFileName,
        hasDownloadUrl: !!result.downloadUrl,
        fileName: result.excelFileName
      }
    });

    // Return the result
    return NextResponse.json({
      success: true,
      result
    });
    
  } catch (error: any) {
    const totalDuration = Date.now() - requestStartTime;
    
    // Clean up resources
    if (fileBuffer) {
      fileBuffer = null;
    }
    
    logError('REQUEST_FAILED', error, {
      totalDuration,
      stack: error?.stack,
      name: error?.name,
      message: error?.message
    });
    
    // Return user-friendly error message
    const isNetworkError = error?.message?.includes('fetch') || error?.code === 'ENOTFOUND';
    const isTimeoutError = error?.name === 'AbortError' || error?.message?.includes('timeout');
    const isFileError = error?.code === 'ENOENT' || error?.code === 'EACCES';
    
    let errorMessage = 'Không thể tạo hồ sơ AI';
    if (isNetworkError) {
      errorMessage = 'Lỗi kết nối mạng';
    } else if (isTimeoutError) {
      errorMessage = 'Quá trình xử lý bị timeout';
    } else if (isFileError) {
      errorMessage = 'Lỗi truy cập tệp';
    }
    
    return NextResponse.json(
      { 
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? error?.message : undefined
      },
      { status: 500 }
    );
  }
}