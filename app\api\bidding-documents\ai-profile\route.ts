import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from '@/lib/auth-server';
import { prisma } from '@/lib/db';

const AI_API_URL = 'https://dev.vietmy.lifesup.ai/service/query/query-from-pdf';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(request);
    if (!session?.userId) {
      return NextResponse.json(
        { error: 'Không có quyền truy cập' },
        { status: 401 }
      );
    }

    // Get form data
    const formData = await request.formData();
    const fileId = formData.get('fileId') as string;
    const equipmentItems = formData.get('equipmentItems') as string;

    if (!fileId || !equipmentItems) {
      return NextResponse.json(
        { error: 'Thiếu thông tin bắt buộc' },
        { status: 400 }
      );
    }

    // Parse equipment items
    let parsedEquipmentItems;
    try {
      parsedEquipmentItems = JSON.parse(equipmentItems);
    } catch (error) {
      return NextResponse.json(
        { error: 'Định dạng danh sách thiết bị không hợp lệ' },
        { status: 400 }
      );
    }

    // Get the attachment file info from database
    const attachment = await prisma.biddingDocumentAttachment.findUnique({
      where: { id: fileId },
      include: {
        biddingDocument: true
      }
    });

    if (!attachment) {
      return NextResponse.json(
        { error: 'Không tìm thấy tệp đính kèm' },
        { status: 404 }
      );
    }

    // Check if the attachment is a PDF from server (not Google Drive)
    if (attachment.source !== 'local' || !attachment.fileUrl.endsWith('.pdf')) {
      return NextResponse.json(
        { error: 'Chỉ hỗ trợ tệp PDF từ máy chủ' },
        { status: 400 }
      );
    }

    // Get the actual file path
    // Remove leading slash from fileUrl if present
    const fileUrl = attachment.fileUrl.startsWith('/') ? attachment.fileUrl.slice(1) : attachment.fileUrl;
    
    // Read the file
    const fs = require('fs').promises;
    const path = require('path');
    const fullPath = path.join(process.cwd(), fileUrl);
    
    let fileBuffer;
    try {
      fileBuffer = await fs.readFile(fullPath);
    } catch (error: any) {
      console.error('Error reading file:', error);
      console.error('Attempted path:', fullPath);
      console.error('Attachment info:', {
        id: attachment.id,
        fileName: attachment.fileName,
        fileUrl: attachment.fileUrl,
        source: attachment.source
      });
      return NextResponse.json(
        { 
          error: 'Không thể đọc tệp',
          details: error.code === 'ENOENT' ? 'Không tìm thấy tệp trên máy chủ' : error.message
        },
        { status: 500 }
      );
    }

    // Create form data for AI API
    const aiFormData = new FormData();
    const blob = new Blob([fileBuffer], { type: 'application/pdf' });
    aiFormData.append('file', blob, attachment.fileName);
    
    // Convert equipment items to topic string
    const topic = JSON.stringify(parsedEquipmentItems);
    aiFormData.append('topic', topic);

    // Call AI API
    const aiResponse = await fetch(AI_API_URL, {
      method: 'POST',
      headers: {
        'accept': 'application/json',
      },
      body: aiFormData
    });

    if (!aiResponse.ok) {
      const errorText = await aiResponse.text();
      console.error('AI API error:', errorText);
      return NextResponse.json(
        { error: 'Xử lý AI thất bại' },
        { status: 500 }
      );
    }

    const aiResult = await aiResponse.json();

    // Log the AI result for debugging
    console.log('AI API response:', aiResult);

    // Return the result
    return NextResponse.json({
      success: true,
      result: {
        excelFileUrl: aiResult.excel_file_path || aiResult.download_url,
        excelFileName: aiResult.excel_file_name,
        gcsBucket: aiResult.gcs_bucket,
        gcsFilename: aiResult.gcs_filename,
        downloadUrl: aiResult.download_url || aiResult.excel_file_path
      }
    });
  } catch (error) {
    console.error('AI profile generation error:', error);
    return NextResponse.json(
      { error: 'Không thể tạo hồ sơ AI' },
      { status: 500 }
    );
  }
}