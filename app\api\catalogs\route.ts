import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { prisma } from '@/lib/db'
import { getServerSession } from '@/lib/auth-server'
import { AuditAction } from '@/lib/auth'

// Validation schemas
const createCatalogSchema = z.object({
  catalogCode: z.string()
    .min(1, 'Catalog code is required')
    .max(50, 'Catalog code must be less than 50 characters')
    .regex(/^[A-Z0-9_]+$/, 'Catalog code must contain only uppercase letters, numbers, and underscores'),
  catalogName: z.string()
    .min(1, 'Catalog name is required')
    .max(100, 'Catalog name must be less than 100 characters'),
  description: z.string().optional(),
})

const catalogFiltersSchema = z.object({
  search: z.string().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE']).optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  sortBy: z.enum(['catalogCode', 'catalogName', 'createdAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
})

// GET /api/catalogs - Get list of catalogs
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(request)
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const filters = catalogFiltersSchema.parse({
      search: searchParams.get('search') || undefined,
      status: searchParams.get('status') || undefined,
      page: searchParams.get('page') || undefined,
      limit: searchParams.get('limit') || undefined,
      sortBy: searchParams.get('sortBy') || undefined,
      sortOrder: searchParams.get('sortOrder') || undefined,
    })

    // Build where clause
    const where: any = {}
    
    if (filters.search) {
      where.OR = [
        { catalogCode: { contains: filters.search, mode: 'insensitive' } },
        { catalogName: { contains: filters.search, mode: 'insensitive' } },
        { description: { contains: filters.search, mode: 'insensitive' } },
      ]
    }

    if (filters.status) {
      where.status = filters.status
    }

    // Get total count
    const total = await prisma.catalog.count({ where })

    // Get catalogs with pagination
    const catalogs = await prisma.catalog.findMany({
      where,
      include: {
        _count: {
          select: { equipments: true }
        },
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      },
      orderBy: {
        [filters.sortBy]: filters.sortOrder
      },
      skip: (filters.page - 1) * filters.limit,
      take: filters.limit,
    })

    // Transform the response
    const transformedCatalogs = catalogs.map(catalog => ({
      ...catalog,
      equipmentCount: catalog._count.equipments,
      _count: undefined,
    }))

    return NextResponse.json({
      catalogs: transformedCatalogs,
      total,
      page: filters.page,
      totalPages: Math.ceil(total / filters.limit),
    })
  } catch (error) {
    console.error('GET /api/catalogs error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch catalogs' },
      { status: 500 }
    )
  }
}

// POST /api/catalogs - Create new catalog
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(request)
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is admin
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Forbidden: Only admins can create catalogs' },
        { status: 403 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validation = createCatalogSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid data', details: validation.error.flatten() },
        { status: 400 }
      )
    }

    const { catalogCode, catalogName, description } = validation.data

    // Check if catalog code already exists
    const existingCode = await prisma.catalog.findUnique({
      where: { catalogCode }
    })

    if (existingCode) {
      return NextResponse.json(
        { error: 'Category code already exists', message: 'Category code already exists' },
        { status: 409 }
      )
    }

    // Check if catalog name already exists for active catalogs
    const existingName = await prisma.catalog.findFirst({
      where: {
        catalogName: {
          equals: catalogName,
          mode: 'insensitive'
        },
        status: 'ACTIVE'
      }
    })

    if (existingName) {
      return NextResponse.json(
        { error: 'Category name already exists', message: 'Category name already exists' },
        { status: 409 }
      )
    }

    // Create catalog
    const catalog = await prisma.catalog.create({
      data: {
        catalogCode,
        catalogName,
        description,
        createdBy: session.user.id,
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      }
    })

    // Log audit
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: AuditAction.CREATE,
        details: {
          entity: 'Catalog',
          catalogId: catalog.id,
          catalogCode: catalog.catalogCode,
        },
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
      }
    })

    return NextResponse.json({
      ...catalog,
      equipmentCount: 0,
    }, { status: 201 })
  } catch (error) {
    console.error('POST /api/catalogs error:', error)
    return NextResponse.json(
      { error: 'Failed to create catalog' },
      { status: 500 }
    )
  }
}