import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { verifySession } from '@/lib/auth-server';
import { googleDriveServiceNoLogin } from '@/services/googleDriveServiceNoLogin';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await verifySession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;

    const equipment = await prisma.equipment.findUnique({
      where: { id },
      include: {
        catalog: {
          select: {
            id: true,
            catalogCode: true,
            catalogName: true
          }
        },
        creator: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        documents: {
          include: {
            uploader: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          },
          orderBy: {
            uploadedAt: 'desc'
          }
        }
      }
    });

    if (!equipment) {
      return NextResponse.json(
        { error: 'Equipment not found' },
        { status: 404 }
      );
    }

    // Fetch Google Drive metadata for documents that have googleDriveId
    const isGoogleDriveConfigured = await googleDriveServiceNoLogin.isAuthenticated();
    console.log('Google Drive configured:', isGoogleDriveConfigured);
    
    if (isGoogleDriveConfigured && equipment.documents && equipment.documents.length > 0) {
      const documentsWithGoogleDrive = equipment.documents.filter(doc => doc.googleDriveId);
      console.log(`Found ${documentsWithGoogleDrive.length} documents with Google Drive IDs`);
      
      if (documentsWithGoogleDrive.length > 0) {
        try {
          // Fetch fresh metadata from Google Drive
          const googleDriveFiles = await Promise.all(
            documentsWithGoogleDrive.map(async (doc) => {
              try {
                const fileData = await googleDriveServiceNoLogin.getFile(doc.googleDriveId!);
                return {
                  id: doc.id,
                  googleDriveId: doc.googleDriveId,
                  fileName: fileData.name || doc.fileName,
                  mimeType: fileData.mimeType || doc.mimeType,
                  googleDriveUrl: fileData.webViewLink,
                  googleDriveEmbedUrl: fileData.embedLink,
                  googleDriveIconUrl: fileData.iconLink,
                  fileSize: fileData.size ? parseInt(fileData.size) : doc.fileSize,
                  modifiedTime: fileData.modifiedTime
                };
              } catch (error) {
                console.error(`Failed to fetch Google Drive file ${doc.googleDriveId}:`, error);
                // Return the original document data if Google Drive fetch fails
                return {
                  id: doc.id,
                  googleDriveId: doc.googleDriveId,
                  fileName: doc.fileName,
                  mimeType: doc.mimeType,
                  googleDriveUrl: doc.googleDriveUrl,
                  googleDriveEmbedUrl: doc.googleDriveEmbedUrl,
                  googleDriveIconUrl: doc.googleDriveIconUrl,
                  fileSize: doc.fileSize,
                  failed: true
                };
              }
            })
          );

          // Update documents with fresh Google Drive metadata
          const successfulFetches = googleDriveFiles.filter(f => f && !f.failed).length;
          console.log(`Successfully fetched metadata for ${successfulFetches} Google Drive files`);
          
          equipment.documents = equipment.documents.map(doc => {
            if (doc.googleDriveId) {
              const freshData = googleDriveFiles.find(gd => gd && gd.id === doc.id);
              if (freshData && !freshData.failed) {
                return {
                  ...doc,
                  fileName: freshData.fileName,
                  mimeType: freshData.mimeType,
                  googleDriveUrl: freshData.googleDriveUrl,
                  googleDriveEmbedUrl: freshData.googleDriveEmbedUrl,
                  googleDriveIconUrl: freshData.googleDriveIconUrl,
                  fileSize: freshData.fileSize
                };
              }
            }
            return doc;
          });
        } catch (error) {
          console.error('Error fetching Google Drive metadata:', error);
          // Continue with existing data if Google Drive fetch fails
        }
      }
    }

    return NextResponse.json(equipment);
  } catch (error) {
    console.error('Error fetching equipment:', error);
    return NextResponse.json(
      { error: 'Failed to fetch equipment' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await verifySession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Allow all authenticated users to update equipment (ADMIN and USER roles)

    const { id } = await params;
    const body = await request.json();

    // Check if equipment exists
    const existing = await prisma.equipment.findUnique({
      where: { id }
    });

    if (!existing) {
      return NextResponse.json(
        { error: 'Equipment not found' },
        { status: 404 }
      );
    }

    // Check if new equipment code is unique
    if (body.equipmentCode && body.equipmentCode !== existing.equipmentCode) {
      const duplicate = await prisma.equipment.findUnique({
        where: { equipmentCode: body.equipmentCode }
      });

      if (duplicate) {
        return NextResponse.json(
          { error: 'Equipment code already exists' },
          { status: 400 }
        );
      }
    }

    // Verify catalog exists if changing
    if (body.catalogId && body.catalogId !== existing.catalogId) {
      const catalog = await prisma.catalog.findUnique({
        where: { id: body.catalogId }
      });

      if (!catalog) {
        return NextResponse.json(
          { error: 'Catalog not found' },
          { status: 400 }
        );
      }
    }

    const equipment = await prisma.equipment.update({
      where: { id },
      data: body,
      include: {
        catalog: {
          select: {
            id: true,
            catalogCode: true,
            catalogName: true
          }
        },
        creator: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    // Log audit
    await prisma.auditLog.create({
      data: {
        userId: session.userId,
        action: 'UPDATE_EQUIPMENT',
        details: {
          equipmentId: equipment.id,
          equipmentCode: equipment.equipmentCode,
          changes: body
        }
      }
    });

    return NextResponse.json(equipment);
  } catch (error) {
    console.error('Error updating equipment:', error);
    return NextResponse.json(
      { error: 'Failed to update equipment' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await verifySession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Allow all authenticated users to delete equipment (ADMIN and USER roles)

    const { id } = await params;

    // Check if equipment exists
    const equipment = await prisma.equipment.findUnique({
      where: { id }
    });

    if (!equipment) {
      return NextResponse.json(
        { error: 'Equipment not found' },
        { status: 404 }
      );
    }

    // Delete equipment
    await prisma.equipment.delete({
      where: { id }
    });

    // Log audit
    await prisma.auditLog.create({
      data: {
        userId: session.userId,
        action: 'DELETE_EQUIPMENT',
        details: {
          equipmentId: equipment.id,
          equipmentCode: equipment.equipmentCode,
          name: equipment.name
        }
      }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting equipment:', error);
    return NextResponse.json(
      { error: 'Failed to delete equipment' },
      { status: 500 }
    );
  }
}