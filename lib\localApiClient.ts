import { tokenManager } from '@/lib/tokenManager'
import { authService } from '@/services/authService'

interface ApiClientConfig {
  timeout?: number
}

class LocalApiClient {
  private timeout: number

  constructor(config: ApiClientConfig = {}) {
    this.timeout = config.timeout || 30000
  }

  private async request<T>(
    method: string,
    endpoint: string,
    data?: any,
    options: RequestInit = {}
  ): Promise<T> {
    // Get token from memory
    let token = tokenManager.getAccessToken()
    
    // If no token or token is expired, try to refresh
    if (!token || tokenManager.isExpired()) {
      try {
        await authService.refreshToken()
        token = tokenManager.getAccessToken()
      } catch (error) {
        // If refresh fails, redirect to login
        if (typeof window !== 'undefined') {
          window.location.href = '/login'
        }
        throw new Error('Authentication required')
      }
    }
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    }

    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }

    // Merge with options.headers if exists
    if (options.headers) {
      Object.assign(headers, options.headers)
    }

    const config: RequestInit = {
      method,
      headers,
      signal: options.signal,
      cache: 'no-store', // Prevent caching to ensure fresh data
    }

    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      config.body = JSON.stringify(data)
    }

    // Use relative URL for local API routes
    const url = `/api${endpoint}`

    try {
      const response = await fetch(url, config)

      if (response.status === 401) {
        // Try to refresh token once
        try {
          await authService.refreshToken()
          
          // Retry the original request with new token
          const newToken = tokenManager.getAccessToken()
          if (newToken) {
            headers['Authorization'] = `Bearer ${newToken}`
            const retryResponse = await fetch(url, { ...config, headers })
            if (retryResponse.ok) {
              return await retryResponse.json()
            }
          }
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError)
        }
        
        // If refresh fails, clear token and redirect to login
        tokenManager.clear()
        if (typeof window !== 'undefined') {
          window.location.href = '/login'
        }
        throw new Error('Unauthorized')
      }

      if (!response.ok) {
        const error = await response.json().catch(() => ({ message: 'Request failed' }))
        const errorObj = new Error(error.message || error.error || `HTTP error! status: ${response.status}`)
        // Preserve the original error object for better error handling
        Object.assign(errorObj, error)
        throw errorObj
      }

      const result = await response.json()
      return result
    } catch (error: any) {
      // Don't log abort errors (these happen when components unmount)
      if (error?.name === 'AbortError') {
        throw error
      }
      console.error('API request failed:', error)
      throw error
    }
  }

  async get<T>(endpoint: string, options?: RequestInit): Promise<T> {
    return this.request<T>('GET', endpoint, undefined, options)
  }

  async post<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
    return this.request<T>('POST', endpoint, data, options)
  }

  async put<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
    return this.request<T>('PUT', endpoint, data, options)
  }

  async patch<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
    return this.request<T>('PATCH', endpoint, data, options)
  }

  async delete<T>(endpoint: string, options?: RequestInit): Promise<T> {
    return this.request<T>('DELETE', endpoint, undefined, options)
  }
}

export const localApiClient = new LocalApiClient()