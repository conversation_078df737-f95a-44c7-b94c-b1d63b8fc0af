'use client'

import React, { useMemo, useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  Home, 
  Briefcase, 
  FileText, 
  Users, 
  BarChart3, 
  Settings,
  LogOut,
  Menu,
  X,
  FolderTree,
  Package,
  FileArchive,
  FileCheck2,
} from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { SidebarSkeleton } from './SidebarSkeleton'
import { ProfileDialog } from '@/components/profile/ProfileDialog'

interface SidebarProps {
  isOpen: boolean
  onToggle: () => void
}

export function SimpleSidebar({ isOpen, onToggle }: SidebarProps) {
  const pathname = usePathname()
  const { user, logout, isLoading } = useAuth()
  const [profileDialogOpen, setProfileDialogOpen] = useState(false)

  const menuItems = [
    { 
      icon: Home, 
      label: 'Trang chủ', 
      href: '/dashboard',
      roles: ['ADMIN']
    },
    { 
      icon: FileCheck2, 
      label: '<PERSON><PERSON> sơ dự thầu', 
      href: '/bidding-documents',
      roles: ['ADMIN', 'USER']
    },
    { 
      icon: FolderTree, 
      label: '<PERSON>h mục', 
      href: '/catalogs',
      roles: ['ADMIN']
    },
    { 
      icon: Package, 
      label: 'Thiết bị', 
      href: '/equipment',
      roles: ['ADMIN','USER']
    },
    { 
      icon: FileArchive, 
      label: 'Tài liệu', 
      href: '/documents',
      roles: ['ADMIN']
    },
    // { 
    //   icon: BarChart3, 
    //   label: 'Báo cáo', 
    //   href: '/reports',
    //   roles: ['ADMIN']
    // },
    { 
      icon: Users, 
      label: 'Người dùng', 
      href: '/users',
      roles: ['ADMIN']
    },
    // { 
    //   icon: Settings, 
    //   label: 'Cài đặt', 
    //   href: '/settings',
    //   roles: ['ADMIN']
    // },
  ]

  const filteredMenuItems = useMemo(() => {
    if (!user) return []
    return menuItems.filter(item => item.roles.includes(user.role))
  }, [user?.role])

  // Show skeleton while loading
  if (isLoading || !user) {
    return <SidebarSkeleton />
  }

  return (
    <>
      {/* Mobile backdrop */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onToggle}
        />
      )}

      {/* Sidebar */}
      <aside
        className={`fixed top-0 left-0 z-50 h-full bg-white dark:bg-gray-800 shadow-lg transition-transform duration-300 ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        } lg:translate-x-0 lg:static lg:z-0 w-64`}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-2">
              <Briefcase className="w-8 h-8 text-blue-600" />
              <span className="text-xl font-bold text-gray-900 dark:text-gray-100">
                Smart Bidding
              </span>
            </div>
            <button
              onClick={onToggle}
              className="lg:hidden text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* User info */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-3">
              <button
                onClick={() => setProfileDialogOpen(true)}
                className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium hover:bg-blue-600 transition-colors cursor-pointer"
                title="Thông tin tài khoản"
              >
                {user.name?.charAt(0)?.toUpperCase() || user.username?.charAt(0)?.toUpperCase() || 'U'}
              </button>
              <div>
                <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {user.name || user.username || 'User'}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {user.role === 'ADMIN' ? 'Quản trị viên' : 'Người dùng'}
                </div>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 overflow-y-auto">
            <ul className="space-y-2">
              {filteredMenuItems.map((item) => {
                const Icon = item.icon
                // Check if current pathname starts with the menu item's href
                // This handles both exact matches and nested routes
                const isActive = pathname === item.href || pathname.startsWith(item.href + '/')
                
                return (
                  <li key={item.href}>
                    <Link
                      href={item.href}
                      onClick={() => {
                        // Close sidebar on mobile after navigation
                        if (window.innerWidth < 1024) {
                          onToggle()
                        }
                      }}
                      className={`flex items-center gap-3 px-3 py-2 rounded-md transition-colors ${
                        isActive
                          ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300'
                          : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
                      }`}
                    >
                      <Icon className="w-5 h-5" />
                      <span>{item.label}</span>
                    </Link>
                  </li>
                )
              })}
            </ul>
          </nav>

          {/* Logout */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={logout}
              className="flex items-center gap-3 w-full px-3 py-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-colors"
            >
              <LogOut className="w-5 h-5" />
              <span>Đăng xuất</span>
            </button>
          </div>
        </div>
      </aside>

      {/* Profile Dialog */}
      <ProfileDialog 
        isOpen={profileDialogOpen}
        onClose={() => setProfileDialogOpen(false)}
      />
    </>
  )
}