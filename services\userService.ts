import { BaseService } from './baseService'
import type { User } from '@/types/user'

export interface UserFilters {
  page?: number
  limit?: number
  search?: string
  role?: string
  status?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface CreateUserInput {
  username: string
  email: string
  password: string
  name: string
  role: 'ADMIN' | 'USER'
  department?: string
  phone?: string
  status?: 'ACTIVE' | 'INACTIVE'
}

export interface UpdateUserInput {
  email?: string
  name?: string
  role?: 'ADMIN' | 'USER'
  department?: string
  phone?: string
  status?: 'ACTIVE' | 'INACTIVE'
  password?: string
}

export interface UsersResponse {
  users: User[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export class UserService extends BaseService {
  private static instance: UserService

  private constructor() {
    super({ useLocalApi: true })
  }

  static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService()
    }
    return UserService.instance
  }

  async getUsers(filters: UserFilters = {}, signal?: AbortSignal): Promise<UsersResponse> {
    const queryParams = new URLSearchParams()
    
    if (filters.page) queryParams.append('page', filters.page.toString())
    if (filters.limit) queryParams.append('limit', filters.limit.toString())
    if (filters.search) queryParams.append('search', filters.search)
    if (filters.role) queryParams.append('role', filters.role)
    if (filters.status) queryParams.append('status', filters.status)
    if (filters.sortBy) queryParams.append('sortBy', filters.sortBy)
    if (filters.sortOrder) queryParams.append('sortOrder', filters.sortOrder)

    const endpoint = `/users?${queryParams.toString()}`
    return this.get<UsersResponse>(endpoint, signal)
  }

  async getUser(id: string, signal?: AbortSignal): Promise<User> {
    return this.get<User>(`/users/${id}`, signal)
  }

  async createUser(data: CreateUserInput, signal?: AbortSignal): Promise<User> {
    return this.post<User>('/users', data, { signal })
  }

  async updateUser(id: string, data: UpdateUserInput, signal?: AbortSignal): Promise<User> {
    return this.put<User>(`/users/${id}`, data, { signal })
  }

  async deleteUser(id: string, signal?: AbortSignal): Promise<{ message: string }> {
    return this.delete<{ message: string }>(`/users/${id}`, { signal })
  }

  async checkUsername(username: string, signal?: AbortSignal): Promise<{ available: boolean }> {
    const queryParams = new URLSearchParams({ username })
    return this.get<{ available: boolean }>(`/users/check-username?${queryParams.toString()}`, signal)
  }

  async checkEmail(email: string, signal?: AbortSignal): Promise<{ available: boolean }> {
    const queryParams = new URLSearchParams({ email })
    return this.get<{ available: boolean }>(`/users/check-email?${queryParams.toString()}`, signal)
  }

  async resetPassword(id: string, signal?: AbortSignal): Promise<{ message: string; user: User }> {
    return this.post<{ message: string; user: User }>(`/users/${id}/reset-password`, {}, { signal })
  }

  async toggleStatus(id: string, signal?: AbortSignal): Promise<{ message: string; user: User }> {
    return this.post<{ message: string; user: User }>(`/users/${id}/toggle-status`, {}, { signal })
  }

  async exportUsers(filters: UserFilters = {}, signal?: AbortSignal): Promise<Blob> {
    const queryParams = new URLSearchParams()
    
    if (filters.search) queryParams.append('search', filters.search)
    if (filters.role) queryParams.append('role', filters.role)
    if (filters.status) queryParams.append('status', filters.status)
    if (filters.sortBy) queryParams.append('sortBy', filters.sortBy)
    if (filters.sortOrder) queryParams.append('sortOrder', filters.sortOrder)

    const response = await fetch(`/api/users/export?${queryParams.toString()}`, {
      method: 'GET',
      credentials: 'include',
      signal
    })
    
    if (!response.ok) throw new Error('Export failed')
    return response.blob()
  }

  async importUsers(file: File, signal?: AbortSignal): Promise<{ imported: number; failed: number; errors?: string[] }> {
    const formData = new FormData()
    formData.append('file', file)
    
    const response = await fetch('/api/users/import', {
      method: 'POST',
      body: formData,
      credentials: 'include',
      signal
    })
    
    if (!response.ok) throw new Error('Import failed')
    return response.json()
  }
}

export const userService = UserService.getInstance()