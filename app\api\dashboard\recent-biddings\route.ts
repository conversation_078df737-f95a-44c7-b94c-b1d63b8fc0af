import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { verifyAccessToken } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Basic database connectivity check
    if (!prisma) {
      console.error('Database connection not available')
      return NextResponse.json(
        { error: 'Lỗi kết nối cơ sở dữ liệu', details: 'Database connection failed' },
        { status: 500 }
      )
    }
    // Get recent bidding documents with creator information
    const recentBiddingDocuments = await prisma.biddingDocument.findMany({
      take: 10, // Limit to 10 most recent
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        creator: {
          select: {
            name: true,
            username: true
          }
        },
        _count: {
          select: {
            attachments: true
          }
        }
      }
    })
    
    // Transform the data to match the frontend expectation
    const formattedBiddings = recentBiddingDocuments.map(bidding => ({
      id: bidding.id,
      code: bidding.code,
      name: bidding.name,
      title: bidding.name, // Use name as title for compatibility
      status: bidding.status,
      customerName: bidding.customerName || 'Chưa xác định',
      createdAt: bidding.createdAt.toISOString(),
      updatedAt: bidding.updatedAt.toISOString(),
      createdBy: bidding.creator?.name || 'Không xác định',
      attachmentCount: bidding._count.attachments,
      // Note: This system doesn't have value/price field, so we'll show attachment count instead
      value: `${bidding._count.attachments} tài liệu`,
      endDate: bidding.updatedAt.toISOString() // Use updatedAt as placeholder for endDate
    }))
    
    return NextResponse.json({ 
      success: true, 
      data: formattedBiddings 
    })
  } catch (error) {
    console.error('Recent biddings error:', error)
    
    // Provide more specific Vietnamese error messages
    let errorMessage = 'Không thể tải danh sách hồ sơ dự thầu'
    let errorDetails = 'Unknown error'
    
    if (error instanceof Error) {
      errorDetails = error.message
      if (error.message.includes('connect')) {
        errorMessage = 'Lỗi kết nối cơ sở dữ liệu'
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Hết thời gian chờ kết nối cơ sở dữ liệu'
      } else if (error.message.includes('authentication')) {
        errorMessage = 'Lỗi xác thực người dùng'
      } else if (error.message.includes('not found')) {
        errorMessage = 'Không tìm thấy dữ liệu hồ sơ dự thầu'
      }
    }
    
    return NextResponse.json(
      { 
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? errorDetails : 'Internal server error'
      },
      { status: 500 }
    )
  }
}