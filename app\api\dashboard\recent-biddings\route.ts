import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { verifyAccessToken } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Get recent bidding documents with creator information
    const recentBiddingDocuments = await prisma.biddingDocument.findMany({
      take: 10, // Limit to 10 most recent
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        creator: {
          select: {
            name: true,
            username: true
          }
        },
        _count: {
          select: {
            attachments: true
          }
        }
      }
    })
    
    // Transform the data to match the frontend expectation
    const formattedBiddings = recentBiddingDocuments.map(bidding => ({
      id: bidding.id,
      code: bidding.code,
      name: bidding.name,
      title: bidding.name, // Use name as title for compatibility
      status: bidding.status,
      customerName: bidding.customerName || 'Chưa xác định',
      createdAt: bidding.createdAt.toISOString(),
      updatedAt: bidding.updatedAt.toISOString(),
      createdBy: bidding.creator?.name || '<PERSON>hô<PERSON> x<PERSON><PERSON> định',
      attachmentCount: bidding._count.attachments,
      // Note: This system doesn't have value/price field, so we'll show attachment count instead
      value: `${bidding._count.attachments} tài liệu`,
      endDate: bidding.updatedAt.toISOString() // Use updatedAt as placeholder for endDate
    }))
    
    return NextResponse.json({ 
      success: true, 
      data: formattedBiddings 
    })
  } catch (error) {
    console.error('Recent biddings error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch recent bidding documents' },
      { status: 500 }
    )
  }
}