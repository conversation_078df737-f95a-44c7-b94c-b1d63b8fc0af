import { NextResponse } from 'next/server';
import { google } from 'googleapis';

export async function GET() {
  try {
    const client_id = process.env.GOOGLE_CLIENT_ID;
    const client_secret = process.env.GOOGLE_CLIENT_SECRET;
    const refresh_token = process.env.GOOGLE_REFRESH_TOKEN;

    if (!client_id || !client_secret || !refresh_token) {
      throw new Error('Missing Google OAuth2 credentials');
    }

    // Create OAuth2 client
    const oauth2Client = new google.auth.OAuth2(
      client_id,
      client_secret,
      process.env.GOOGLE_REDIRECT_URI
    );

    // Set refresh token
    oauth2Client.setCredentials({
      refresh_token: refresh_token
    });

    // Get new access token
    const { credentials } = await oauth2Client.refreshAccessToken();
    
    return NextResponse.json({
      accessToken: credentials.access_token,
      expiryDate: credentials.expiry_date
    });
  } catch (error) {
    console.error('Error getting client token:', error);
    return NextResponse.json(
      { error: 'Failed to get access token' },
      { status: 500 }
    );
  }
}