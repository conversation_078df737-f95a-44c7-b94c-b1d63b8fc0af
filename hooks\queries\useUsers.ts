import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { userService, type UserFilters } from '@/services/userService'
import type { CreateUserInput, UpdateUserInput } from '@/services/userService'
import { toast } from 'sonner'

export function useUsers(filters: UserFilters = {}) {
  return useQuery({
    queryKey: ['users', filters],
    queryFn: ({ signal }) => userService.getUsers(filters, signal),
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  })
}

export function useUser(id: string) {
  return useQuery({
    queryKey: ['users', id],
    queryFn: ({ signal }) => userService.getUser(id, signal),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  })
}

export function useCreateUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateUserInput) => userService.createUser(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast.success('User created successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create user')
    },
  })
}

export function useUpdateUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateUserInput }) =>
      userService.updateUser(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      queryClient.invalidateQueries({ queryKey: ['users', variables.id] })
      toast.success('User updated successfully')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update user')
    },
  })
}

export function useDeleteUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => userService.deleteUser(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast.success('User has been disabled')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to disable user')
    },
  })
}

export function useCheckUsername() {
  return useMutation({
    mutationFn: (username: string) => userService.checkUsername(username),
  })
}

export function useCheckEmail() {
  return useMutation({
    mutationFn: (email: string) => userService.checkEmail(email),
  })
}

export function useResetPassword() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => userService.resetPassword(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast.success('Password has been reset to: admin123')
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to reset password')
    },
  })
}

export function useToggleUserStatus() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => userService.toggleStatus(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast.success(data.message)
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to change status')
    },
  })
}

export function useExportUsers() {
  return useMutation({
    mutationFn: (filters: UserFilters = {}) => userService.exportUsers(filters),
    onError: (error: any) => {
      toast.error(error.message || 'Failed to export user list')
    },
  })
}

export function useImportUsers() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (file: File) => userService.importUsers(file),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      if (data.failed === 0) {
        toast.success(`Successfully imported ${data.imported} users`)
      } else {
        toast.warning(`Imported ${data.imported} users, failed ${data.failed} users`)
      }
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to import user list')
    },
  })
}