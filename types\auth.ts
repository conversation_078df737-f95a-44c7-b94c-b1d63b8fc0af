export interface User {
  id: string
  username: string
  email: string
  name: string
  role: UserRole
  department?: string
  phone?: string
  avatar?: string
  status: 'ACTIVE' | 'INACTIVE'
  createdAt: string
  updatedAt: string
}

export type UserRole = 'ADMIN' | 'USER'

export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  user: User
  accessToken: string
  expiresIn: number
}

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
}