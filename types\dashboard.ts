export interface DashboardStatItem {
  value: number
  change: string
  trend: 'up' | 'down' | 'stable'
}

export interface DashboardStats {
  totalBiddingDocuments: DashboardStatItem
  activeBiddingDocuments: DashboardStatItem
  totalEquipment: DashboardStatItem
  totalUsers: DashboardStatItem
  completedBiddingDocuments: number
  totalCatalogs: number
}

export interface BiddingTrend {
  month: string
  count: number
  value: number
}

export interface CategoryDistribution {
  category: string
  count: number
  percentage: number
}

export interface RecentActivity {
  id: number
  type: 'BIDDING_CREATED' | 'BIDDING_UPDATED' | 'PARTICIPANT_JOINED' | 'WINNER_SELECTED' | 'PROJECT_CREATED'
  title: string
  description: string
  timestamp: string
  userId: number
  userName: string
}