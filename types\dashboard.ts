export interface DashboardStats {
  totalBiddings: number
  activeBiddings: number
  completedBiddings: number
  totalValue: number
  totalProjects: number
  activeProjects: number
  totalContractors: number
  activeContractors: number
  successRate: number
  averageBiddingTime: number
}

export interface BiddingTrend {
  month: string
  count: number
  value: number
}

export interface CategoryDistribution {
  category: string
  count: number
}

export interface RecentActivity {
  id: number
  type: 'BIDDING_CREATED' | 'BIDDING_UPDATED' | 'PARTICIPANT_JOINED' | 'WINNER_SELECTED' | 'PROJECT_CREATED'
  title: string
  description: string
  timestamp: string
  userId: number
  userName: string
}