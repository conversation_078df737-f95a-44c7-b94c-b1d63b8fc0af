import React from 'react'

export function SidebarSkeleton() {
  return (
    <aside className="fixed top-0 left-0 z-50 h-full bg-white dark:bg-gray-800 shadow-lg lg:translate-x-0 lg:static lg:z-0 w-64">
      <div className="flex flex-col h-full">
        {/* Header skeleton */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
            <div className="w-32 h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
          </div>
        </div>

        {/* User info skeleton */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse" />
            <div>
              <div className="w-24 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2" />
              <div className="w-16 h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
            </div>
          </div>
        </div>

        {/* Navigation skeleton */}
        <nav className="flex-1 p-4 overflow-y-auto">
          <ul className="space-y-2">
            {[...Array(6)].map((_, index) => (
              <li key={index}>
                <div className="flex items-center gap-3 px-3 py-2 rounded-md">
                  <div className="w-5 h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                  <div className="w-20 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                </div>
              </li>
            ))}
          </ul>
        </nav>

        {/* Logout skeleton */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3 px-3 py-2">
            <div className="w-5 h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
            <div className="w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
          </div>
        </div>
      </div>
    </aside>
  )
}