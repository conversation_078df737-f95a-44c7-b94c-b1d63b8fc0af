import type { 
  Catalog, 
  CatalogFilters, 
  CatalogsResponse, 
  CreateCatalogRequest, 
  UpdateCatalogRequest,
  CheckCodeResponse,
  Equipment,
  EquipmentFilters,
  EquipmentsResponse,
  CreateEquipmentRequest,
  UpdateEquipmentRequest
} from '@/types/catalog'
import { BaseService } from './baseService'

export class CatalogService extends BaseService {
  private static instance: CatalogService

  private constructor() {
    super({ useLocalApi: true })
  }

  static getInstance(): CatalogService {
    if (!CatalogService.instance) {
      CatalogService.instance = new CatalogService()
    }
    return CatalogService.instance
  }

  // Catalog methods
  async getCatalogs(filters: CatalogFilters = {}, signal?: AbortSignal): Promise<CatalogsResponse> {
    const params = new URLSearchParams()
    
    if (filters.search) params.append('search', filters.search)
    if (filters.status) params.append('status', filters.status)
    if (filters.page) params.append('page', filters.page.toString())
    if (filters.limit) params.append('limit', filters.limit.toString())
    if (filters.sortBy) params.append('sortBy', filters.sortBy)
    if (filters.sortOrder) params.append('sortOrder', filters.sortOrder)

    const queryString = params.toString()
    const url = `/catalogs${queryString ? `?${queryString}` : ''}`
    
    return this.get<CatalogsResponse>(url, signal)
  }

  async getCatalogById(id: string, signal?: AbortSignal): Promise<Catalog> {
    return this.get<Catalog>(`/catalogs/${id}`, signal)
  }

  async createCatalog(data: CreateCatalogRequest): Promise<Catalog> {
    return this.post<Catalog>('/catalogs', data)
  }

  async updateCatalog(id: string, data: UpdateCatalogRequest): Promise<Catalog> {
    return this.put<Catalog>(`/catalogs/${id}`, data)
  }

  async deleteCatalog(id: string, force = false): Promise<void> {
    const params = force ? '?force=true' : ''
    return this.delete<void>(`/catalogs/${id}${params}`)
  }

  async checkCatalogCode(code: string): Promise<CheckCodeResponse> {
    return this.post<CheckCodeResponse>('/catalogs/check-code', { code })
  }

  async getCatalogEquipments(catalogId: string, signal?: AbortSignal): Promise<Equipment[]> {
    const catalog = await this.getCatalogById(catalogId, signal)
    return (catalog as any).equipments || []
  }

  // Equipment methods
  async getEquipments(filters: EquipmentFilters = {}, signal?: AbortSignal): Promise<EquipmentsResponse> {
    const params = new URLSearchParams()
    
    if (filters.search) params.append('search', filters.search)
    if (filters.catalogId) params.append('catalogId', filters.catalogId)
    if (filters.status) params.append('status', filters.status)
    if (filters.page) params.append('page', filters.page.toString())
    if (filters.limit) params.append('limit', filters.limit.toString())
    if (filters.sortBy) params.append('sortBy', filters.sortBy)
    if (filters.sortOrder) params.append('sortOrder', filters.sortOrder)

    const queryString = params.toString()
    const url = `/equipments${queryString ? `?${queryString}` : ''}`
    
    return this.get<EquipmentsResponse>(url, signal)
  }

  async getEquipmentById(id: string, signal?: AbortSignal): Promise<Equipment> {
    return this.get<Equipment>(`/equipments/${id}`, signal)
  }

  async createEquipment(data: CreateEquipmentRequest): Promise<Equipment> {
    return this.post<Equipment>('/equipments', data)
  }

  async updateEquipment(id: string, data: UpdateEquipmentRequest): Promise<Equipment> {
    return this.put<Equipment>(`/equipments/${id}`, data)
  }

  async deleteEquipment(id: string): Promise<void> {
    return this.delete<void>(`/equipments/${id}`)
  }

  async checkEquipmentCode(code: string): Promise<{ exists: boolean; equipment?: Equipment }> {
    return this.post<{ exists: boolean; equipment?: Equipment }>('/equipments/check-code', { code })
  }

  // Bulk operations
  async bulkDeleteCatalogs(ids: string[]): Promise<{ success: number; failed: number }> {
    return this.post<{ success: number; failed: number }>('/catalogs/bulk-delete', { ids })
  }

  async exportCatalogs(filters: CatalogFilters = {}): Promise<Blob> {
    const params = new URLSearchParams()
    
    if (filters.search) params.append('search', filters.search)
    if (filters.status) params.append('status', filters.status)

    const queryString = params.toString()
    const url = `/catalogs/export${queryString ? `?${queryString}` : ''}`
    
    const response = await fetch(`${this.baseUrl}${url}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    })

    if (!response.ok) {
      throw new Error('Failed to export catalogs')
    }

    return response.blob()
  }

  async importCatalogs(file: File): Promise<{ success: number; failed: number; errors?: string[] }> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await fetch(`${this.baseUrl}/catalogs/import`, {
      method: 'POST',
      body: formData,
      credentials: 'include',
    })

    if (!response.ok) {
      throw new Error('Failed to import catalogs')
    }

    return response.json()
  }
}

// Export singleton instance
export const catalogService = CatalogService.getInstance()