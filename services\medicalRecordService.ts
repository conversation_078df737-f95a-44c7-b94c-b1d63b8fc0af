import { BaseService } from './baseService'
import { tokenManager } from '@/lib/tokenManager'
import type { 
  MedicalRecord, 
  MedicalRecordAttachment,
  CreateMedicalRecordRequest,
  UpdateMedicalRecordRequest,
  MedicalRecordFilter,
  MedicalRecordListResponse 
} from '@/types/medicalRecord'

class MedicalRecordService extends BaseService {
  private static instance: MedicalRecordService

  private constructor() {
    super({ useLocalApi: true })
  }

  static getInstance(): MedicalRecordService {
    if (!MedicalRecordService.instance) {
      MedicalRecordService.instance = new MedicalRecordService()
    }
    return MedicalRecordService.instance
  }

  // Get medical records with filters
  async getMedicalRecords(filter?: MedicalRecordFilter): Promise<MedicalRecordListResponse> {
    const params = new URLSearchParams()
    
    if (filter?.search) params.append('search', filter.search)
    if (filter?.recordType?.length) params.append('recordType', filter.recordType.join(','))
    if (filter?.patientId) params.append('patientId', filter.patientId)
    if (filter?.doctorName) params.append('doctorName', filter.doctorName)
    if (filter?.department) params.append('department', filter.department)
    if (filter?.dateFrom) params.append('dateFrom', filter.dateFrom)
    if (filter?.dateTo) params.append('dateTo', filter.dateTo)
    if (filter?.page) params.append('page', filter.page.toString())
    if (filter?.limit) params.append('limit', filter.limit.toString())

    const queryString = params.toString()
    const url = `/medical-records${queryString ? `?${queryString}` : ''}`
    
    return this.get<MedicalRecordListResponse>(url)
  }

  // Get medical record by ID
  async getMedicalRecordById(id: string): Promise<MedicalRecord> {
    return this.get<MedicalRecord>(`/medical-records/${id}`)
  }

  // Create medical record
  async createMedicalRecord(data: CreateMedicalRecordRequest): Promise<MedicalRecord> {
    return this.post<MedicalRecord>('/medical-records', data)
  }

  // Update medical record
  async updateMedicalRecord(id: string, data: UpdateMedicalRecordRequest): Promise<MedicalRecord> {
    return this.put<MedicalRecord>(`/medical-records/${id}`, data)
  }

  // Delete medical record
  async deleteMedicalRecord(id: string): Promise<void> {
    return this.delete<void>(`/medical-records/${id}`)
  }

  // Upload PDF attachment
  async uploadAttachment(
    medicalRecordId: string,
    file: File
  ): Promise<MedicalRecordAttachment> {
    const formData = new FormData()
    formData.append('file', file)

    // Get the access token from token manager
    const token = tokenManager.getAccessToken()
    if (!token) {
      throw new Error('No authentication token available')
    }

    const response = await fetch(
      `/api/medical-records/${medicalRecordId}/attachments`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      }
    )

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.message || 'Failed to upload attachment')
    }

    return response.json()
  }

  // Delete attachment
  async deleteAttachment(medicalRecordId: string, attachmentId: string): Promise<void> {
    return this.delete<void>(`/medical-records/${medicalRecordId}/attachments/${attachmentId}`)
  }

  // Save multiple attachments
  async saveAttachments(
    medicalRecordId: string,
    attachments: MedicalRecordAttachment[]
  ): Promise<void> {
    return this.post<void>(`/medical-records/${medicalRecordId}/attachments/batch`, {
      attachments
    })
  }

  // Check if patient ID exists
  async checkPatientIdExists(patientId: string): Promise<boolean> {
    try {
      const response = await this.get<{ exists: boolean }>(
        `/medical-records/check-patient?patientId=${encodeURIComponent(patientId)}`
      )
      return response.exists
    } catch {
      return false
    }
  }
}

export const medicalRecordService = MedicalRecordService.getInstance()