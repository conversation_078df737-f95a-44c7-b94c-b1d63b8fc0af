'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useCreateMedicalRecord, useUpdateMedicalRecord, useDeleteMedicalAttachment, useSaveMedicalAttachments, useCheckPatientIdExists, useUploadMedicalAttachment } from '@/hooks/queries/useMedicalRecords'
import { useToast } from '@/hooks/useToast'
import { Button } from '@/components/ui/Button'
import { Select } from '@/components/ui/Select'
import { MedicalRecordUpload } from './MedicalRecordUpload'
import { MedicalRecordList } from './MedicalRecordList'
import { FileText, Upload, AlertCircle, Calendar, User, Stethoscope } from 'lucide-react'
import { MedicalRecordType } from '@/types/medicalRecord'
import type { MedicalRecord, CreateMedicalRecordRequest, UpdateMedicalRecordRequest, MedicalRecordAttachment } from '@/types/medicalRecord'

interface MedicalRecordFormProps {
  record?: MedicalRecord
  onSuccess?: () => void
}

export function MedicalRecordForm({ record, onSuccess }: MedicalRecordFormProps) {
  const router = useRouter()
  const toast = useToast()
  const isEdit = !!record

  const [formData, setFormData] = useState({
    patientName: record?.patientName || '',
    patientId: record?.patientId || '',
    recordType: record?.recordType || MedicalRecordType.EXAMINATION,
    description: record?.description || '',
    diagnosis: record?.diagnosis || '',
    treatment: record?.treatment || '',
    doctorName: record?.doctorName || '',
    department: record?.department || '',
    recordDate: record?.recordDate || new Date().toISOString().split('T')[0]
  })

  const [attachments, setAttachments] = useState<(MedicalRecordAttachment & { file?: File })[]>(record?.attachments || [])
  const [isCheckingPatientId, setIsCheckingPatientId] = useState(false)
  const [patientIdError, setPatientIdError] = useState('')

  const createMutation = useCreateMedicalRecord()
  const updateMutation = useUpdateMedicalRecord()
  const deleteAttachmentMutation = useDeleteMedicalAttachment()
  const saveAttachmentsMutation = useSaveMedicalAttachments()
  const checkPatientIdExistsMutation = useCheckPatientIdExists()
  const uploadAttachmentMutation = useUploadMedicalAttachment()

  useEffect(() => {
    if (record) {
      setFormData({
        patientName: record.patientName,
        patientId: record.patientId,
        recordType: record.recordType,
        description: record.description || '',
        diagnosis: record.diagnosis || '',
        treatment: record.treatment || '',
        doctorName: record.doctorName || '',
        department: record.department || '',
        recordDate: record.recordDate
      })
      setAttachments(record.attachments || [])
    }
  }, [record])

  const handlePatientIdChange = async (patientId: string) => {
    setFormData(prev => ({ ...prev, patientId }))
    setPatientIdError('')
    
    if (patientId && !isEdit) {
      setIsCheckingPatientId(true)
      try {
        const exists = await checkPatientIdExistsMutation.mutateAsync(patientId)
        if (exists) {
          setPatientIdError('Mã bệnh nhân đã tồn tại')
        }
      } catch (error) {
        console.error('Error checking patient ID:', error)
      } finally {
        setIsCheckingPatientId(false)
      }
    }
  }

  const handleUploadComplete = async (attachment: MedicalRecordAttachment & { file?: File }) => {
    try {
      const updatedAttachments = [...attachments, attachment]
      setAttachments(updatedAttachments)
    } catch (error) {
      console.error('Error handling upload:', error)
      toast.error('Lỗi khi xử lý file upload')
    }
  }

  const handleDeleteAttachment = async (attachmentId: string) => {
    try {
      // If it's an existing document (not temp), call API to delete
      if (!attachmentId.startsWith('temp-') && record?.id) {
        await deleteAttachmentMutation.mutateAsync({
          medicalRecordId: record.id,
          attachmentId
        })
      }
      
      // Update local state
      const updatedAttachments = attachments.filter(att => att.id !== attachmentId)
      setAttachments(updatedAttachments)
    } catch (error) {
      console.error('Error deleting attachment:', error)
      toast.error('Lỗi khi xóa tài liệu')
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.patientName || !formData.patientId) {
      toast.error('Vui lòng nhập đầy đủ thông tin bắt buộc')
      return
    }

    if (patientIdError) {
      toast.error('Vui lòng kiểm tra lại mã bệnh nhân')
      return
    }

    try {
      if (isEdit) {
        const updateData: UpdateMedicalRecordRequest = {
          patientName: formData.patientName,
          recordType: formData.recordType,
          description: formData.description,
          diagnosis: formData.diagnosis,
          treatment: formData.treatment,
          doctorName: formData.doctorName,
          department: formData.department,
          recordDate: formData.recordDate
        }
        
        await updateMutation.mutateAsync({
          id: record.id,
          data: updateData
        })
        
        // Save new attachments if any were added
        const newAttachments = attachments.filter(att => att.id.startsWith('temp-'))
        if (newAttachments.length > 0) {
          try {
            await saveAttachmentsMutation.mutateAsync({
              medicalRecordId: record.id,
              attachments: newAttachments
            })
            toast.success('Tài liệu mới đã được lưu thành công')
          } catch (error) {
            console.error('Error saving attachments:', error)
            toast.error('Lỗi khi lưu tài liệu. Vui lòng thử lại.')
          }
        }
        
        toast.success('Cập nhật hồ sơ y tế thành công')
      } else {
        const createData: CreateMedicalRecordRequest = {
          patientName: formData.patientName,
          patientId: formData.patientId,
          recordType: formData.recordType,
          description: formData.description,
          diagnosis: formData.diagnosis,
          treatment: formData.treatment,
          doctorName: formData.doctorName,
          department: formData.department,
          recordDate: formData.recordDate
        }

        const newRecord = await createMutation.mutateAsync(createData)
        
        // Upload PDF files if any were selected
        if (newRecord && attachments.length > 0) {
          try {
            // Upload PDF files that have the file object
            const pdfFilesToUpload = attachments.filter(att => 
              att.mimeType === 'application/pdf' && att.file && att.fileUrl.startsWith('blob:')
            )
            
            if (pdfFilesToUpload.length > 0) {
              for (const pdfAttachment of pdfFilesToUpload) {
                if (pdfAttachment.file) {
                  try {
                    await uploadAttachmentMutation.mutateAsync({
                      medicalRecordId: newRecord.id,
                      file: pdfAttachment.file
                    })
                  } catch (uploadError) {
                    console.error('Error uploading PDF:', uploadError)
                    toast.error(`Lỗi khi upload ${pdfAttachment.fileName}`)
                  }
                }
              }
            }
            
            toast.success('Tạo hồ sơ y tế và upload file thành công')
            router.push(`/medical-records/${newRecord.id}`)
            return
          } catch (error) {
            console.error('Error saving attachments:', error)
            toast.error('Lỗi khi lưu tài liệu. Vui lòng thử lại.')
          }
        } else {
          toast.success('Tạo hồ sơ y tế mới thành công')
        }
      }

      if (onSuccess) {
        onSuccess()
      } else {
        router.push('/medical-records')
      }
    } catch (error) {
      console.error('Failed to save medical record:', error)
    }
  }

  const recordTypeOptions = [
    { value: MedicalRecordType.EXAMINATION, label: 'Khám bệnh' },
    { value: MedicalRecordType.TREATMENT, label: 'Điều trị' },
    { value: MedicalRecordType.SURGERY, label: 'Phẫu thuật' },
    { value: MedicalRecordType.TEST_RESULT, label: 'Kết quả xét nghiệm' },
    { value: MedicalRecordType.PRESCRIPTION, label: 'Đơn thuốc' },
    { value: MedicalRecordType.OTHER, label: 'Khác' }
  ]

  return (
    <form className="space-y-8" onSubmit={handleSubmit}>
      {/* Basic Information Section */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-lg">
              <FileText className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Thông tin hồ sơ y tế</h2>
              <p className="text-sm text-gray-600">Thông tin bệnh nhân và chi tiết khám bệnh</p>
            </div>
          </div>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label className="flex items-center text-sm font-medium text-gray-700">
                <User className="w-4 h-4 text-gray-500 mr-1" />
                Tên bệnh nhân <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="text"
                value={formData.patientName}
                onChange={(e) => setFormData({ ...formData, patientName: e.target.value })}
                className="w-full h-11 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white"
                placeholder="Nhập tên bệnh nhân"
                required
              />
            </div>

            <div className="space-y-2">
              <label className="flex items-center text-sm font-medium text-gray-700">
                <AlertCircle className="w-4 h-4 text-red-500 mr-1" />
                Mã bệnh nhân <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="text"
                value={formData.patientId}
                onChange={(e) => handlePatientIdChange(e.target.value)}
                disabled={isEdit}
                className="w-full h-11 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white disabled:bg-gray-100 disabled:cursor-not-allowed"
                placeholder="Nhập mã bệnh nhân"
                required
              />
              {isCheckingPatientId && (
                <p className="mt-1 text-sm text-gray-500">Đang kiểm tra...</p>
              )}
              {patientIdError && (
                <p className="mt-1 text-sm text-red-600">{patientIdError}</p>
              )}
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Loại hồ sơ
              </label>
              <Select
                value={formData.recordType}
                onChange={(e) => setFormData({ ...formData, recordType: e.target.value as MedicalRecordType })}
                className="w-full h-11 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white"
              >
                {recordTypeOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </Select>
            </div>

            <div className="space-y-2">
              <label className="flex items-center text-sm font-medium text-gray-700">
                <Calendar className="w-4 h-4 text-gray-500 mr-1" />
                Ngày khám
              </label>
              <input
                type="date"
                value={formData.recordDate}
                onChange={(e) => setFormData({ ...formData, recordDate: e.target.value })}
                className="w-full h-11 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white"
              />
            </div>

            <div className="space-y-2">
              <label className="flex items-center text-sm font-medium text-gray-700">
                <Stethoscope className="w-4 h-4 text-gray-500 mr-1" />
                Bác sĩ khám
              </label>
              <input
                type="text"
                value={formData.doctorName}
                onChange={(e) => setFormData({ ...formData, doctorName: e.target.value })}
                className="w-full h-11 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white"
                placeholder="Nhập tên bác sĩ"
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Khoa/Phòng
              </label>
              <input
                type="text"
                value={formData.department}
                onChange={(e) => setFormData({ ...formData, department: e.target.value })}
                className="w-full h-11 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white"
                placeholder="Nhập tên khoa/phòng"
              />
            </div>

            <div className="md:col-span-2 space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Mô tả/Triệu chứng
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white resize-none"
                rows={3}
                placeholder="Mô tả triệu chứng, tình trạng bệnh..."
              />
            </div>

            <div className="md:col-span-2 space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Chẩn đoán
              </label>
              <textarea
                value={formData.diagnosis}
                onChange={(e) => setFormData({ ...formData, diagnosis: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white resize-none"
                rows={2}
                placeholder="Kết quả chẩn đoán..."
              />
            </div>

            <div className="md:col-span-2 space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Hướng điều trị
              </label>
              <textarea
                value={formData.treatment}
                onChange={(e) => setFormData({ ...formData, treatment: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white resize-none"
                rows={2}
                placeholder="Phương pháp điều trị, thuốc kê đơn..."
              />
            </div>
          </div>
        </div>
      </div>

      {/* PDF Attachments Section */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-8 h-8 bg-green-100 rounded-lg">
              <Upload className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">File PDF đính kèm</h2>
              <p className="text-sm text-gray-600">
                Upload các file PDF liên quan đến hồ sơ y tế
              </p>
            </div>
          </div>
        </div>
        
        <div className="p-6">
          <div className="space-y-6">
            <MedicalRecordUpload
              medicalRecordId={record?.id}
              onUploadComplete={handleUploadComplete}
              disabled={false}
            />
            
            <MedicalRecordList
              attachments={attachments}
              onDelete={handleDeleteAttachment}
              canDelete={true}
              canView={true}
            />
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
          <div className="text-sm text-gray-600">
            {isEdit ? 'Lưu thay đổi để cập nhật thông tin hồ sơ y tế' : 'Kiểm tra kỹ thông tin trước khi tạo hồ sơ mới'}
          </div>
          <div className="flex gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/medical-records')}
              className="px-6 py-3 border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Hủy bỏ
            </Button>
            <Button
              type="submit"
              disabled={createMutation.isPending || updateMutation.isPending || isCheckingPatientId}
              className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {createMutation.isPending || updateMutation.isPending
                ? 'Đang xử lý...'
                : isEdit
                ? 'Cập nhật hồ sơ'
                : 'Tạo hồ sơ mới'}
            </Button>
          </div>
        </div>
      </div>
    </form>
  )
}