'use client'

import { useState } from 'react'
import { Dialog } from '@/components/ui/Dialog'
import { Button } from '@/components/ui/Button'
import { useEquipments } from '@/hooks/queries/useEquipment'
import { useAddEquipmentItem } from '@/hooks/queries/useBiddingDocuments'
import { useToast } from '@/hooks/useToast'

interface AddEquipmentDialogProps {
  isOpen: boolean
  onClose: () => void
  biddingDocumentId: string
  existingEquipmentIds: string[]
}

export function AddEquipmentDialog({
  isOpen,
  onClose,
  biddingDocumentId,
  existingEquipmentIds
}: AddEquipmentDialogProps) {
  const toast = useToast()
  const [selectedEquipmentId, setSelectedEquipmentId] = useState('')
  const [pageRange, setPageRange] = useState({ from: '', to: '' })
  
  const { data: equipmentData } = useEquipments({ limit: 100 })
  const addMutation = useAddEquipmentItem()

  const availableEquipment = equipmentData?.data.filter(
    eq => !existingEquipmentIds.includes(eq.id)
  ) || []

  const equipmentOptions = availableEquipment.map(eq => ({
    value: eq.id,
    label: `${eq.equipmentCode} - ${eq.name}`
  }))

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!selectedEquipmentId) {
      toast.error('Vui lòng chọn thiết bị')
      return
    }

    try {
      await addMutation.mutateAsync({
        biddingDocumentId,
        equipmentId: selectedEquipmentId,
        pageRange: pageRange.from && pageRange.to ? {
          from: parseInt(pageRange.from),
          to: parseInt(pageRange.to)
        } : undefined
      })
      onClose()
    } catch (error) {
      // Error handled by mutation
    }
  }

  return (
    <Dialog
      isOpen={isOpen}
      onClose={onClose}
      title="Thêm thiết bị vào hồ sơ"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Chọn thiết bị <span className="text-red-500">*</span>
          </label>
          <select
            value={selectedEquipmentId}
            onChange={(e) => setSelectedEquipmentId(e.target.value)}
            className="w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
          >
            <option value="">Chọn thiết bị</option>
            {equipmentOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          {availableEquipment.length === 0 && (
            <p className="mt-1 text-sm text-gray-500">
              Tất cả thiết bị đã được thêm vào hồ sơ
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Phạm vi trang cần AI xử lý (tùy chọn)
          </label>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <input
                type="number"
                placeholder="Từ trang"
                value={pageRange.from}
                onChange={(e) => setPageRange(prev => ({ ...prev, from: e.target.value }))}
                className="w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
              />
            </div>
            <div>
              <input
                type="number"
                placeholder="Đến trang"
                value={pageRange.to}
                onChange={(e) => setPageRange(prev => ({ ...prev, to: e.target.value }))}
                className="w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
              />
            </div>
          </div>
          <p className="mt-1 text-sm text-gray-500">
            Để trống nếu muốn AI xử lý toàn bộ tài liệu
          </p>
        </div>

        <div className="flex justify-end gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
          >
            Hủy
          </Button>
          <Button
            type="submit"
            disabled={addMutation.isPending || availableEquipment.length === 0}
          >
            {addMutation.isPending ? 'Đang thêm...' : 'Thêm thiết bị'}
          </Button>
        </div>
      </form>
    </Dialog>
  )
}