import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from '@/lib/auth-server'
import { prisma } from '@/lib/db'
import { BiddingDocumentStatus } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    console.log('Dashboard stats API called')
    const session = await getServerSession(request)
    if (!session) {
      console.log('No session found, returning unauthorized')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    console.log('Session found, proceeding with data fetch')

    // Get current date for trend calculations
    const now = new Date()
    const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const previousMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
    const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1)

    // Get basic statistics (simplified version for testing)
    const [
      totalBiddingDocuments,
      activeBiddingDocuments,
      completedBiddingDocuments,
      totalEquipment,
      totalUsers,
      totalCatalogs,
    ] = await Promise.all([
      prisma.biddingDocument.count(),
      prisma.biddingDocument.count({
        where: { status: BiddingDocumentStatus.IN_PROGRESS }
      }),
      prisma.biddingDocument.count({
        where: { status: BiddingDocumentStatus.COMPLETED }
      }),
      prisma.equipment.count(),
      prisma.user.count(),
      prisma.catalog.count(),
    ])

    console.log('Database queries completed', { 
      totalBiddingDocuments, 
      totalUsers, 
      totalEquipment 
    })

    // Simple trend placeholders for testing
    const simpleTrend = { change: '+5.2%', trend: 'up' as const }

    // Calculate additional metrics
    const successRate = totalBiddingDocuments > 0 
      ? (completedBiddingDocuments / totalBiddingDocuments) * 100 
      : 0

    const response = {
      totalBiddingDocuments: {
        value: totalBiddingDocuments,
        change: simpleTrend.change,
        trend: simpleTrend.trend
      },
      activeBiddingDocuments: {
        value: activeBiddingDocuments,
        change: simpleTrend.change,
        trend: simpleTrend.trend
      },
      completedBiddingDocuments: {
        value: completedBiddingDocuments,
        percentage: successRate.toFixed(1)
      },
      totalEquipment: {
        value: totalEquipment,
        change: simpleTrend.change,
        trend: simpleTrend.trend
      },
      totalUsers: {
        value: totalUsers,
        change: simpleTrend.change,
        trend: simpleTrend.trend
      },
      totalCatalogs: {
        value: totalCatalogs
      },
      lastUpdated: new Date().toISOString()
    }

    console.log('Returning response:', response)
    return NextResponse.json(response)
  } catch (error) {
    console.error('Error fetching dashboard stats:', error)
    
    let errorMessage = 'Failed to fetch dashboard statistics'
    let errorDetails = 'Unknown error'
    
    if (error instanceof Error) {
      errorDetails = error.message
      if (error.message.includes('connect')) {
        errorMessage = 'Database connection error'
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Database query timeout'
      }
    }
    
    return NextResponse.json(
      { 
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? errorDetails : 'Internal server error'
      },
      { status: 500 }
    )
  }
}