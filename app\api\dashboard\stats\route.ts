import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { verifyAccessToken } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Get current date for calculations
    const now = new Date()
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())
    
    // Get total bidding documents count
    const totalBiddingDocuments = await prisma.biddingDocument.count()
    
    // Get active bidding documents (IN_PROGRESS)
    const activeBiddingDocuments = await prisma.biddingDocument.count({
      where: { status: 'IN_PROGRESS' }
    })
    
    // Get completed bidding documents
    const completedBiddingDocuments = await prisma.biddingDocument.count({
      where: { status: 'COMPLETED' }
    })
    
    // Get total equipment count
    const totalEquipment = await prisma.equipment.count({
      where: { status: 'ACTIVE' }
    })
    
    // Get total catalogs count
    const totalCatalogs = await prisma.catalog.count({
      where: { status: 'ACTIVE' }
    })
    
    // Get total users count
    const totalUsers = await prisma.user.count({
      where: { status: 'ACTIVE' }
    })
    
    // Calculate changes from last month
    const lastMonthBiddingDocuments = await prisma.biddingDocument.count({
      where: {
        createdAt: {
          gte: lastMonth,
          lte: now
        }
      }
    })
    
    const lastMonthActiveBidding = await prisma.biddingDocument.count({
      where: {
        status: 'IN_PROGRESS',
        updatedAt: {
          gte: lastMonth,
          lte: now
        }
      }
    })
    
    const lastMonthEquipment = await prisma.equipment.count({
      where: {
        status: 'ACTIVE',
        createdAt: {
          gte: lastMonth,
          lte: now
        }
      }
    })
    
    const lastMonthUsers = await prisma.user.count({
      where: {
        status: 'ACTIVE',
        createdAt: {
          gte: lastMonth,
          lte: now
        }
      }
    })
    
    // Calculate percentage changes (simplified)
    const calculateChange = (current: number, lastMonth: number) => {
      if (current === 0) return '0%'
      if (lastMonth === 0) return current > 0 ? '+100%' : '0%'
      const percentage = Math.round(((lastMonth / current) * 100))
      return percentage > 0 ? `+${percentage}%` : `${percentage}%`
    }
    
    const stats = {
      totalBiddingDocuments: {
        value: totalBiddingDocuments,
        change: calculateChange(totalBiddingDocuments, lastMonthBiddingDocuments),
        trend: lastMonthBiddingDocuments > 0 ? 'up' : 'stable'
      },
      activeBiddingDocuments: {
        value: activeBiddingDocuments,
        change: calculateChange(activeBiddingDocuments, lastMonthActiveBidding),
        trend: lastMonthActiveBidding > 0 ? 'up' : 'stable'
      },
      totalEquipment: {
        value: totalEquipment,
        change: calculateChange(totalEquipment, lastMonthEquipment),
        trend: lastMonthEquipment > 0 ? 'up' : 'stable'
      },
      totalUsers: {
        value: totalUsers,
        change: calculateChange(totalUsers, lastMonthUsers),
        trend: lastMonthUsers > 0 ? 'up' : 'stable'
      },
      completedBiddingDocuments,
      totalCatalogs
    }
    
    return NextResponse.json({ success: true, data: stats })
  } catch (error) {
    console.error('Dashboard stats error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch dashboard stats' },
      { status: 500 }
    )
  }
}