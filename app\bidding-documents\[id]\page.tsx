'use client';

import { useParams, useRouter } from 'next/navigation';
import { useBiddingDocument } from '@/hooks/queries/useBiddingDocuments';
import { useAuth } from '@/hooks/queries/useAuth';
import { Button } from '@/components/ui/Button';
import { MainLayout } from '@/components/layout/MainLayout';
import { BiddingDocumentDetail } from '@/components/bidding-documents/BiddingDocumentDetail';
import { ArrowLeft, Edit, FileText, Package, Users, BarChart } from 'lucide-react';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';

export default function BiddingDocumentDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const documentId = params.id as string;

  const { data: document, isLoading, error } = useBiddingDocument(documentId);

  const canEdit = user?.role === 'ADMIN' || user?.role === 'USER';

  if (isLoading) {
    return (
      <MainLayout>
        <div className="p-6">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error || !document) {
    return (
      <MainLayout>
        <div className="p-6">
          <div className="text-center text-red-600">
            Không tìm thấy hồ sơ dự thầu hoặc có lỗi xảy ra
          </div>
        </div>
      </MainLayout>
    );
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending' },
      IN_PROGRESS: { color: 'bg-blue-100 text-blue-800', label: 'In Progress' },
      COMPLETED: { color: 'bg-green-100 text-green-800', label: 'Completed' }
    };
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING;
    return (
      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${config.color}`}>
        {config.label}
      </span>
    );
  };

  return (
    <MainLayout>
      <div className="p-6">
        <div className="mb-6">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="outline"
              onClick={() => router.push('/bidding-documents')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Quay lại
            </Button>
            {canEdit && (
              <Button
                onClick={() => router.push(`/bidding-documents/${documentId}/edit`)}
                className="flex items-center gap-2"
              >
                <Edit className="w-4 h-4" />
                Chỉnh sửa
              </Button>
            )}
          </div>
          
          <h1 className="text-2xl font-bold text-gray-900">{document.name}</h1>
          <p className="text-gray-600 mt-1">Mã hồ sơ: {document.code}</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold mb-4">Thông tin cơ bản</h2>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500">Trạng thái</label>
                  <p className="mt-1">
                    {getStatusBadge(document.status)}
                  </p>
                </div>
                
                {document.customerName && (
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Khách hàng</label>
                    <p className="mt-1 text-gray-900">{document.customerName}</p>
                  </div>
                )}
                
                {document.description && (
                  <div className="col-span-2">
                    <label className="block text-sm font-medium text-gray-500">Mô tả</label>
                    <p className="mt-1 text-gray-900">{document.description}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Equipment Items */}
            {document.equipmentItems && document.equipmentItems.length > 0 && (
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-lg">
                    <Package className="w-5 h-5 text-blue-600" />
                  </div>
                  <h2 className="text-lg font-semibold">Thiết bị liên quan</h2>
                </div>
                
                <div className="space-y-3">
                  {document.equipmentItems.map((item) => (
                    <div key={item.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-medium text-gray-900">{item.equipment.name}</h3>
                          <p className="text-sm text-gray-500">Mã: {item.equipment.code}</p>
                          {item.pageRange && (
                            <p className="text-sm text-gray-500 mt-1">
                              Phạm vi trang: {item.pageRange.from} - {item.pageRange.to}
                            </p>
                          )}
                        </div>
                        {item.tenderDocuments && item.tenderDocuments.length > 0 && (
                          <span className="text-sm text-gray-500">
                            {item.tenderDocuments.length} tài liệu
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Technical Response Document */}
            {document.technicalResponseDocument && (
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="flex items-center justify-center w-8 h-8 bg-green-100 rounded-lg">
                    <FileText className="w-5 h-5 text-green-600" />
                  </div>
                  <h2 className="text-lg font-semibold">Tài liệu phản hồi kỹ thuật</h2>
                </div>
                
                <div className="border rounded-lg p-4">
                  <p className="font-medium">{document.technicalResponseDocument.fileName}</p>
                  <p className="text-sm text-gray-500">
                    Tạo lúc: {format(new Date(document.technicalResponseDocument.generatedAt), 'dd/MM/yyyy HH:mm', { locale: vi })}
                  </p>
                  {document.technicalResponseDocument.isEdited && (
                    <p className="text-sm text-gray-500">
                      Đã chỉnh sửa bởi {document.technicalResponseDocument.lastEditedBy?.name}
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Evaluation Result */}
            {document.evaluationResult && (
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="flex items-center justify-center w-8 h-8 bg-purple-100 rounded-lg">
                    <BarChart className="w-5 h-5 text-purple-600" />
                  </div>
                  <h2 className="text-lg font-semibold">Kết quả đánh giá</h2>
                </div>
                
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Tỷ lệ thành công</label>
                    <p className="mt-1 text-2xl font-bold text-gray-900">
                      {document.evaluationResult.successRate}%
                    </p>
                  </div>
                  
                  {document.evaluationResult.notes && (
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Ghi chú</label>
                      <p className="mt-1 text-gray-900">{document.evaluationResult.notes}</p>
                    </div>
                  )}
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Đánh giá bởi</label>
                    <p className="mt-1 text-gray-900">
                      {document.evaluationResult.evaluatedBy.name} - {format(new Date(document.evaluationResult.evaluatedAt), 'dd/MM/yyyy HH:mm', { locale: vi })}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Detailed Component */}
            <BiddingDocumentDetail document={document} />
          </div>

          {/* Sidebar Information */}
          <div className="space-y-6">
            {/* Metadata */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4">Thông tin hệ thống</h3>
              
              <div className="space-y-3 text-sm">
                {document.createdBy && (
                  <div>
                    <label className="block font-medium text-gray-500">Người tạo</label>
                    <p className="mt-1 text-gray-900">{document.createdBy.name}</p>
                  </div>
                )}
                
                <div>
                  <label className="block font-medium text-gray-500">Ngày tạo</label>
                  <p className="mt-1 text-gray-900">
                    {format(new Date(document.createdAt), 'dd/MM/yyyy HH:mm', { locale: vi })}
                  </p>
                </div>
                
                <div>
                  <label className="block font-medium text-gray-500">Cập nhật lần cuối</label>
                  <p className="mt-1 text-gray-900">
                    {format(new Date(document.updatedAt), 'dd/MM/yyyy HH:mm', { locale: vi })}
                  </p>
                </div>
              </div>
            </div>

            {/* Statistics */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4">Thống kê</h3>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">Số thiết bị</span>
                  <span className="font-medium">{document.equipmentItems?.length || 0}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">Số tài liệu đính kèm</span>
                  <span className="font-medium">{document.attachments?.length || 0}</span>
                </div>
                
                {document.technicalResponseDocument && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Tài liệu phản hồi</span>
                    <span className="font-medium text-green-600">Đã tạo</span>
                  </div>
                )}
                
                {document.evaluationResult && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Đánh giá</span>
                    <span className="font-medium text-purple-600">Đã hoàn thành</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}