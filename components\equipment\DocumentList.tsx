'use client';

import React, { useState } from 'react';
import { 
  FileText, 
  Download, 
  Trash2, 
  Eye, 
  Calendar, 
  User,
  FileSpreadsheet,
  FileType,
  Image,
  FileArchive,
  FileVideo,
  FileAudio,
  File
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Dialog } from '@/components/ui/Dialog';
import type { EquipmentDocument } from '@/types/equipment';

interface DocumentListProps {
  documents: EquipmentDocument[];
  onDelete?: (documentId: string) => void;
  onView?: (document: EquipmentDocument) => void;
  isLoading?: boolean;
  canDelete?: boolean;
  className?: string;
}

export function DocumentList({
  documents,
  onDelete,
  onView,
  isLoading = false,
  canDelete = true,
  className = ''
}: DocumentListProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<EquipmentDocument | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const getFileIcon = (mimeType: string) => {
    // PDF files
    if (mimeType.includes('pdf')) {
      return { Icon: FileText, color: 'text-red-500' };
    }
    
    // Word documents
    if (mimeType.includes('word') || mimeType.includes('document') || 
        mimeType.includes('msword') || mimeType.includes('vnd.openxmlformats-officedocument.wordprocessingml')) {
      return { Icon: FileType, color: 'text-blue-500' };
    }
    
    // Excel spreadsheets
    if (mimeType.includes('sheet') || mimeType.includes('excel') || 
        mimeType.includes('ms-excel') || mimeType.includes('vnd.openxmlformats-officedocument.spreadsheetml')) {
      return { Icon: FileSpreadsheet, color: 'text-green-500' };
    }
    
    // Images
    if (mimeType.startsWith('image/')) {
      return { Icon: Image, color: 'text-purple-500' };
    }
    
    // Videos
    if (mimeType.startsWith('video/')) {
      return { Icon: FileVideo, color: 'text-orange-500' };
    }
    
    // Audio
    if (mimeType.startsWith('audio/')) {
      return { Icon: FileAudio, color: 'text-pink-500' };
    }
    
    // Archives
    if (mimeType.includes('zip') || mimeType.includes('rar') || 
        mimeType.includes('tar') || mimeType.includes('7z') || 
        mimeType.includes('compressed')) {
      return { Icon: FileArchive, color: 'text-gray-600' };
    }
    
    // Text files
    if (mimeType.includes('text/') || mimeType.includes('plain')) {
      return { Icon: FileText, color: 'text-gray-700' };
    }
    
    // Default
    return { Icon: File, color: 'text-gray-400' };
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return Math.round(bytes / Math.pow(k, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleDownload = (doc: EquipmentDocument) => {
    // Create download link
    const link = document.createElement('a');
    link.href = doc.fileUrl;
    link.download = doc.fileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleDeleteClick = (doc: EquipmentDocument) => {
    setDocumentToDelete(doc);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!documentToDelete || !onDelete) return;
    
    setIsDeleting(true);
    try {
      await onDelete(documentToDelete.id);
      setDeleteDialogOpen(false);
      setDocumentToDelete(null);
    } catch (error) {
      console.error('Failed to delete document:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  if (isLoading) {
    return (
      <div className={`flex justify-center items-center h-32 ${className}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (documents.length === 0) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <FileText className="w-12 h-12 mx-auto text-gray-400 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">
          Chưa có tài liệu nào được tải lên
        </p>
      </div>
    );
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Tài liệu đính kèm ({documents.length})
        </h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {documents.map((doc) => {
          const { Icon, color } = getFileIcon(doc.mimeType || 'application/octet-stream');
          return (
            <div
              key={doc.id}
              className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3 flex-1">
                  <Icon className={`w-10 h-10 ${color} flex-shrink-0`} />
                  <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 truncate">
                    {doc.fileName}
                  </h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    {formatFileSize(doc.fileSize)}
                  </p>
                  <div className="flex items-center gap-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
                    <span className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      {formatDate(doc.uploadedAt)}
                    </span>
                    {doc.uploader && (
                      <span className="flex items-center gap-1">
                        <User className="w-3 h-3" />
                        {doc.uploader.name}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-1 ml-2">
                {onView && (
                  <button
                    onClick={() => onView(doc)}
                    className="p-2 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-md transition-colors"
                    title="Xem"
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                )}
                <button
                  onClick={() => handleDownload(doc)}
                  className="p-2 text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-md transition-colors"
                  title="Tải xuống"
                >
                  <Download className="w-4 h-4" />
                </button>
                {canDelete && onDelete && (
                  <button
                    onClick={() => handleDeleteClick(doc)}
                    className="p-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-colors"
                    title="Xóa"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>
          </div>
          );
        })}
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        title="Xác nhận xóa tài liệu"
        description={`Bạn có chắc chắn muốn xóa tài liệu "${documentToDelete?.fileName}"? Hành động này không thể hoàn tác.`}
      >
        <div className="flex justify-end gap-3 mt-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => setDeleteDialogOpen(false)}
            disabled={isDeleting}
          >
            Hủy
          </Button>
          <Button
            type="button"
            variant="primary"
            onClick={confirmDelete}
            disabled={isDeleting}
          >
            {isDeleting ? 'Đang xóa...' : 'Xóa'}
          </Button>
        </div>
      </Dialog>
    </div>
  );
}