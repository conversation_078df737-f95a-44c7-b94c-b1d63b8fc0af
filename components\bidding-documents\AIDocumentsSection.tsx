'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { BiddingDocumentList } from './BiddingDocumentList'
import { AIProfileCreationDialog } from './AIProfileCreationDialog'
import { useToast } from '@/hooks/useToast'
import { FileSpreadsheet, Plus, Sparkles } from 'lucide-react'
import type { BiddingDocument, BiddingDocumentAttachment } from '@/types/biddingDocument'

interface EquipmentItem {
  equipmentId: string
  pageRange?: {
    from: number
    to: number
  }
}

interface AIDocumentsSectionProps {
  document?: BiddingDocument
  attachments: BiddingDocumentAttachment[]
  equipmentItems: EquipmentItem[]
  onDeleteAttachment: (attachmentId: string) => void
  onAttachmentAdded?: (attachment: BiddingDocumentAttachment & { file?: File }) => void
  onEquipmentItemsChange?: (items: EquipmentItem[]) => void
  isEdit?: boolean
}

export function AIDocumentsSection({
  document,
  attachments,
  equipmentItems,
  onDeleteAttachment,
  onAttachmentAdded,
  onEquipmentItemsChange,
  isEdit = false
}: AIDocumentsSectionProps) {
  const [showAIDialog, setShowAIDialog] = useState(false)
  const toast = useToast()

  // Filter AI-generated Excel files
  const aiGeneratedDocuments = attachments.filter(att => 
    att.mimeType === 'application/vnd.google-apps.spreadsheet' || 
    att.mimeType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  )

  // Get PDF files for AI generation
  const pdfFiles = attachments.filter(att => att.mimeType === 'application/pdf')

  const handleAIProfileCreation = async (
    selectedFile: string, 
    selectedEquipmentItems: EquipmentItem[],
    aiGeneratedAttachment?: BiddingDocumentAttachment
  ) => {
    // Update equipment items
    if (onEquipmentItemsChange) {
      onEquipmentItemsChange(selectedEquipmentItems)
    }
    
    // If we have an AI-generated attachment, notify parent
    if (aiGeneratedAttachment && document?.id) {
      // Add the new attachment to the local state
      if (onAttachmentAdded) {
        onAttachmentAdded({
          ...aiGeneratedAttachment,
          id: `ai-${Date.now()}`, // Temporary ID
          uploadedAt: new Date().toISOString()
        })
      }
      
      toast.success('Hồ sơ AI đã được tạo và lưu vào Google Drive thành công!')
    }
    
    setShowAIDialog(false)
  }

  // Don't render if not in edit mode
  if (!isEdit) return null

  return (
    <>
      {/* AI Documents Section */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-8 h-8 bg-purple-100 rounded-lg">
                <FileSpreadsheet className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-gray-900">Hồ sơ AI</h2>
                <p className="text-sm text-gray-600">
                  Tạo và quản lý hồ sơ dự thầu được tạo bằng AI
                </p>
              </div>
            </div>
            
            {/* Create New Button */}
            <Button
              type="button"
              onClick={() => setShowAIDialog(true)}
              className="flex items-center gap-2 bg-purple-600 hover:bg-purple-700 text-white"
              disabled={!document?.id || pdfFiles.length === 0}
            >
              <Plus className="w-4 h-4" />
              Tạo mới
            </Button>
          </div>
        </div>
        
        <div className="p-6">
          {/* Show create prompt if no AI documents exist */}
          {aiGeneratedDocuments.length === 0 ? (
            <div className="text-center py-8">
              <div className="mx-auto w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                <Sparkles className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Chưa có hồ sơ AI nào
              </h3>
              <p className="text-sm text-gray-600 mb-6 max-w-md mx-auto">
                Sử dụng AI để tạo hồ sơ dự thầu Excel từ tệp tin PDF và danh sách thiết bị. 
                {pdfFiles.length === 0 && ' Vui lòng tải lên file PDF trước.'}
              </p>
              {document?.id && pdfFiles.length > 0 && (
                <Button
                  type="button"
                  size="lg"
                  onClick={() => setShowAIDialog(true)}
                  className="gap-2 bg-purple-600 hover:bg-purple-700 text-white"
                >
                  <Sparkles className="w-5 h-5" />
                  Tạo hồ sơ bằng AI
                </Button>
              )}
            </div>
          ) : (
            /* Show AI documents list */
            <div className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <div className="text-sm text-gray-600">
                  Tìm thấy <span className="font-semibold text-purple-600">{aiGeneratedDocuments.length}</span> hồ sơ AI
                </div>
              </div>
              
              <BiddingDocumentList
                attachments={aiGeneratedDocuments}
                onDelete={onDeleteAttachment}
                canDelete={true}
                canView={true}
              />
            </div>
          )}
        </div>
      </div>

      {/* AI Profile Creation Dialog */}
      <AIProfileCreationDialog
        isOpen={showAIDialog}
        onClose={() => setShowAIDialog(false)}
        onConfirm={handleAIProfileCreation}
        existingFiles={pdfFiles.map(att => ({
          id: att.id,
          name: att.fileName,
          url: att.fileUrl
        }))}
        existingEquipmentItems={equipmentItems}
        biddingDocumentId={document?.id}
      />
    </>
  )
}