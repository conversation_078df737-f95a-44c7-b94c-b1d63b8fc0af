'use client'

import React, { useState, useEffect } from 'react'
import { Search, Filter, X } from 'lucide-react'
import { Select } from '@/components/ui/Select'
import { useDebounce } from '@/hooks/useDebounce'
import type { UserFilters as UserFiltersType } from '@/services/userService'

interface UserFiltersProps {
  filters: UserFiltersType
  onFilterChange: (filters: UserFiltersType) => void
}

const roleOptions = [
  { value: '', label: 'Tất cả vai trò' },
  { value: 'ADMIN', label: 'Quản trị viên' },
  { value: 'USER', label: 'Người dùng' },
]

const statusOptions = [
  { value: '', label: 'Tất cả trạng thái' },
  { value: 'ACTIVE', label: 'Hoạt động' },
  { value: 'INACTIVE', label: 'Không hoạt động' },
]

const sortOptions = [
  { value: 'createdAt', label: '<PERSON><PERSON><PERSON> tạo' },
  { value: 'name', label: 'Tên' },
  { value: 'username', label: 'Tên người dùng' },
  { value: 'email', label: 'Email' },
  { value: 'lastLoginAt', label: 'Đăng nhập cuối' },
]

export function UserFilters({ filters, onFilterChange }: UserFiltersProps) {
  const [localSearch, setLocalSearch] = useState(filters.search || '')
  const debouncedSearch = useDebounce(localSearch, 500)

  useEffect(() => {
    if (debouncedSearch !== filters.search) {
      onFilterChange({ ...filters, search: debouncedSearch, page: 1 })
    }
  }, [debouncedSearch])

  const handleRoleChange = (role: string) => {
    onFilterChange({ ...filters, role: role || undefined, page: 1 })
  }

  const handleStatusChange = (status: string) => {
    onFilterChange({ ...filters, status: status || undefined, page: 1 })
  }

  const handleSortChange = (sortBy: string) => {
    onFilterChange({ ...filters, sortBy, page: 1 })
  }

  const handleSortOrderChange = () => {
    onFilterChange({
      ...filters,
      sortOrder: filters.sortOrder === 'asc' ? 'desc' : 'asc',
      page: 1,
    })
  }

  const handleClearFilters = () => {
    setLocalSearch('')
    onFilterChange({
      page: 1,
      limit: filters.limit || 10,
      sortBy: 'createdAt',
      sortOrder: 'desc',
    })
  }

  const hasActiveFilters = localSearch || filters.role || filters.status

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        {/* Search */}
        <div className="lg:col-span-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              value={localSearch}
              onChange={(e) => setLocalSearch(e.target.value.trim())}
              placeholder="Tìm kiếm theo tên, email, username..."
              className="w-full h-10 pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
            />
          </div>
        </div>

        {/* Role Filter */}
        <div>
          <Select
            value={filters.role || ''}
            onChange={(e) => handleRoleChange(e.target.value)}
            className="w-full"
          >
            {roleOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </Select>
        </div>

        {/* Status Filter */}
        <div>
          <Select
            value={filters.status || ''}
            onChange={(e) => handleStatusChange(e.target.value)}
            className="w-full"
          >
            {statusOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </Select>
        </div>

        {/* Sort */}
        <div className="flex space-x-2">
          <Select
            value={filters.sortBy || 'createdAt'}
            onChange={(e) => handleSortChange(e.target.value)}
            className="flex-1"
          >
            {sortOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </Select>
          <button
            onClick={handleSortOrderChange}
            className="px-3 h-10 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 dark:text-gray-100"
            title={filters.sortOrder === 'asc' ? 'Sắp xếp tăng dần' : 'Sắp xếp giảm dần'}
          >
            {filters.sortOrder === 'asc' ? '↑' : '↓'}
          </button>
        </div>
      </div>

      {/* Active filters indicator */}
      {hasActiveFilters && (
        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
            <Filter className="h-4 w-4 mr-2" />
            <span>Đang lọc kết quả</span>
          </div>
          <button
            onClick={handleClearFilters}
            className="flex items-center text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
          >
            <X className="h-4 w-4 mr-1" />
            Xóa bộ lọc
          </button>
        </div>
      )}
    </div>
  )
}