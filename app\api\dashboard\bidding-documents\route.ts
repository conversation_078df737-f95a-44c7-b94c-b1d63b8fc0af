import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from '@/lib/auth-server'
import { prisma } from '@/lib/db'
import { BiddingDocumentStatus } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status') as BiddingDocumentStatus | null
    const customerName = searchParams.get('customerName') || ''
    const search = searchParams.get('search') || ''
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'
    const urgent = searchParams.get('urgent') === 'true' // Show only urgent documents
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')

    const skip = (page - 1) * limit

    // Build where conditions
    const whereConditions: any = {}
    const andConditions: any[] = []

    // Search in code, name, customer name
    if (search) {
      andConditions.push({
        OR: [
          { code: { contains: search, mode: 'insensitive' } },
          { name: { contains: search, mode: 'insensitive' } },
          { customerName: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ]
      })
    }

    // Filter by status
    if (status) {
      andConditions.push({ status })
    }

    // Filter by customer name
    if (customerName) {
      andConditions.push({ 
        customerName: { contains: customerName, mode: 'insensitive' } 
      })
    }

    // Date range filter
    if (dateFrom || dateTo) {
      const dateFilter: any = {}
      if (dateFrom) {
        dateFilter.gte = new Date(dateFrom)
      }
      if (dateTo) {
        const endDate = new Date(dateTo)
        endDate.setHours(23, 59, 59, 999)
        dateFilter.lte = endDate
      }
      andConditions.push({ createdAt: dateFilter })
    }

    // Urgent documents (created in last 7 days or updated recently)
    if (urgent) {
      const urgentDate = new Date()
      urgentDate.setDate(urgentDate.getDate() - 7)
      andConditions.push({
        OR: [
          { createdAt: { gte: urgentDate } },
          { updatedAt: { gte: urgentDate } },
          { status: BiddingDocumentStatus.IN_PROGRESS }
        ]
      })
    }

    if (andConditions.length > 0) {
      whereConditions.AND = andConditions
    }

    // Validate sort field
    const validSortFields = ['createdAt', 'updatedAt', 'code', 'name', 'status']
    const orderBy = validSortFields.includes(sortBy) ? sortBy : 'createdAt'
    const orderDirection = sortOrder === 'asc' ? 'asc' : 'desc'

    // Get documents with related data
    const [documents, total] = await Promise.all([
      prisma.biddingDocument.findMany({
        where: whereConditions,
        skip,
        take: limit,
        orderBy: { [orderBy]: orderDirection },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              username: true,
              email: true
            }
          },
          _count: {
            select: {
              attachments: true,
              equipmentItems: true
            }
          }
        }
      }),
      prisma.biddingDocument.count({ where: whereConditions })
    ])

    // Calculate progress and priority for each document
    const documentsWithMetadata = documents.map(doc => {
      // Calculate progress based on status and attachments
      let progress = 0
      switch (doc.status) {
        case BiddingDocumentStatus.PENDING:
          progress = 25
          break
        case BiddingDocumentStatus.IN_PROGRESS:
          progress = doc._count.attachments > 0 ? 75 : 50
          break
        case BiddingDocumentStatus.COMPLETED:
          progress = 100
          break
      }

      // Determine priority based on creation date and status
      const daysSinceCreated = Math.floor((Date.now() - new Date(doc.createdAt).getTime()) / (1000 * 60 * 60 * 24))
      let priority: 'low' | 'medium' | 'high' = 'medium'
      
      if (doc.status === BiddingDocumentStatus.IN_PROGRESS && daysSinceCreated > 14) {
        priority = 'high'
      } else if (daysSinceCreated < 3) {
        priority = 'high'
      } else if (daysSinceCreated > 30) {
        priority = 'low'
      }

      // Calculate estimated completion time
      const avgCompletionDays = 21 // Average days to complete a bidding document
      const estimatedCompletion = new Date(doc.createdAt)
      estimatedCompletion.setDate(estimatedCompletion.getDate() + avgCompletionDays)

      return {
        id: doc.id,
        code: doc.code,
        name: doc.name,
        description: doc.description,
        status: doc.status,
        customerName: doc.customerName,
        createdAt: doc.createdAt.toISOString(),
        updatedAt: doc.updatedAt.toISOString(),
        creator: doc.creator,
        attachmentCount: doc._count.attachments,
        equipmentItemsCount: doc._count.equipmentItems,
        progress,
        priority,
        daysSinceCreated,
        estimatedCompletion: estimatedCompletion.toISOString(),
        isOverdue: doc.status !== BiddingDocumentStatus.COMPLETED && daysSinceCreated > avgCompletionDays,
        tags: [
          doc.status === BiddingDocumentStatus.COMPLETED ? 'Hoàn thành' : null,
          doc._count.attachments === 0 ? 'Thiếu tài liệu' : null,
          priority === 'high' ? 'Ưu tiên cao' : null,
          daysSinceCreated > avgCompletionDays ? 'Quá hạn' : null
        ].filter(Boolean)
      }
    })

    // Get summary statistics for the filtered results
    const statusSummary = await prisma.biddingDocument.groupBy({
      by: ['status'],
      where: whereConditions,
      _count: {
        status: true
      }
    })

    const summary = {
      total,
      byStatus: statusSummary.reduce((acc, item) => {
        acc[item.status] = item._count.status
        return acc
      }, {} as Record<string, number>),
      urgent: documentsWithMetadata.filter(doc => doc.priority === 'high').length,
      overdue: documentsWithMetadata.filter(doc => doc.isOverdue).length,
      averageProgress: documentsWithMetadata.length > 0 
        ? documentsWithMetadata.reduce((sum, doc) => sum + doc.progress, 0) / documentsWithMetadata.length 
        : 0
    }

    return NextResponse.json({
      documents: documentsWithMetadata,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      },
      summary,
      filters: {
        status,
        customerName,
        search,
        urgent,
        dateFrom,
        dateTo,
        sortBy: orderBy,
        sortOrder: orderDirection
      }
    })
  } catch (error) {
    console.error('Error fetching dashboard bidding documents:', error)
    
    let errorMessage = 'Failed to fetch bidding documents overview'
    let errorDetails = 'Unknown error'
    
    if (error instanceof Error) {
      errorDetails = error.message
      if (error.message.includes('connect')) {
        errorMessage = 'Database connection error'
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Database query timeout'
      }
    }
    
    return NextResponse.json(
      { 
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? errorDetails : 'Internal server error'
      },
      { status: 500 }
    )
  }
}