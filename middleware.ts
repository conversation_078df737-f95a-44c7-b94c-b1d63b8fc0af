import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { verifyAccessToken } from '@/lib/auth'

// Define public routes that don't require authentication
const publicRoutes = [
  '/login',
  '/',
  '/api/auth/login',
  '/api/auth/refresh',
  '/api/auth/google',
  '/api/auth/google/callback',
]

// Define protected page routes that require authentication
const protectedPageRoutes = [
  '/dashboard',
  '/bidding-documents',
  '/catalogs',
  '/documents',
  '/equipment',
  '/users',
]

// Define API routes that require authentication
const protectedAPIRoutes = [
  '/api/auth/session',
  '/api/auth/logout',
  '/api/bidding-documents',
  '/api/catalogs',
  '/api/documents',
  '/api/equipment',
  '/api/users',
  '/api/google-drive',
]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Check if the route is public
  const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route))
  
  if (isPublicRoute) {
    // If user is already authenticated and tries to access login, redirect to dashboard
    if (pathname === '/login') {
      const sessionId = request.cookies.get('session-id')?.value
      const refreshToken = request.cookies.get('refresh-token')?.value
      if (sessionId && refreshToken) {
        return NextResponse.redirect(new URL('/dashboard', request.url))
      }
    }
    return NextResponse.next()
  }

  // Check for session and refresh token in cookies
  const sessionId = request.cookies.get('session-id')?.value
  const refreshToken = request.cookies.get('refresh-token')?.value

  if (!sessionId || !refreshToken) {
    // For API routes, return 401
    if (pathname.startsWith('/api/')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }
    // For page routes, redirect to login
    return NextResponse.redirect(new URL('/login', request.url))
  }

  // For API routes that require auth, check the Authorization header
  if (pathname.startsWith('/api/') && protectedAPIRoutes.some(route => pathname.startsWith(route))) {
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')
    
    if (!token) {
      return NextResponse.json(
        { error: 'Authorization header required' },
        { status: 401 }
      )
    }
    
    // Verify the access token
    const payload = await verifyAccessToken(token)
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  }

  // Add security headers
  const response = NextResponse.next()
  
  // Security headers
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  
  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (public directory)
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}