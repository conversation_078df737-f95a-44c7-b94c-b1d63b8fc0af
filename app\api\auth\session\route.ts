import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { prisma } from '@/lib/db'
import { verifyAccessToken } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Get token from Authorization header instead of cookie
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '')
    
    const cookieStore = await cookies()
    const sessionId = cookieStore.get('session-id')?.value

    if (!token || !sessionId) {
      return NextResponse.json(
        { error: 'No authentication token found' },
        { status: 401 }
      )
    }

    // Verify the access token
    const payload = await verifyAccessToken(token)
    
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Verify session exists and is active
    const session = await prisma.session.findFirst({
      where: { 
        id: sessionId,
        status: 'ACTIVE',
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            email: true,
            name: true,
            role: true,
            department: true,
            phone: true,
            avatar: true,
            status: true,
            emailVerified: true,
            twoFactorEnabled: true,
            createdAt: true,
            updatedAt: true,
          },
        },
      },
    })

    if (!session) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 401 }
      )
    }

    // Check if session is expired
    if (session.expiresAt < new Date()) {
      // Mark session as expired
      await prisma.session.update({
        where: { id: sessionId },
        data: {
          status: 'EXPIRED',
          updatedAt: new Date(),
        },
      })

      return NextResponse.json(
        { error: 'Session expired' },
        { status: 401 }
      )
    }

    // Return user data
    return NextResponse.json({
      user: session.user,
      session: {
        id: session.id,
        expiresAt: session.expiresAt,
        createdAt: session.createdAt,
      },
    })
  } catch (error) {
    console.error('Session verification error:', error)
    return NextResponse.json(
      { error: 'Failed to verify session' },
      { status: 500 }
    )
  }
}