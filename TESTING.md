# Testing Guide - Hướng dẫn Testing

## Tổng quan

Dự án này sử dụng:
- **Jest** + **React Testing Library**: Unit test và component test
- **Playwright**: E2E test cho giao diện
- **MSW (Mock Service Worker)**: Mock API cho testing

## Cấu trúc thư mục test

```
__tests__/
├── unit/                    # Unit tests
│   ├── components/         # Component tests
│   ├── services/          # Service tests
│   └── hooks/             # Hook tests
├── utils/                  # Test utilities
└── mocks/                  # API mocks với MSW

e2e/                        # E2E tests với Playwright
├── auth.spec.ts           # Test login/logout
├── dashboard.spec.ts      # Test dashboard
└── ...
```

## Chạy Tests

### Unit Tests (Jest)

```bash
# Chạy tất cả unit tests
npm run test:unit

# Chạy với coverage report
npm run test:unit:coverage

# Chạy ở watch mode (tự động chạy lại khi code thay đổi)
npm run test:unit:watch

# Chạy test một file cụ thể
npx jest __tests__/unit/components/ui/Button.test.tsx
```

### E2E Tests (Playwright)

```bash
# Cài đặt browsers (chỉ cần chạy 1 lần)
npx playwright install

# Chạy tất cả E2E tests
npm run test:e2e

# Chạy với UI mode (xem test chạy trực quan)
npm run test:e2e:ui

# Chạy với browser hiển thị
npm run test:e2e:headed

# Chạy một test file cụ thể
npx playwright test e2e/auth.spec.ts
```

### Chạy tất cả tests

```bash
npm test
```

## Viết Tests

### 1. Unit Test cho Component

```typescript
// __tests__/unit/components/MyComponent.test.tsx
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { MyComponent } from '@/components/MyComponent'

describe('MyComponent', () => {
  it('renders correctly', () => {
    render(<MyComponent title="Test" />)
    expect(screen.getByText('Test')).toBeInTheDocument()
  })

  it('handles click events', async () => {
    const handleClick = jest.fn()
    render(<MyComponent onClick={handleClick} />)
    
    await userEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })
})
```

### 2. Unit Test cho Service

```typescript
// __tests__/unit/services/myService.test.ts
import { myService } from '@/services/myService'

describe('MyService', () => {
  beforeEach(() => {
    global.fetch = jest.fn()
  })

  it('fetches data successfully', async () => {
    const mockData = { id: 1, name: 'Test' }
    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockData,
    })

    const result = await myService.getData()
    expect(result).toEqual(mockData)
  })
})
```

### 3. E2E Test với Playwright

```typescript
// e2e/feature.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Feature Tests', () => {
  test('user can complete workflow', async ({ page }) => {
    // Navigate to page
    await page.goto('/feature')
    
    // Fill form
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.click('button[type="submit"]')
    
    // Verify result
    await expect(page.locator('.success-message')).toBeVisible()
  })
})
```

## Coverage Report

Sau khi chạy tests với coverage:

```bash
npm run test:unit:coverage
```

Coverage report sẽ được tạo tại:
- Terminal: Hiển thị tóm tắt
- `coverage/lcov-report/index.html`: Report HTML chi tiết

## CI/CD với GitLab

File `.gitlab-ci.yml` đã được cấu hình để:

1. **Stage: install** - Cài đặt dependencies
2. **Stage: lint** - Kiểm tra code style
3. **Stage: test** - Chạy unit tests và E2E tests
4. **Stage: build** - Build ứng dụng
5. **Stage: deploy** - Deploy lên môi trường

### Xem test results trên GitLab

- Test coverage được hiển thị trong merge requests
- Test reports có thể xem trong tab "Tests" của pipeline
- Screenshots và videos của failed E2E tests được lưu như artifacts

## Best Practices

### 1. Đặt tên test rõ ràng

```typescript
// ❌ Không tốt
it('works', () => {})

// ✅ Tốt
it('displays error message when email is invalid', () => {})
```

### 2. Sử dụng data-testid cho E2E

```tsx
// Component
<button data-testid="submit-button">Submit</button>

// Test
await page.click('[data-testid="submit-button"]')
```

### 3. Mock external dependencies

```typescript
// Mock API calls
jest.mock('@/services/api')

// Mock Next.js router
jest.mock('next/navigation')
```

### 4. Test các cases quan trọng

- Happy path (trường hợp thành công)
- Error handling
- Edge cases
- Loading states
- Empty states

### 5. Giữ tests độc lập

Mỗi test nên:
- Setup riêng data cần thiết
- Không phụ thuộc vào test khác
- Clean up sau khi chạy xong

## Troubleshooting

### Jest không tìm thấy tests

```bash
# Kiểm tra pattern matching
npx jest --listTests
```

### Playwright timeout

```typescript
// Tăng timeout cho test cụ thể
test('slow test', async ({ page }) => {
  test.setTimeout(60000) // 60 seconds
  // ...
})
```

### MSW không mock được API

```typescript
// Kiểm tra handlers đã đúng path chưa
console.log('Request URL:', request.url)
```

## Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Playwright Documentation](https://playwright.dev/docs/intro)
- [MSW Documentation](https://mswjs.io/docs/)

## Support

Nếu gặp vấn đề với testing, vui lòng:
1. Kiểm tra lại các bước setup
2. Xem log files để debug
3. Tạo issue trong GitLab với tag "testing"