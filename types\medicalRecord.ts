export interface MedicalRecord {
  id: string
  patientName: string
  patientId: string
  recordType: MedicalRecordType
  description?: string
  diagnosis?: string
  treatment?: string
  doctorName?: string
  department?: string
  recordDate: string
  createdAt: string
  updatedAt: string
  createdBy: {
    id: string
    name: string
    username: string
  }
  attachments?: MedicalRecordAttachment[]
}

export interface MedicalRecordAttachment {
  id: string
  fileName: string
  fileUrl: string
  fileSize: number
  mimeType: string
  uploadedAt: string
  source: 'local' | 'google_drive'
  googleDriveId?: string
  googleDriveUrl?: string
}

export enum MedicalRecordType {
  EXAMINATION = 'EXAMINATION', // Khám bệnh
  TREATMENT = 'TREATMENT', // Điều trị
  SURGERY = 'SURGERY', // Phẫu thuật
  TEST_RESULT = 'TEST_RESULT', // Kết quả xét nghiệm
  PRESCRIPTION = 'PRESCRIPTION', // Đơn thuốc
  OTHER = 'OTHER' // Khác
}

export interface CreateMedicalRecordRequest {
  patientName: string
  patientId: string
  recordType: MedicalRecordType
  description?: string
  diagnosis?: string
  treatment?: string
  doctorName?: string
  department?: string
  recordDate: string
}

export interface UpdateMedicalRecordRequest {
  patientName?: string
  recordType?: MedicalRecordType
  description?: string
  diagnosis?: string
  treatment?: string
  doctorName?: string
  department?: string
  recordDate?: string
}

export interface MedicalRecordFilter {
  search?: string
  recordType?: MedicalRecordType[]
  patientId?: string
  doctorName?: string
  department?: string
  dateFrom?: string
  dateTo?: string
  page?: number
  limit?: number
}

export interface MedicalRecordListResponse {
  items: MedicalRecord[]
  total: number
  page: number
  limit: number
}