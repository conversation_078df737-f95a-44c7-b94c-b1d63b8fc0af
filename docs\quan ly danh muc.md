# Chức năng Quản lý danh mục

## <PERSON><PERSON> tả
Chức năng cho phép người dùng có quyền (**Admin**) thực hiện các thao tác: t<PERSON><PERSON> mớ<PERSON>, chỉnh sửa, x<PERSON><PERSON> danh mục thiết bị.  
Danh mục là cấp phân loại cha để quản lý các thiết bị/model, gi<PERSON><PERSON> hệ thống tổ chức thông tin một cách logic và dễ tìm kiếm.

## Tác nhân
- Quản trị viên (**Admin**)

## Điều kiện trước
- Người dùng đã đăng nhập hệ thống với quyền **Admin**  
- Hệ thống đang hoạt động bình thường  
- Kết nối cơ sở dữ liệu ổn định  

## Điều kiện sau
- <PERSON><PERSON> mục được tạo mới / cập nhật / xóa khỏi hệ thống thành công  
- <PERSON><PERSON><PERSON> diện danh sách cập nhật tức thì  

## Ngo<PERSON><PERSON> lệ
- Mã danh mục bị trùng  
- Lỗi không kết nối tới cơ sở dữ liệu  
- Người dùng không có quyền thực hiện  

## Các yêu cầu đặc biệt
- Tên danh mục không được trùng với các danh mục đang hoạt động khác  
- Mã danh mục chỉ chứa ký tự không dấu và viết liền không cách  


# Biểu đồ luồng xử lý chức năng

## 1. Tạo mới
1. Người dùng nhập thông tin danh mục  
2. Hệ thống kiểm tra tính hợp lệ của dữ liệu  
3. Hệ thống kiểm tra trùng mã danh mục  
4. Nếu hợp lệ và không trùng mã → Lưu vào cơ sở dữ liệu  
5. Hiển thị thông báo thành công  

## 2. Chỉnh sửa
1. Người dùng chọn danh mục cần chỉnh sửa  
2. Người dùng sửa thông tin danh mục  
3. Hệ thống kiểm tra tính hợp lệ của dữ liệu  
4. Cập nhật thông tin vào cơ sở dữ liệu  
5. Hiển thị kết quả cập nhật  

## 3. Xóa
1. Người dùng nhấn **Xóa** danh mục  
2. Hệ thống kiểm tra xem danh mục có đang được sử dụng không  
   - Nếu **không liên kết thiết bị** → Cho phép xóa  
   - Nếu **có thiết bị liên kết** → Hiển thị cảnh báo hoặc gán thiết bị về danh mục rỗng  
3. Cập nhật danh sách danh mục
