import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from '@/lib/auth-server';
import { prisma } from '@/lib/db';

// POST: Create multiple documents for equipment
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: equipmentId } = await params;
    
    // Get user session
    const session = await getServerSession(request);
    if (!session?.userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if equipment exists
    const equipment = await prisma.equipment.findUnique({
      where: { id: equipmentId }
    });

    if (!equipment) {
      return NextResponse.json(
        { error: 'Equipment not found' },
        { status: 404 }
      );
    }

    // Parse request body
    const { documents } = await request.json();

    if (!Array.isArray(documents) || documents.length === 0) {
      return NextResponse.json(
        { error: 'No documents provided' },
        { status: 400 }
      );
    }

    // Create documents in database
    const createdDocuments = await Promise.all(
      documents.map(async (doc) => {
        return await prisma.equipmentDocument.create({
          data: {
            equipmentId,
            fileName: doc.fileName,
            fileUrl: doc.fileUrl,
            fileSize: doc.fileSize || 0,
            mimeType: doc.mimeType || 'application/octet-stream',
            uploadedBy: session.userId,
            googleDriveId: doc.googleDriveId || null,
            googleDriveUrl: doc.googleDriveUrl || null,
            googleDriveEmbedUrl: doc.googleDriveEmbedUrl || null,
            googleDriveIconUrl: doc.googleDriveIconUrl || null
          },
          include: {
            uploader: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        });
      })
    );

    return NextResponse.json(createdDocuments);
  } catch (error) {
    console.error('Error creating documents:', error);
    return NextResponse.json(
      { error: 'Failed to create documents' },
      { status: 500 }
    );
  }
}