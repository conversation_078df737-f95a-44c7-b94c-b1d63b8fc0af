'use client'

import React, { useState } from 'react'
import { Lock, Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { useChangePassword } from '@/hooks/queries/useProfile'
import type { ChangePasswordInput } from '@/services/profileService'

export function ChangePasswordTab() {
  const changePasswordMutation = useChangePassword()
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [successMessage, setSuccessMessage] = useState('')
  const [formData, setFormData] = useState<ChangePasswordInput>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  })

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.currentPassword.trim()) {
      newErrors.currentPassword = 'Mật khẩu hiện tại là bắt buộc'
    }

    const trimmedNewPassword = formData.newPassword.trim()
    if (!trimmedNewPassword) {
      newErrors.newPassword = 'Mật khẩu mới là bắt buộc'
    } else if (trimmedNewPassword.length < 6) {
      newErrors.newPassword = 'Mật khẩu mới phải có ít nhất 6 ký tự'
    }

    if (!formData.confirmPassword.trim()) {
      newErrors.confirmPassword = 'Xác nhận mật khẩu là bắt buộc'
    } else if (formData.newPassword.trim() !== formData.confirmPassword.trim()) {
      newErrors.confirmPassword = 'Mật khẩu không khớp'
    }

    if (formData.currentPassword.trim() && trimmedNewPassword && 
        formData.currentPassword.trim() === trimmedNewPassword) {
      newErrors.newPassword = 'Mật khẩu mới phải khác mật khẩu hiện tại'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSuccessMessage('')

    if (!validateForm()) {
      return
    }

    try {
      const response = await changePasswordMutation.mutateAsync({
        currentPassword: formData.currentPassword.trim(),
        newPassword: formData.newPassword.trim(),
        confirmPassword: formData.confirmPassword.trim()
      })
      setSuccessMessage(response.message || 'Đổi mật khẩu thành công')
      setFormData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      })
      setErrors({})
    } catch (error) {
      if (error instanceof Error && 'response' in error) {
        const err = error as { response?: { data?: { error?: string } } }
        if (err.response?.data?.error) {
          setErrors({ submit: err.response.data.error })
        }
      }
    }
  }

  const handleInputChange = (field: keyof ChangePasswordInput, value: string) => {
    setFormData({ ...formData, [field]: value })
    // Clear error for the field being edited
    if (errors[field]) {
      setErrors({ ...errors, [field]: '' })
    }
    // Clear submit error when user starts typing
    if (errors.submit) {
      setErrors({ ...errors, submit: '' })
    }
    // Clear success message when user starts typing
    if (successMessage) {
      setSuccessMessage('')
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Success Message */}
      {successMessage && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-3 flex items-start">
          <CheckCircle className="w-5 h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
          <p className="text-sm text-green-700 dark:text-green-300">{successMessage}</p>
        </div>
      )}

      {/* Submit Error */}
      {errors.submit && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3 flex items-start">
          <AlertCircle className="w-5 h-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
          <p className="text-sm text-red-700 dark:text-red-300">{errors.submit}</p>
        </div>
      )}

      {/* Current Password */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          <Lock className="inline w-4 h-4 mr-1" />
          Mật khẩu hiện tại *
        </label>
        <div className="relative">
          <input
            type={showCurrentPassword ? 'text' : 'password'}
            value={formData.currentPassword}
            onChange={(e) => handleInputChange('currentPassword', e.target.value)}
            className={`w-full h-10 px-3 py-2 pr-10 border rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 ${
              errors.currentPassword ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
            }`}
            placeholder="Nhập mật khẩu hiện tại"
          />
          <button
            type="button"
            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
            className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
          >
            {showCurrentPassword ? (
              <EyeOff className="w-5 h-5" />
            ) : (
              <Eye className="w-5 h-5" />
            )}
          </button>
        </div>
        {errors.currentPassword && (
          <p className="mt-1 text-sm text-red-500 flex items-center">
            <AlertCircle className="w-4 h-4 mr-1" />
            {errors.currentPassword}
          </p>
        )}
      </div>

      {/* New Password */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          <Lock className="inline w-4 h-4 mr-1" />
          Mật khẩu mới *
        </label>
        <div className="relative">
          <input
            type={showNewPassword ? 'text' : 'password'}
            value={formData.newPassword}
            onChange={(e) => handleInputChange('newPassword', e.target.value)}
            className={`w-full h-10 px-3 py-2 pr-10 border rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 ${
              errors.newPassword ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
            }`}
            placeholder="Nhập mật khẩu mới (ít nhất 6 ký tự)"
          />
          <button
            type="button"
            onClick={() => setShowNewPassword(!showNewPassword)}
            className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
          >
            {showNewPassword ? (
              <EyeOff className="w-5 h-5" />
            ) : (
              <Eye className="w-5 h-5" />
            )}
          </button>
        </div>
        {errors.newPassword && (
          <p className="mt-1 text-sm text-red-500 flex items-center">
            <AlertCircle className="w-4 h-4 mr-1" />
            {errors.newPassword}
          </p>
        )}
      </div>

      {/* Confirm New Password */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          <Lock className="inline w-4 h-4 mr-1" />
          Xác nhận mật khẩu mới *
        </label>
        <div className="relative">
          <input
            type={showConfirmPassword ? 'text' : 'password'}
            value={formData.confirmPassword}
            onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
            className={`w-full h-10 px-3 py-2 pr-10 border rounded-md focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 ${
              errors.confirmPassword ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
            }`}
            placeholder="Nhập lại mật khẩu mới"
          />
          <button
            type="button"
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
          >
            {showConfirmPassword ? (
              <EyeOff className="w-5 h-5" />
            ) : (
              <Eye className="w-5 h-5" />
            )}
          </button>
        </div>
        {errors.confirmPassword && (
          <p className="mt-1 text-sm text-red-500 flex items-center">
            <AlertCircle className="w-4 h-4 mr-1" />
            {errors.confirmPassword}
          </p>
        )}
      </div>

      {/* Password Requirements */}
      <div className="bg-gray-50 dark:bg-gray-800 rounded-md p-3">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          <strong>Password Requirements:</strong>
        </p>
        <ul className="text-sm text-gray-600 dark:text-gray-400 mt-1 space-y-1">
          <li className="flex items-center">
            <span className={`w-2 h-2 rounded-full mr-2 ${
              formData.newPassword.length >= 6 ? 'bg-green-500' : 'bg-gray-300'
            }`}></span>
            At least 6 characters
          </li>
          <li className="flex items-center">
            <span className={`w-2 h-2 rounded-full mr-2 ${
              formData.newPassword && formData.currentPassword && 
              formData.newPassword !== formData.currentPassword ? 'bg-green-500' : 'bg-gray-300'
            }`}></span>
            Different from current password
          </li>
          <li className="flex items-center">
            <span className={`w-2 h-2 rounded-full mr-2 ${
              formData.newPassword && formData.confirmPassword && 
              formData.newPassword === formData.confirmPassword ? 'bg-green-500' : 'bg-gray-300'
            }`}></span>
            Confirmation matches new password
          </li>
        </ul>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end pt-4">
        <Button
          type="submit"
          variant="primary"
          disabled={changePasswordMutation.isPending}
        >
          {changePasswordMutation.isPending ? 'Processing...' : 'Change Password'}
        </Button>
      </div>
    </form>
  )
}