/**
 * Token storage utilities for managing authentication tokens in localStorage
 * Provides secure storage with expiration handling
 */

interface StoredTokenData {
  accessToken: string
  refreshToken: string
  expiresAt: number // Unix timestamp in milliseconds
  sessionId?: string
}

const TOKEN_STORAGE_KEY = 'auth_tokens'

/**
 * Save tokens to localStorage with expiration
 */
export const saveTokens = (
  accessToken: string,
  refreshToken: string,
  expiresIn: string | number,
  sessionId?: string
): void => {
  if (typeof window === 'undefined') return

  try {
    // Calculate expiration time
    let expirationMs: number
    
    if (typeof expiresIn === 'string') {
      // Parse duration string like "15m", "7d", etc.
      expirationMs = parseDuration(expiresIn)
    } else {
      // Assume it's seconds if number
      expirationMs = expiresIn * 1000
    }

    const expiresAt = Date.now() + expirationMs

    const tokenData: StoredTokenData = {
      accessToken,
      refreshToken,
      expiresAt,
      sessionId
    }

    localStorage.setItem(TOKEN_STORAGE_KEY, JSON.stringify(tokenData))
  } catch (error) {
    console.error('Failed to save tokens:', error)
  }
}

/**
 * Get stored tokens from localStorage
 * Returns null if tokens don't exist or are expired
 */
export const getStoredTokens = (): StoredTokenData | null => {
  if (typeof window === 'undefined') return null

  try {
    const stored = localStorage.getItem(TOKEN_STORAGE_KEY)
    if (!stored) return null

    const tokenData: StoredTokenData = JSON.parse(stored)

    // Check if tokens are expired
    if (isTokenExpired(tokenData.expiresAt)) {
      clearStoredTokens()
      return null
    }

    return tokenData
  } catch (error) {
    console.error('Failed to get stored tokens:', error)
    clearStoredTokens()
    return null
  }
}

/**
 * Clear stored tokens from localStorage
 */
export const clearStoredTokens = (): void => {
  if (typeof window === 'undefined') return

  try {
    localStorage.removeItem(TOKEN_STORAGE_KEY)
  } catch (error) {
    console.error('Failed to clear stored tokens:', error)
  }
}

/**
 * Check if token is expired
 */
export const isTokenExpired = (expiresAt: number): boolean => {
  // Add 30 second buffer to account for clock skew
  return Date.now() >= (expiresAt - 30000)
}

/**
 * Parse duration string to milliseconds
 * Supports formats like "15m", "1h", "7d"
 */
const parseDuration = (duration: string): number => {
  const match = duration.match(/^(\d+)([mhd])$/)
  if (!match) return 15 * 60 * 1000 // Default 15 minutes

  const [, value, unit] = match
  const num = parseInt(value)

  switch (unit) {
    case 'm': return num * 60 * 1000
    case 'h': return num * 60 * 60 * 1000
    case 'd': return num * 24 * 60 * 60 * 1000
    default: return 15 * 60 * 1000
  }
}

/**
 * Update only the access token (used after refresh)
 */
export const updateAccessToken = (accessToken: string, expiresIn: string | number): void => {
  const current = getStoredTokens()
  if (!current) return

  saveTokens(
    accessToken,
    current.refreshToken,
    expiresIn,
    current.sessionId
  )
}

/**
 * Get remaining time until token expires (in minutes)
 */
export const getTokenExpiryMinutes = (): number | null => {
  const tokens = getStoredTokens()
  if (!tokens) return null

  const remainingMs = tokens.expiresAt - Date.now()
  return Math.max(0, Math.floor(remainingMs / (60 * 1000)))
}

/**
 * Check if token needs refresh (less than 5 minutes remaining)
 */
export const shouldRefreshToken = (): boolean => {
  const remainingMinutes = getTokenExpiryMinutes()
  if (remainingMinutes === null) return false
  
  return remainingMinutes <= 5
}