export interface BiddingDocument {
  id: string
  code: string // <PERSON><PERSON> hồ sơ
  name: string // Tê<PERSON> hồ sơ
  description?: string // Mô tả
  status: BiddingDocumentStatus
  customerName?: string // Tên khách hàng
  createdBy: {
    id: string
    name: string
    username: string
  }
  createdAt: string
  updatedAt: string
  equipmentItems: BiddingEquipmentItem[]
  technicalResponseDocument?: TechnicalResponseDocument
  evaluationResult?: EvaluationResult
  attachments?: BiddingDocumentAttachment[]
}

export interface BiddingDocumentAttachment {
  id: string
  fileName: string
  fileUrl: string
  fileSize: number
  mimeType: string
  uploadedAt: string
  source: 'local' | 'google_drive'
  googleDriveId?: string
  googleDriveUrl?: string
  _viewMode?: 'view' | 'edit';
}

export interface BiddingEquipmentItem {
  id: string
  equipmentId: string
  equipment: {
    id: string
    code: string
    name: string
    specifications?: Record<string, any>
  }
  tenderDocuments: TenderDocument[] // Tài liệu yêu cầu mời thầu
  pageRange?: {
    from: number
    to: number
  } // Phạm vi trang cần AI xử lý
}

export interface TenderDocument {
  id: string
  fileName: string
  fileUrl: string
  fileSize: number
  mimeType: string
  uploadedAt: string
  pageRange?: {
    from: number
    to: number
  }
}

export interface TechnicalResponseDocument {
  id: string
  fileName: string
  fileUrl: string
  fileSize: number
  mimeType: string
  generatedAt: string
  aiProcessedData?: Record<string, any>
  isEdited: boolean
  lastEditedAt?: string
  lastEditedBy?: {
    id: string
    name: string
  }
}

export interface EvaluationResult {
  id: string
  successRate: number // Tỉ lệ phần trăm AI xử lý thành công
  notes?: string // Ghi chú
  evaluatedAt: string
  evaluatedBy: {
    id: string
    name: string
  }
}

export enum BiddingDocumentStatus {
  PENDING = 'PENDING', // Pending
  IN_PROGRESS = 'IN_PROGRESS', // In Progress
  COMPLETED = 'COMPLETED' // Completed
}

// Request/Response types
export interface CreateBiddingDocumentRequest {
  code: string
  name: string
  description?: string
  customerName?: string
  status?: BiddingDocumentStatus
  equipmentItems?: {
    equipmentId: string
    pageRange?: {
      from: number
      to: number
    }
  }[]
}

export interface UpdateBiddingDocumentRequest {
  name?: string
  description?: string
  customerName?: string
  status?: BiddingDocumentStatus
  equipmentItems?: {
    equipmentId: string
    pageRange?: {
      from: number
      to: number
    }
  }[]
}

export interface UploadTenderDocumentRequest {
  biddingDocumentId: string
  equipmentItemId: string
  file: File
  pageRange?: {
    from: number
    to: number
  }
}

export interface GenerateTechnicalResponseRequest {
  biddingDocumentId: string
  format: 'WORD' | 'PDF' | 'EXCEL'
}

export interface UpdateEvaluationRequest {
  biddingDocumentId: string
  notes: string
}

export interface BiddingDocumentFilter {
  search?: string
  status?: BiddingDocumentStatus[]
  customerName?: string
  createdFrom?: string
  createdTo?: string
  page?: number
  limit?: number
}

export interface BiddingDocumentListResponse {
  items: BiddingDocument[]
  total: number
  page: number
  limit: number
}