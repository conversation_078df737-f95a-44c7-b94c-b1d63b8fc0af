{"permissions": {"allow": ["Bash(npm install:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(npm run build:*)", "Bash(rm:*)", "Bash(move \"src\\app\\[locale]\\page.tsx\" \"src\\app\\page.tsx\")", "Bash(npm run lint)", "Bash(npx:*)", "WebFetch(domain:tailwindcss.com)", "Bash(grep:*)", "<PERSON><PERSON>(curl:*)", "Bash(ls:*)", "<PERSON><PERSON>(sed:*)", "Bash(npm install:*)", "Bash(npx prisma:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(rm:*)", "Bash(npm run:*)", "<PERSON><PERSON>(npx jest:*)", "<PERSON><PERSON>(npx playwright install:*)", "<PERSON><PERSON>(taskkill:*)", "<PERSON><PERSON>(dir publicuploads)", "<PERSON><PERSON>(cat:*)", "Bash(find:*)", "Bash(pg_isready:*)", "Bash(psql:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(node:*)", "Bash(kill:*)"], "deny": []}}