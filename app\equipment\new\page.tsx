'use client';

import { Metadata } from 'next';
import { useRouter } from 'next/navigation';
import { EquipmentForm } from '@/components/equipment/EquipmentForm';
import { MainLayout } from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/Button';
import { ArrowLeft, Plus, ChevronRight, List } from 'lucide-react';

export default function NewEquipmentPage() {
  const router = useRouter();

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50">
        {/* Enhanced Header */}
        <div className="bg-white border-b border-gray-200 shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="py-6">
              {/* Breadcrumb */}
              <nav className="flex items-center space-x-2 text-sm mb-4">
                <button
                  onClick={() => router.push('/equipment')}
                  className="text-blue-600 hover:text-blue-700 font-medium"
                >
                  <PERSON><PERSON><PERSON><PERSON> lý thiết bị
                </button>
                <ChevronRight className="w-4 h-4 text-gray-400" />
                <span className="text-gray-500">Thêm mới</span>
              </nav>

              {/* Header Content */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center justify-center w-10 h-10 bg-green-100 rounded-lg">
                    <Plus className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">Thêm thiết bị mới</h1>
                    <p className="text-gray-600 mt-1">
                      Nhập đầy đủ thông tin để tạo thiết bị mới trong hệ thống
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3 mt-4 sm:mt-0">
                  <Button
                    variant="outline"
                    onClick={() => router.push('/equipment')}
                    className="flex items-center gap-2 bg-white"
                  >
                    <ArrowLeft className="w-4 h-4" />
                    Quay lại danh sách
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => router.push('/equipment')}
                    className="flex items-center gap-2 bg-white"
                  >
                    <List className="w-4 h-4" />
                    Xem danh sách
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Form Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <EquipmentForm onSuccess={() => router.push('/equipment')} />
        </div>
      </div>
    </MainLayout>
  );
}