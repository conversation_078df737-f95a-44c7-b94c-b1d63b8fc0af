import { NextRequest, NextResponse } from 'next/server';
import { googleDriveServiceNoLogin } from '@/services/googleDriveServiceNoLogin';

export async function POST(request: NextRequest) {
  try {
    // Check if Google Drive is configured
    const isConfigured = await googleDriveServiceNoLogin.isAuthenticated();
    console.log('isConfigured', isConfigured);

    if (!isConfigured) {
      return NextResponse.json(
        { error: 'Google Drive not configured' },
        { status: 503 }
      );
    }

    // Get file IDs from request body
    const { fileIds } = await request.json();
    
    if (!fileIds || !Array.isArray(fileIds) || fileIds.length === 0) {
      return NextResponse.json(
        { error: 'No file IDs provided' },
        { status: 400 }
      );
    }

    // Fetch file metadata for each ID
    const files = await Promise.all(
      fileIds.map(async (fileId) => {
        try {
          const fileData = await googleDriveServiceNoLogin.getFile(fileId);
          return {
            id: fileData.id,
            name: fileData.name,
            mimeType: fileData.mimeType,
            url: fileData.webViewLink,
            embedUrl: fileData.embedLink,
            iconUrl: fileData.iconLink,
            size: fileData.size,
            modifiedTime: fileData.modifiedTime,
            success: true
          };
        } catch (error) {
          console.error(`Error fetching file ${fileId}:`, error);
          return {
            id: fileId,
            success: false,
            error: 'File not found or access denied'
          };
        }
      })
    );

    return NextResponse.json({
      success: true,
      files: files.filter(f => f.success)
    });

  } catch (error) {
    console.error('Google Drive batch fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch files from Google Drive' },
      { status: 500 }
    );
  }
}