'use client';

import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/Button';
import { FolderOpen, FileText } from 'lucide-react';

declare global {
  interface Window {
    google: any;
    gapi: any;
    googlePickerCallback: (data: any) => void;
  }
}

interface GoogleDrivePickerProps {
  onFilesSelected: (files: GoogleDriveFile[]) => void;
  multiSelect?: boolean;
  acceptedTypes?: string[];
  disabled?: boolean;
}

export interface GoogleDriveFile {
  id: string;
  name: string;
  mimeType: string;
  iconUrl: string;
  url: string;
  embedUrl?: string;
}

export function GoogleDrivePicker({
  onFilesSelected,
  multiSelect = true,
  acceptedTypes = [
    'application/vnd.google-apps.document',
    'application/vnd.google-apps.spreadsheet',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/msword',
    'application/vnd.ms-excel',
    'application/pdf',
    'image/jpeg',
    'image/png'
  ],
  disabled = false
}: GoogleDrivePickerProps) {
  const [pickerApiLoaded, setPickerApiLoaded] = useState(false);
  const [oauthToken, setOauthToken] = useState<string | null>(null);

  useEffect(() => {
    // Load Google API
    const script = document.createElement('script');
    script.src = 'https://apis.google.com/js/api.js';
    script.onload = () => {
      window.gapi.load('auth', () => {});
      window.gapi.load('picker', () => {
        setPickerApiLoaded(true);
      });
    };
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  const authenticate = async () => {
    // For server-side auth with refresh token, we need to get a client-side token
    // This can be done through a popup flow or by getting a token from the server
    try {
      const response = await fetch('/api/google-drive/client-token');
      const data = await response.json();
      if (data.accessToken) {
        return data.accessToken;
      }
      throw new Error('No access token received');
    } catch (error) {
      console.error('Authentication error:', error);
      throw error;
    }
  };

  const createPicker = async () => {
    try {
      const token = oauthToken || await authenticate();
      if (!oauthToken) setOauthToken(token);

      const docsView = new window.google.picker.DocsView()
        .setIncludeFolders(true)
        .setSelectFolderEnabled(false);

      // Set mime type filter if specified
      if (acceptedTypes.length > 0) {
        docsView.setMimeTypes(acceptedTypes.join(','));
      }

      const picker = new window.google.picker.PickerBuilder()
        .addView(docsView)
        .setOAuthToken(token)
        .setDeveloperKey(process.env.NEXT_PUBLIC_GOOGLE_API_KEY)
        .setCallback(pickerCallback)
        .setTitle('Chọn file từ Google Drive')
        .setLocale('vi');

      if (multiSelect) {
        picker.enableFeature(window.google.picker.Feature.MULTISELECT_ENABLED);
      }

      picker.build().setVisible(true);
    } catch (error) {
      console.error('Error creating picker:', error);
      alert('Lỗi khi mở Google Drive. Vui lòng thử lại.');
    }
  };

  const pickerCallback = (data: any) => {
    if (data.action === window.google.picker.Action.PICKED) {
      const files: GoogleDriveFile[] = data.docs.map((doc: any) => ({
        id: doc.id,
        name: doc.name,
        mimeType: doc.mimeType,
        iconUrl: doc.iconUrl,
        url: doc.url,
        embedUrl: getEmbedUrl(doc)
      }));
      onFilesSelected(files);
    }
  };

  const getEmbedUrl = (doc: any): string | undefined => {
    if (doc.mimeType === 'application/vnd.google-apps.document') {
      return `https://docs.google.com/document/d/${doc.id}/edit?embedded=true`;
    } else if (doc.mimeType === 'application/vnd.google-apps.spreadsheet') {
      return `https://docs.google.com/spreadsheets/d/${doc.id}/edit?embedded=true`;
    } else if (doc.mimeType === 'application/vnd.google-apps.presentation') {
      return `https://docs.google.com/presentation/d/${doc.id}/edit?embedded=true`;
    } else if (doc.mimeType.startsWith('image/')) {
      return `https://drive.google.com/uc?id=${doc.id}&export=view`;
    } else {
      return `https://drive.google.com/file/d/${doc.id}/preview`;
    }
  };

  return (
    <Button
      type="button"
      onClick={createPicker}
      disabled={disabled || !pickerApiLoaded}
      className="flex items-center gap-2"
      variant="outline"
    >
      <FolderOpen className="w-4 h-4" />
      Chọn từ Google Drive
    </Button>
  );
}