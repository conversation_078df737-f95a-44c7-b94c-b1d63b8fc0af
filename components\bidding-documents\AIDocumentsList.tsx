'use client'

import { useState, useEffect } from 'react'
import { FileSpreadsheet, Download, Eye, Edit3, Trash2, Calendar, FileText, Search, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { GoogleDriveViewer } from '@/components/equipment/GoogleDriveViewer'
import { formatBytes } from '@/utils/format'
import { useToast } from '@/hooks/useToast'
import { useRouter } from 'next/navigation'
import type { BiddingDocument, BiddingDocumentAttachment } from '@/types/biddingDocument'

interface AIDocumentsListProps {
  documents: BiddingDocument[]
  isLoading: boolean
  onRefresh: () => void
  refreshTrigger?: number
}

export function AIDocumentsList({ 
  documents, 
  isLoading, 
  onRefresh,
  refreshTrigger = 0 
}: AIDocumentsListProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedAttachment, setSelectedAttachment] = useState<BiddingDocumentAttachment | null>(null)
  const [showViewer, setShowViewer] = useState(false)
  const toast = useToast()
  const router = useRouter()

  // Extract all AI attachments with document info
  const aiAttachments = documents.flatMap(doc => 
    (doc.attachments || [])
      .filter(att => 
        att.mimeType === 'application/vnd.google-apps.spreadsheet' || 
        att.mimeType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      )
      .map(att => ({
        ...att,
        documentId: doc.id,
        documentName: doc.name,
        documentCode: doc.code,
        customerName: doc.customerName,
      }))
  )

  // Filter attachments based on search
  const filteredAttachments = aiAttachments.filter(att => 
    att.fileName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    att.documentName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    att.documentCode?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    att.customerName?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleView = (attachment: typeof aiAttachments[0]) => {
    setSelectedAttachment(attachment)
    setShowViewer(true)
  }

  const handleDownload = async (attachment: typeof aiAttachments[0]) => {
    try {
      if (attachment.googleDriveUrl) {
        window.open(attachment.googleDriveUrl, '_blank')
      } else {
        window.open(attachment.fileUrl, '_blank')
      }
    } catch (error) {
      toast.error('Không thể tải xuống file')
    }
  }

  const handleEdit = (documentId: string) => {
    router.push(`/bidding-documents/${documentId}/edit`)
  }

  useEffect(() => {
    // Refresh effect when trigger changes
    if (refreshTrigger > 0) {
      onRefresh()
    }
  }, [refreshTrigger])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <RefreshCw className="w-8 h-8 animate-spin text-purple-600" />
        <span className="ml-3 text-gray-600">Đang tải danh sách hồ sơ AI...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Search Bar */}
      <div className="flex items-center gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            placeholder="Tìm kiếm theo tên file, mã hồ sơ, khách hàng..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          />
        </div>
        <Button
          variant="outline"
          onClick={onRefresh}
          className="flex items-center gap-2"
        >
          <RefreshCw className="w-4 h-4" />
          Làm mới
        </Button>
      </div>

      {/* AI Documents Grid */}
      {filteredAttachments.length === 0 ? (
        <div className="text-center py-12">
          <FileSpreadsheet className="w-16 h-16 mx-auto text-gray-300 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchTerm ? 'Không tìm thấy hồ sơ AI nào' : 'Chưa có hồ sơ AI nào'}
          </h3>
          <p className="text-gray-600">
            {searchTerm ? 'Thử tìm kiếm với từ khóa khác' : 'Bắt đầu tạo hồ sơ AI bằng cách chuyển sang tab "Tạo hồ sơ mới"'}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredAttachments.map((attachment) => (
            <div
              key={attachment.id}
              className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200"
            >
              <div className="p-6">
                {/* File Info */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-3 bg-purple-100 rounded-lg">
                      <FileSpreadsheet className="w-6 h-6 text-purple-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-gray-900 truncate" title={attachment.fileName}>
                        {attachment.fileName}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {formatBytes(attachment.fileSize)}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Document Info */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center gap-2 text-sm">
                    <FileText className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">Hồ sơ:</span>
                    <span className="font-medium text-gray-900 truncate" title={attachment.documentName}>
                      {attachment.documentCode} - {attachment.documentName}
                    </span>
                  </div>
                  {attachment.customerName && (
                    <div className="flex items-center gap-2 text-sm">
                      <span className="text-gray-600">Khách hàng:</span>
                      <span className="text-gray-900">{attachment.customerName}</span>
                    </div>
                  )}
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-600">Tạo lúc:</span>
                    <span className="text-gray-900">
                      {new Date(attachment.uploadedAt).toLocaleString('vi-VN')}
                    </span>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center gap-2 pt-4 border-t">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleView(attachment)}
                    className="flex-1 flex items-center justify-center gap-1"
                  >
                    <Eye className="w-4 h-4" />
                    Xem
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleDownload(attachment)}
                    className="flex-1 flex items-center justify-center gap-1"
                  >
                    <Download className="w-4 h-4" />
                    Tải
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleEdit(attachment.documentId)}
                    className="flex-1 flex items-center justify-center gap-1"
                  >
                    <Edit3 className="w-4 h-4" />
                    Sửa
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Google Drive Viewer */}
      {showViewer && selectedAttachment && (
        <GoogleDriveViewer
          file={{
            id: selectedAttachment.googleDriveId || selectedAttachment.id,
            name: selectedAttachment.fileName,
            mimeType: selectedAttachment.mimeType,
            url: selectedAttachment.googleDriveUrl || selectedAttachment.fileUrl
          }}
          onClose={() => {
            setShowViewer(false)
            setSelectedAttachment(null)
          }}
          allowEdit={true}
        />
      )}
    </div>
  )
}