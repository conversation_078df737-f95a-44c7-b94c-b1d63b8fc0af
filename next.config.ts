import type { NextConfig } from 'next'

const nextConfig: NextConfig = {
  reactStrictMode: true,
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Disable SWC minification if there are issues
  swcMinify: true,
  // Increase build memory
  experimental: {
    // Disable build activity indicator for cleaner logs
    webVitalsAttribution: ['CLS', 'LCP'],
  },
  // Serve uploads directory
  async rewrites() {
    return [
      {
        source: '/uploads/:path*',
        destination: '/api/uploads/:path*',
      },
    ]
  },
}

export default nextConfig