'use client';

import React from 'react';
import { SimpleTooltip } from './Tooltip';
import { cn } from '@/lib/utils';

interface TruncatedTextProps {
  text: string;
  className?: string;
  maxWidth?: string;
  showTooltip?: boolean;
}

export function TruncatedText({ 
  text, 
  className, 
  maxWidth = 'max-w-xs',
  showTooltip = true 
}: TruncatedTextProps) {
  const needsTruncation = showTooltip;
  
  const textElement = (
    <div className={cn(
      'truncate',
      maxWidth,
      className
    )}>
      {text}
    </div>
  );

  if (!needsTruncation || !text) {
    return textElement;
  }

  return (
    <SimpleTooltip content={text}>
      {textElement}
    </SimpleTooltip>
  );
}