{"name": "bidding-system", "version": "1.0.0", "description": "<PERSON><PERSON> thống thầu thông minh", "private": true, "scripts": {"dev": "next dev -p 3002", "build": "next build", "start": "next start -p 3002", "lint": "next lint", "test": "jest && playwright test", "test:unit": "jest", "test:unit:watch": "jest --watch", "test:unit:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:ui": "playwright test --ui", "test:visual": "playwright test --config=playwright.visual.config.ts", "db:migrate": "dotenv -e .env.local -- prisma migrate dev", "db:push": "dotenv -e .env.local -- prisma db push", "db:seed": "dotenv -e .env.local -- tsx prisma/seed.ts", "db:studio": "dotenv -e .env.local -- prisma studio", "db:generate": "dotenv -e .env.local -- prisma generate", "google:token": "node scripts/get-refresh-token.js"}, "dependencies": {"@prisma/client": "^6.13.0", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-tabs": "^1.1.13", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query": "^5.84.2", "@types/mime-types": "^3.0.1", "@types/react": "^19.1.9", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^19.1.7", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "googleapis": "^157.0.0", "jose": "^6.0.12", "js-cookie": "^3.0.5", "lucide-react": "^0.539.0", "mime-types": "^3.0.1", "next": "^15.4.6", "postcss": "^8.5.6", "prisma": "^6.13.0", "react": "^19.1.1", "react-datepicker": "^8.7.0", "react-day-picker": "^9.9.0", "react-dom": "^19.1.1", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "typescript": "^5.9.2", "xlsx": "^0.18.5", "zod": "^4.0.16"}, "devDependencies": {"@playwright/test": "^1.54.2", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^3.0.0", "@types/jest": "^30.0.0", "@types/js-cookie": "^3.0.6", "@types/node": "^24.2.1", "@types/whatwg-fetch": "^0.0.33", "dotenv": "^17.2.1", "dotenv-cli": "^10.0.0", "eslint": "^9.33.0", "eslint-config-next": "^15.4.6", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "msw": "^2.10.4", "ts-jest": "^29.4.1", "ts-node": "^10.9.2", "tsx": "^4.20.3", "whatwg-fetch": "^3.6.20"}}