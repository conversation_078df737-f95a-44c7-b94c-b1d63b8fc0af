'use client'

import React, { createContext, useContext, useEffect, useState, useCallback, useRef } from 'react'
import { useRouter } from 'next/navigation'
import type { User, LoginRequest } from '@/types/auth'
import { authService } from '@/services/authService'
import { authUtils } from '@/utils/authUtils'
import { tokenManager } from '@/lib/tokenManager'

interface AuthContextType {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  isVerifying: boolean
  login: (credentials: LoginRequest) => Promise<void>
  logout: () => Promise<void>
  refreshUser: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isVerifying, setIsVerifying] = useState(false)
  const router = useRouter()
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    checkAuth()
  }, [])

  // No need for auto-refresh interval - token refresh is handled automatically by API client
  // when making requests

  const checkAuth = async () => {
    // Check for cached user data first for immediate UI rendering
    const cachedUser = authUtils.getCachedUser()
    
    try {
      if (cachedUser) {
        setUser(cachedUser)
        setIsLoading(false) // Set loading false immediately if we have cached user
      }

      // Check if we have access token in memory or can refresh it
      let hasToken = authService.isAuthenticated()
      
      if (!hasToken) {
        // Try to refresh token using the refresh token cookie
        try {
          await authService.refreshToken()
          hasToken = authService.isAuthenticated()
        } catch (error: any) {
          // Refresh failed, user needs to login
          console.error('Token refresh failed:', error)
          
          // If it's a 401 error, the cookies are invalid
          if (error.message?.includes('401') || error.message?.includes('Session not found')) {
            // Clear any cached user data
            authUtils.clearCachedUser()
            setUser(null)
            setIsLoading(false)
            router.push('/login')
            return
          }
        }
      }
      
      if (hasToken) {
        setIsVerifying(true)
        const currentUser = await authService.getCurrentUser()
        
        setUser(currentUser)
        // Update cache with fresh data
        authUtils.setCachedUser(currentUser)
      } else {
        // No auth token and refresh failed, redirect to login
        authUtils.clearCachedUser()
        setUser(null)
        setIsLoading(false)
        router.push('/login')
        return
      }
    } catch (error: any) {
      console.error('Auth check failed:', error)
      
      // For any authentication error, clear everything and redirect to login
      authUtils.clearCachedUser()
      setUser(null)
      setIsLoading(false)
      
      // Clear token from memory
      tokenManager.clear()
      
      // Always redirect to login page on any auth error
      router.push('/login')
      return
    } finally {
      setIsVerifying(false)
      setIsLoading(false)
    }
  }



  const login = async (credentials: LoginRequest) => {
    const response = await authService.login(credentials)
    setUser(response.user)
    // Cache user data after successful login
    authUtils.setCachedUser(response.user)
    router.push('/dashboard')
  }

  const logout = async () => {
    await authService.logout()
    setUser(null)
    // Clear cached user data on logout
    authUtils.clearCachedUser()
    router.push('/login')
  }

  const refreshUser = async () => {
    try {
      const currentUser = await authService.getCurrentUser()
      setUser(currentUser)
      // Update cache with fresh data
      authUtils.setCachedUser(currentUser)
    } catch (error) {
      console.error('Failed to refresh user:', error)
      authUtils.clearCachedUser()
      setUser(null)
      
      // Clear token from memory
      tokenManager.clear()
      
      // Redirect to login
      router.push('/login')
    }
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated: !!user,
        isLoading,
        isVerifying,
        login,
        logout,
        refreshUser,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}