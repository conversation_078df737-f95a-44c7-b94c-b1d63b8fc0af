import type { 
  Bidding, 
  Bid<PERSON>Filters, 
  BiddingsResponse, 
  CreateBiddingRequest, 
  UpdateBiddingRequest,
  BiddingParticipant 
} from '@/types/bidding'
import { BaseService } from './baseService'

export class BiddingService extends BaseService {
  private static instance: BiddingService

  private constructor() {
    super({ baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api/v1' })
  }

  static getInstance(): BiddingService {
    if (!BiddingService.instance) {
      BiddingService.instance = new BiddingService()
    }
    return BiddingService.instance
  }

  async getBiddings(filters: BiddingFilters = {}, signal?: AbortSignal): Promise<BiddingsResponse> {
    const requestBody: any = {
      page: filters.page ? filters.page - 1 : 0,
      size: filters.limit || 10,
      search: filters.search,
      status: filters.status,
      method: filters.method,
      category: filters.category,
      projectId: filters.projectId,
      startDate: filters.startDate,
      endDate: filters.endDate,
      sortBy: filters.sortBy || 'createdAt',
      sortOrder: filters.sortOrder || 'desc',
    }

    const data = await this.post<any>('/biddings/search', requestBody, { signal })

    return {
      biddings: data.content || [],
      total: data.totalElements || 0,
      page: (data.number || 0) + 1,
      totalPages: data.totalPages || 0,
    }
  }

  async getBiddingById(id: number, signal?: AbortSignal): Promise<Bidding> {
    return this.get<Bidding>(`/biddings/${id}`, signal)
  }

  async createBidding(data: CreateBiddingRequest): Promise<Bidding> {
    return this.post<Bidding>('/biddings', data)
  }

  async updateBidding(id: number, data: UpdateBiddingRequest): Promise<Bidding> {
    return this.put<Bidding>(`/biddings/${id}`, data)
  }

  async deleteBidding(id: number): Promise<void> {
    return this.delete<void>(`/biddings/${id}`)
  }

  async publishBidding(id: number): Promise<Bidding> {
    return this.post<Bidding>(`/biddings/${id}/publish`)
  }

  async cancelBidding(id: number, reason: string): Promise<Bidding> {
    return this.post<Bidding>(`/biddings/${id}/cancel`, { reason })
  }

  async getParticipants(biddingId: number, signal?: AbortSignal): Promise<BiddingParticipant[]> {
    return this.get<BiddingParticipant[]>(`/biddings/${biddingId}/participants`, signal)
  }

  async registerParticipant(biddingId: number, contractorId: number): Promise<BiddingParticipant> {
    return this.post<BiddingParticipant>(`/biddings/${biddingId}/participants`, { contractorId })
  }

  async submitProposal(
    biddingId: number, 
    participantId: number, 
    proposalData: any
  ): Promise<BiddingParticipant> {
    return this.post<BiddingParticipant>(
      `/biddings/${biddingId}/participants/${participantId}/submit`,
      proposalData
    )
  }

  async evaluateProposals(biddingId: number, evaluations: any[]): Promise<BiddingParticipant[]> {
    return this.post<BiddingParticipant[]>(`/biddings/${biddingId}/evaluate`, { evaluations })
  }

  async selectWinner(biddingId: number, participantId: number): Promise<Bidding> {
    return this.post<Bidding>(`/biddings/${biddingId}/winner`, { participantId })
  }

  async exportBiddings(filters: BiddingFilters = {}): Promise<Blob> {
    const requestBody: any = {
      search: filters.search,
      status: filters.status,
      method: filters.method,
      category: filters.category,
      startDate: filters.startDate,
      endDate: filters.endDate,
    }

    const response = await fetch(`${this.baseUrl}/biddings/export`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    })

    if (!response.ok) {
      throw new Error('Export failed')
    }

    return response.blob()
  }
}

export const biddingService = BiddingService.getInstance()