# Chức năng: <PERSON><PERSON><PERSON><PERSON> lý thiết bị

## <PERSON><PERSON> tả
Chức năng cho phép người dùng có quyền thực hiện các thao tác: **tạo**, **chỉnh sửa**, **xóa** thiết bị.  
- Mỗi thiết bị thuộc **một danh mục cụ thể**.  
- Mỗi thiết bị có thể có **nhiều phiên bản thông số kỹ thuật** và có khả năng chọn **1 phiên bản làm mặc định**.  
- Hỗ trợ **theo dõi và xem lại** các phiên bản đã lưu trước đó.

## Tác nhân
- Người dùng có quyền **quản lý thiết bị**

## Điều kiện trước
- Người dùng đã đăng nhập hệ thống  
- <PERSON><PERSON> quyền thao tác thiết bị  
- Cơ sở dữ liệu và danh mục thiết bị đã tồn tại  

## Điều kiện sau
- Thiết bị được cập nhật vào hệ thống  
- Danh sách hiển thị chính xác theo hành động  
- Thông tin phiên bản được gắn đúng với thiết bị tương ứng  

## Ngoại lệ
- Trùng mã thiết bị  

## Các yêu cầu đặc biệt
- Mỗi thiết bị phải có **ít nhất một phiên bản** thông số kỹ thuật  
- Mỗi thiết bị chỉ thuộc **một danh mục**  
- Mỗi thiết bị có tối đa **1 phiên bản** được đánh dấu "mặc định"  
- Hệ thống cho phép **sao chép từ phiên bản cũ** khi tạo phiên bản mới  

---

## Bảng Use Case chi tiết

| **Mục**                | **Nội dung** |
|------------------------|--------------|
| **Tên Use Case**       | Quản lý thiết bị |
| **Tác nhân chính**     | Người dùng có quyền quản lý thiết bị |
| **Mục tiêu**           | Quản lý thông tin thiết bị và các phiên bản thông số kỹ thuật, đảm bảo dữ liệu chính xác và dễ tra cứu |
| **Điều kiện trước**    | - Người dùng đã đăng nhập<br>- Có quyền thao tác thiết bị<br>- Cơ sở dữ liệu và danh mục thiết bị đã tồn tại |
| **Điều kiện sau**      | - Thiết bị được cập nhật thành công<br>- Danh sách thiết bị hiển thị đúng<br>- Phiên bản gắn đúng với thiết bị |
| **Luồng chính**        | 1. Người dùng chọn thao tác (Tạo mới / Chỉnh sửa / Xóa)<br>2. Nhập hoặc cập nhật thông tin thiết bị<br>3. Nhập hoặc chọn phiên bản thôn


## Biểu đồ luồng xử lý chức năng

## 1. Tạo / Sửa / Xóa thiết bị

### Tạo thiết bị
1. Người dùng nhấn **"Tạo thiết bị"**  
2. Nhập thông tin: **Tên**, **Mã**, **Danh mục**  
3. Hệ thống kiểm tra tính hợp lệ của dữ liệu  
4. Lưu thông tin vào **Cơ sở dữ liệu (CSDL)**  

### Sửa thiết bị
1. Người dùng chọn thiết bị cần sửa  
2. Cập nhật thông tin cần thay đổi  
3. Lưu vào **CSDL**  

### Xóa thiết bị
1. Người dùng chọn thiết bị cần xóa  
2. Xóa khỏi **CSDL**  

---

## 2. Quản lý phiên bản thông số kỹ thuật

### Thêm phiên bản
1. Trong giao diện thiết bị, người dùng chọn **"Thêm phiên bản"**  
2. Nhập các **thông số kỹ thuật**  
3. Có thể chọn phiên bản là **"mặc định"**  

### Xem lại phiên bản cũ
- Khi hiển thị thông tin thiết bị, hệ thống cho phép **xem lại các phiên bản đã lưu trước đó**
