import type { 
  Document, 
  DocumentFilters, 
  DocumentsResponse
} from '@/types/document';
import { BaseService } from './baseService';
import { tokenManager } from '@/lib/tokenManager';

export class DocumentService extends BaseService {
  private static instance: DocumentService;

  private constructor() {
    super({ useLocalApi: true });
  }

  static getInstance(): DocumentService {
    if (!DocumentService.instance) {
      DocumentService.instance = new DocumentService();
    }
    return DocumentService.instance;
  }

  async getDocuments(filters: DocumentFilters = {}, signal?: AbortSignal): Promise<DocumentsResponse> {
    const params = new URLSearchParams();
    
    if (filters.search) params.append('search', filters.search);
    if (filters.documentType) params.append('documentType', filters.documentType);
    if (filters.tags && filters.tags.length > 0) {
      filters.tags.forEach(tag => params.append('tags', tag));
    }
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.sortBy) params.append('sortBy', filters.sortBy);
    if (filters.sortOrder) params.append('sortOrder', filters.sortOrder);

    const queryString = params.toString();
    const url = `/documents${queryString ? `?${queryString}` : ''}`;
    
    return this.get<DocumentsResponse>(url, signal);
  }

  async getDocumentById(id: string, signal?: AbortSignal): Promise<Document> {
    return this.get<Document>(`/documents/${id}`, signal);
  }

  async uploadDocument(formData: FormData, signal?: AbortSignal): Promise<Document> {
    // Get the token for authentication
    const token = tokenManager.getAccessToken();
    const headers: HeadersInit = {};
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(`/api/documents`, {
      method: 'POST',
      body: formData,
      headers,
      signal,
      credentials: 'include'
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Upload failed' }));
      throw new Error(error.message || 'Failed to upload document');
    }

    return response.json();
  }

  async deleteDocument(id: string, signal?: AbortSignal): Promise<void> {
    return this.delete(`/documents/${id}`, { signal });
  }

  async downloadDocument(id: string): Promise<void> {
    // Get the token for authentication
    const token = tokenManager.getAccessToken();
    const headers: HeadersInit = {};
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(`/api/documents/download/${id}`, {
      method: 'GET',
      headers,
      credentials: 'include'
    });

    if (!response.ok) {
      throw new Error('Failed to download document');
    }

    const blob = await response.blob();
    const contentDisposition = response.headers.get('content-disposition');
    let filename = 'download';
    
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="?(.+)"?/);
      if (filenameMatch) {
        filename = filenameMatch[1];
      }
    }

    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }

  async getTags(signal?: AbortSignal): Promise<string[]> {
    return this.get<string[]>('/documents/tags', signal);
  }

  async bulkDelete(ids: string[], signal?: AbortSignal): Promise<void> {
    return this.post('/documents/bulk-delete', { ids }, { signal });
  }
}

export const documentService = DocumentService.getInstance();