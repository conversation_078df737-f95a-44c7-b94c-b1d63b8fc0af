'use client'

import { FileText, Plus } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/Button'

interface EmptyStateProps {
  onCreateClick: () => void
  hasFilters?: boolean
}

export function EmptyState({ onCreateClick, hasFilters = false }: EmptyStateProps) {
  return (
    <div className="flex flex-col items-center justify-center py-12 px-4">
      <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
        <FileText className="w-8 h-8 text-gray-400 dark:text-gray-600" />
      </div>
      
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
        {hasFilters ? 'No documents found' : 'No bidding documents yet'}
      </h3>
      
      <p className="text-sm text-gray-500 dark:text-gray-400 text-center mb-6 max-w-sm">
        {hasFilters 
          ? 'Try changing your filters or search keywords.'
          : 'Start by creating your first bidding document.'
        }
      </p>
    </div>
  )
}