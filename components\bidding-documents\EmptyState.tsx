'use client'

import { FileText, Plus } from 'lucide-react'
import { Button } from '@/components/ui/Button'

interface EmptyStateProps {
  onCreateClick: () => void
  hasFilters?: boolean
}

export function EmptyState({ onCreateClick, hasFilters = false }: EmptyStateProps) {
  return (
    <div className="flex flex-col items-center justify-center py-12 px-4">
      <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
        <FileText className="w-8 h-8 text-gray-400 dark:text-gray-600" />
      </div>
      
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
        {hasFilters ? 'Không tìm thấy hồ sơ' : '<PERSON><PERSON><PERSON> c<PERSON> hồ sơ dự thầu'}
      </h3>
      
      <p className="text-sm text-gray-500 dark:text-gray-400 text-center mb-6 max-w-sm">
        {hasFilters 
          ? 'Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm của bạn.'
          : '<PERSON><PERSON><PERSON> đầu bằng cách tạo hồ sơ dự thầu đầu tiên của bạn.'
        }
      </p>
    </div>
  )
}