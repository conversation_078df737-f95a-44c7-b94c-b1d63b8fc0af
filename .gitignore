# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# public
public/

# uploaded files
uploads/bidding-documents/*
uploads/equipment/*
public/uploads/equipment/*
public/uploads/bidding-documents/*

# Keep the directory structure but ignore contents
!uploads/bidding-documents/.gitkeep
!uploads/equipment/.gitkeep
!public/uploads/equipment/.gitkeep
!public/uploads/bidding-documents/.gitkeep