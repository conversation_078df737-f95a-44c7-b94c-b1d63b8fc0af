# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# public (but keep upload structure)
public/*
!public/uploads/

# uploaded files
uploads/bidding-documents/*
uploads/equipment/*
uploads/documents/*
public/uploads/equipment/*
public/uploads/bidding-documents/*
public/uploads/documents/*

# Keep the directory structure but ignore contents
!uploads/bidding-documents/.gitkeep
!uploads/equipment/.gitkeep
!uploads/documents/.gitkeep
!public/uploads/equipment/.gitkeep
!public/uploads/bidding-documents/.gitkeep
!public/uploads/documents/.gitkeep