import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useSession, useLogin, useLogout, useIsAuthenticated } from '@/hooks/queries/useAuth'
import { authService } from '@/services/authService'
import { useRouter } from 'next/navigation'
import { useToast } from '@/hooks/useToast'
import type { LoginRequest } from '@/types/auth'

// Mock dependencies
jest.mock('@/services/authService')
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn(),
  useSearchParams: jest.fn(),
}))
jest.mock('@/hooks/useToast')

describe('useAuth Hooks', () => {
  let queryClient: QueryClient
  const mockRouter = { push: jest.fn() }
  const mockToast = { success: jest.fn(), error: jest.fn() }

  const mockUser = {
    id: 1,
    email: '<EMAIL>',
    name: 'Test User',
    role: 'ADMIN' as const,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  }

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { 
          retry: false,
          gcTime: 0,
          staleTime: 0,
        },
        mutations: { retry: false },
      },
    })
    jest.clearAllMocks()
    ;(useRouter as jest.Mock).mockReturnValue(mockRouter)
    ;(useToast as jest.Mock).mockReturnValue(mockToast)
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )

  describe('useSession', () => {
    it('fetches user session successfully', async () => {
      ;(authService.getCurrentUser as jest.Mock).mockResolvedValue(mockUser)

      const { result } = renderHook(() => useSession(), { wrapper })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockUser)
      expect(authService.getCurrentUser).toHaveBeenCalledTimes(1)
    })

    it('handles error when fetching session fails', async () => {
      const error = new Error('Session fetch failed')
      ;(authService.getCurrentUser as jest.Mock).mockRejectedValue(error)

      const { result } = renderHook(() => useSession(), { wrapper })

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      }, { timeout: 3000 })

      expect(result.current.isError).toBe(true)
      expect(result.current.error).toEqual(error)
    })

    it('does not fetch when disabled', () => {
      ;(authService.getCurrentUser as jest.Mock).mockResolvedValue(mockUser)

      renderHook(() => useSession(false), { wrapper })

      expect(authService.getCurrentUser).not.toHaveBeenCalled()
    })
  })

  describe('useLogin', () => {
    it('successfully logs in user', async () => {
      const loginResponse = {
        token: 'mock-token',
        refreshToken: 'mock-refresh',
        user: mockUser,
      }
      ;(authService.login as jest.Mock).mockResolvedValue(loginResponse)

      const { result } = renderHook(() => useLogin(), { wrapper })

      const credentials: LoginRequest = {
        email: '<EMAIL>',
        password: 'password123',
      }

      await result.current.mutateAsync(credentials)

      await waitFor(() => {
        expect(mockToast.success).toHaveBeenCalledWith('Login successful!')
        expect(mockRouter.push).toHaveBeenCalledWith('/dashboard')
      })

      expect(authService.login).toHaveBeenCalledWith(credentials)
    })

    it('handles login error', async () => {
      const error = new Error('Invalid credentials')
      ;(authService.login as jest.Mock).mockRejectedValue(error)

      const { result } = renderHook(() => useLogin(), { wrapper })

      const credentials: LoginRequest = {
        email: '<EMAIL>',
        password: 'wrongpass',
      }

      await expect(result.current.mutateAsync(credentials)).rejects.toThrow('Invalid credentials')

      await waitFor(() => {
        expect(mockToast.error).toHaveBeenCalledWith('Invalid credentials')
      })

      expect(mockRouter.push).not.toHaveBeenCalled()
    })

    it('shows generic error message when error has no message', async () => {
      const error = new Error()
      ;(authService.login as jest.Mock).mockRejectedValue(error)

      const { result } = renderHook(() => useLogin(), { wrapper })

      const credentials: LoginRequest = {
        email: '<EMAIL>',
        password: 'password123',
      }

      try {
        await result.current.mutateAsync(credentials)
      } catch (e) {
        // Expected to throw
      }

      await waitFor(() => {
        expect(mockToast.error).toHaveBeenCalledWith('Login failed. Please check your credentials.')
      })
    })
  })

  describe('useLogout', () => {
    it('successfully logs out user', async () => {
      ;(authService.logout as jest.Mock).mockResolvedValue(undefined)

      const { result } = renderHook(() => useLogout(), { wrapper })

      await result.current.mutateAsync()

      await waitFor(() => {
        expect(mockToast.success).toHaveBeenCalledWith('Logged out successfully')
        expect(mockRouter.push).toHaveBeenCalledWith('/login')
      })

      expect(authService.logout).toHaveBeenCalledTimes(1)
    })

    it('clears query cache on logout', async () => {
      ;(authService.logout as jest.Mock).mockResolvedValue(undefined)
      const clearSpy = jest.spyOn(queryClient, 'clear')

      const { result } = renderHook(() => useLogout(), { wrapper })

      await result.current.mutateAsync()

      await waitFor(() => {
        expect(clearSpy).toHaveBeenCalled()
      })
    })
  })

  describe('useIsAuthenticated', () => {
    it('returns authenticated status when user exists', async () => {
      ;(authService.getCurrentUser as jest.Mock).mockResolvedValue(mockUser)

      const { result } = renderHook(() => useIsAuthenticated(), { wrapper })

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      expect(result.current.isAuthenticated).toBe(true)
      expect(result.current.user).toEqual(mockUser)
    })

    it('returns not authenticated when no user', async () => {
      ;(authService.getCurrentUser as jest.Mock).mockRejectedValue(new Error('Not authenticated'))

      const { result } = renderHook(() => useIsAuthenticated(), { wrapper })

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      }, { timeout: 2000 })

      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.user).toBeUndefined()
    })

    it('shows loading state initially', () => {
      ;(authService.getCurrentUser as jest.Mock).mockImplementation(
        () => new Promise(() => {}) // Never resolves
      )

      const { result } = renderHook(() => useIsAuthenticated(), { wrapper })

      expect(result.current.isLoading).toBe(true)
      expect(result.current.isAuthenticated).toBe(false)
    })
  })
})