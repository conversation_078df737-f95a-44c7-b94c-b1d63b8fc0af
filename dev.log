
> bidding-system@1.0.0 dev
> next dev

 ⚠ Warning: Found multiple lockfiles. Selecting /mnt/d/WorkSpace/package-lock.json.
   Consider removing the lockfiles at:
   * /mnt/d/WorkSpace/viet-my-medical/package-lock.json

   ▲ Next.js 15.4.6
   - Local:        http://localhost:3000
   - Network:      http://**************:3000
   - Environments: .env.local

 ✓ Starting...
 ✓ Ready in 16.2s
 ○ Compiling /middleware ...
 ✓ Compiled /middleware in 757ms (246 modules)
 ○ Compiling / ...
 ✓ Compiled / in 8.9s (646 modules)
 GET / 200 in 1325ms
 GET / 200 in 210ms
 ○ Compiling /dashboard ...
 ✓ Compiled /dashboard in 3.8s (984 modules)
 GET /dashboard 200 in 4596ms
prisma:query SELECT "public"."Session"."id", "public"."Session"."userId", "public"."Session"."token", "public"."Session"."refreshToken", "public"."Session"."ipAddress", "public"."Session"."userAgent", "public"."Session"."status"::text, "public"."Session"."expiresAt", "public"."Session"."createdAt", "public"."Session"."updatedAt", "public"."Session"."loggedOutAt" FROM "public"."Session" WHERE ("public"."Session"."id" = $1 AND "public"."Session"."status" = CAST($2::text AS "public"."SessionStatus")) LIMIT $3 OFFSET $4
prisma:query SELECT "public"."User"."id", "public"."User"."username", "public"."User"."email", "public"."User"."name", "public"."User"."role"::text, "public"."User"."department", "public"."User"."phone", "public"."User"."avatar", "public"."User"."status"::text, "public"."User"."emailVerified", "public"."User"."twoFactorEnabled" FROM "public"."User" WHERE "public"."User"."id" IN ($1) OFFSET $2
prisma:query UPDATE "public"."Session" SET "token" = $1, "refreshToken" = $2, "expiresAt" = $3, "ipAddress" = $4, "userAgent" = $5, "updatedAt" = $6 WHERE ("public"."Session"."id" = $7 AND 1=1) RETURNING "public"."Session"."id", "public"."Session"."userId", "public"."Session"."token", "public"."Session"."refreshToken", "public"."Session"."ipAddress", "public"."Session"."userAgent", "public"."Session"."status"::text, "public"."Session"."expiresAt", "public"."Session"."createdAt", "public"."Session"."updatedAt", "public"."Session"."loggedOutAt"
 POST /api/auth/refresh 200 in 4834ms
prisma:query INSERT INTO "public"."AuditLog" ("id","userId","action","details","ipAddress","createdAt") VALUES ($1,$2,$3,$4,$5,$6) RETURNING "public"."AuditLog"."id", "public"."AuditLog"."userId", "public"."AuditLog"."action", "public"."AuditLog"."details", "public"."AuditLog"."ipAddress", "public"."AuditLog"."createdAt"
 GET /dashboard 200 in 195ms
 ✓ Compiled /api/auth/session in 433ms (986 modules)
prisma:query SELECT "public"."Session"."id", "public"."Session"."userId", "public"."Session"."token", "public"."Session"."refreshToken", "public"."Session"."ipAddress", "public"."Session"."userAgent", "public"."Session"."status"::text, "public"."Session"."expiresAt", "public"."Session"."createdAt", "public"."Session"."updatedAt", "public"."Session"."loggedOutAt" FROM "public"."Session" WHERE ("public"."Session"."id" = $1 AND "public"."Session"."status" = CAST($2::text AS "public"."SessionStatus")) LIMIT $3 OFFSET $4
 GET /api/auth/session 200 in 772ms
prisma:query SELECT "public"."User"."id", "public"."User"."username", "public"."User"."email", "public"."User"."name", "public"."User"."role"::text, "public"."User"."department", "public"."User"."phone", "public"."User"."avatar", "public"."User"."status"::text, "public"."User"."emailVerified", "public"."User"."twoFactorEnabled", "public"."User"."createdAt", "public"."User"."updatedAt" FROM "public"."User" WHERE "public"."User"."id" IN ($1) OFFSET $2
 ○ Compiling /bidding-documents ...
 ✓ Compiled /bidding-documents in 1487ms (879 modules)
 GET /bidding-documents 200 in 1961ms
 GET /bidding-documents 200 in 177ms
 ✓ Compiled /api/bidding-documents in 483ms (1044 modules)
prisma:query SELECT 1
prisma:query SELECT "public"."Session"."id", "public"."Session"."userId", "public"."Session"."token", "public"."Session"."refreshToken", "public"."Session"."ipAddress", "public"."Session"."userAgent", "public"."Session"."status"::text, "public"."Session"."expiresAt", "public"."Session"."createdAt", "public"."Session"."updatedAt", "public"."Session"."loggedOutAt" FROM "public"."Session" WHERE ("public"."Session"."id" = $1 AND "public"."Session"."status" = CAST($2::text AS "public"."SessionStatus")) LIMIT $3 OFFSET $4
prisma:query SELECT "public"."User"."id", "public"."User"."username", "public"."User"."email", "public"."User"."name", "public"."User"."role"::text, "public"."User"."department", "public"."User"."status"::text FROM "public"."User" WHERE "public"."User"."id" IN ($1) OFFSET $2
prisma:query SELECT "public"."BiddingDocument"."id", "public"."BiddingDocument"."code", "public"."BiddingDocument"."name", "public"."BiddingDocument"."description", "public"."BiddingDocument"."customerName", "public"."BiddingDocument"."status"::text, "public"."BiddingDocument"."createdBy", "public"."BiddingDocument"."createdAt", "public"."BiddingDocument"."updatedAt" FROM "public"."BiddingDocument" WHERE 1=1 ORDER BY "public"."BiddingDocument"."createdAt" DESC LIMIT $1 OFFSET $2
prisma:query SELECT "public"."User"."id", "public"."User"."name", "public"."User"."username" FROM "public"."User" WHERE "public"."User"."id" IN ($1) OFFSET $2
prisma:query SELECT COUNT(*) AS "_count._all" FROM (SELECT "public"."BiddingDocument"."id" FROM "public"."BiddingDocument" WHERE 1=1 OFFSET $1) AS "sub"
 GET /api/bidding-documents?page=1&limit=10 200 in 978ms
 ○ Compiling /documents ...
 ✓ Compiled /documents in 4.7s (2203 modules)
 GET /documents 200 in 5100ms
 ○ Compiling /api/documents ...
 ✓ Compiled /api/documents in 1280ms (2209 modules)
prisma:query SELECT "public"."Session"."id", "public"."Session"."userId", "public"."Session"."token", "public"."Session"."refreshToken", "public"."Session"."ipAddress", "public"."Session"."userAgent", "public"."Session"."status"::text, "public"."Session"."expiresAt", "public"."Session"."createdAt", "public"."Session"."updatedAt", "public"."Session"."loggedOutAt" FROM "public"."Session" WHERE ("public"."Session"."id" = $1 AND "public"."Session"."status" = CAST($2::text AS "public"."SessionStatus")) LIMIT $3 OFFSET $4
prisma:query SELECT "public"."User"."id", "public"."User"."username", "public"."User"."email", "public"."User"."name", "public"."User"."role"::text, "public"."User"."department", "public"."User"."status"::text FROM "public"."User" WHERE "public"."User"."id" IN ($1) OFFSET $2
prisma:query SELECT "public"."Session"."id", "public"."Session"."userId", "public"."Session"."token", "public"."Session"."refreshToken", "public"."Session"."ipAddress", "public"."Session"."userAgent", "public"."Session"."status"::text, "public"."Session"."expiresAt", "public"."Session"."createdAt", "public"."Session"."updatedAt", "public"."Session"."loggedOutAt" FROM "public"."Session" WHERE ("public"."Session"."id" = $1 AND "public"."Session"."status" = CAST($2::text AS "public"."SessionStatus")) LIMIT $3 OFFSET $4
prisma:query SELECT "public"."User"."id", "public"."User"."username", "public"."User"."email", "public"."User"."name", "public"."User"."role"::text, "public"."User"."department", "public"."User"."status"::text FROM "public"."User" WHERE "public"."User"."id" IN ($1) OFFSET $2
prisma:query SELECT "public"."Document"."id", "public"."Document"."fileName", "public"."Document"."fileUrl", "public"."Document"."fileSize", "public"."Document"."documentType", "public"."Document"."tags", "public"."Document"."description", "public"."Document"."uploadedBy", "public"."Document"."uploadedAt" FROM "public"."Document" WHERE 1=1 ORDER BY "public"."Document"."uploadedAt" DESC LIMIT $1 OFFSET $2
prisma:query SELECT COUNT(*) AS "_count._all" FROM (SELECT "public"."Document"."id" FROM "public"."Document" WHERE 1=1 OFFSET $1) AS "sub"
 GET /api/documents?page=1&limit=10&sortBy=uploadedAt&sortOrder=desc 200 in 1718ms
prisma:query SELECT "public"."Document"."id", "public"."Document"."tags" FROM "public"."Document" WHERE 1=1 OFFSET $1
 GET /api/documents/tags 200 in 1744ms
 GET /bidding-documents 200 in 225ms
prisma:query SELECT 1
prisma:query SELECT "public"."Session"."id", "public"."Session"."userId", "public"."Session"."token", "public"."Session"."refreshToken", "public"."Session"."ipAddress", "public"."Session"."userAgent", "public"."Session"."status"::text, "public"."Session"."expiresAt", "public"."Session"."createdAt", "public"."Session"."updatedAt", "public"."Session"."loggedOutAt" FROM "public"."Session" WHERE ("public"."Session"."id" = $1 AND "public"."Session"."status" = CAST($2::text AS "public"."SessionStatus")) LIMIT $3 OFFSET $4
prisma:query SELECT "public"."User"."id", "public"."User"."username", "public"."User"."email", "public"."User"."name", "public"."User"."role"::text, "public"."User"."department", "public"."User"."status"::text FROM "public"."User" WHERE "public"."User"."id" IN ($1) OFFSET $2
prisma:query SELECT 1
prisma:query SELECT COUNT(*) AS "_count._all" FROM (SELECT "public"."BiddingDocument"."id" FROM "public"."BiddingDocument" WHERE 1=1 OFFSET $1) AS "sub"
prisma:query SELECT "public"."BiddingDocument"."id", "public"."BiddingDocument"."code", "public"."BiddingDocument"."name", "public"."BiddingDocument"."description", "public"."BiddingDocument"."customerName", "public"."BiddingDocument"."status"::text, "public"."BiddingDocument"."createdBy", "public"."BiddingDocument"."createdAt", "public"."BiddingDocument"."updatedAt" FROM "public"."BiddingDocument" WHERE 1=1 ORDER BY "public"."BiddingDocument"."createdAt" DESC LIMIT $1 OFFSET $2
 GET /api/bidding-documents?page=1&limit=10 200 in 219ms
prisma:query SELECT "public"."User"."id", "public"."User"."name", "public"."User"."username" FROM "public"."User" WHERE "public"."User"."id" IN ($1) OFFSET $2
 ○ Compiling /bidding-documents/[id]/edit ...
 ✓ Compiled /bidding-documents/[id]/edit in 2.6s (2256 modules)
 GET /bidding-documents/cmf5ai96i0003ahh4jap4elh4/edit 200 in 8017ms
 GET /bidding-documents/cmf5ai96i0003ahh4jap4elh4/edit 200 in 214ms
 ○ Compiling /api/bidding-documents/[id] ...
 ✓ Compiled /api/bidding-documents/[id] in 892ms (2258 modules)
prisma:query SELECT 1
prisma:query SELECT "public"."Session"."id", "public"."Session"."userId", "public"."Session"."token", "public"."Session"."refreshToken", "public"."Session"."ipAddress", "public"."Session"."userAgent", "public"."Session"."status"::text, "public"."Session"."expiresAt", "public"."Session"."createdAt", "public"."Session"."updatedAt", "public"."Session"."loggedOutAt" FROM "public"."Session" WHERE ("public"."Session"."id" = $1 AND "public"."Session"."status" = CAST($2::text AS "public"."SessionStatus")) LIMIT $3 OFFSET $4
prisma:query SELECT "public"."User"."id", "public"."User"."username", "public"."User"."email", "public"."User"."name", "public"."User"."role"::text, "public"."User"."department", "public"."User"."status"::text FROM "public"."User" WHERE "public"."User"."id" IN ($1) OFFSET $2
prisma:query SELECT "public"."BiddingDocument"."id", "public"."BiddingDocument"."code", "public"."BiddingDocument"."name", "public"."BiddingDocument"."description", "public"."BiddingDocument"."customerName", "public"."BiddingDocument"."status"::text, "public"."BiddingDocument"."createdBy", "public"."BiddingDocument"."createdAt", "public"."BiddingDocument"."updatedAt" FROM "public"."BiddingDocument" WHERE ("public"."BiddingDocument"."id" = $1 AND 1=1) LIMIT $2 OFFSET $3
prisma:query SELECT "public"."User"."id", "public"."User"."name", "public"."User"."username" FROM "public"."User" WHERE "public"."User"."id" IN ($1) OFFSET $2
prisma:query SELECT "public"."BiddingDocumentAttachment"."id", "public"."BiddingDocumentAttachment"."biddingDocumentId", "public"."BiddingDocumentAttachment"."fileName", "public"."BiddingDocumentAttachment"."fileUrl", "public"."BiddingDocumentAttachment"."fileSize", "public"."BiddingDocumentAttachment"."mimeType", "public"."BiddingDocumentAttachment"."source", "public"."BiddingDocumentAttachment"."googleDriveId", "public"."BiddingDocumentAttachment"."googleDriveUrl", "public"."BiddingDocumentAttachment"."uploadedBy", "public"."BiddingDocumentAttachment"."uploadedAt" FROM "public"."BiddingDocumentAttachment" WHERE "public"."BiddingDocumentAttachment"."biddingDocumentId" IN ($1) ORDER BY "public"."BiddingDocumentAttachment"."uploadedAt" DESC OFFSET $2
 GET /api/bidding-documents/cmf5ai96i0003ahh4jap4elh4 200 in 6260ms
 ○ Compiling /api/equipment ...
 ✓ Compiled /api/equipment in 1081ms (2260 modules)
Equipment GET request received
prisma:query SELECT "public"."Session"."id", "public"."Session"."userId", "public"."Session"."token", "public"."Session"."refreshToken", "public"."Session"."ipAddress", "public"."Session"."userAgent", "public"."Session"."status"::text, "public"."Session"."expiresAt", "public"."Session"."createdAt", "public"."Session"."updatedAt", "public"."Session"."loggedOutAt" FROM "public"."Session" WHERE ("public"."Session"."id" = $1 AND "public"."Session"."status" = CAST($2::text AS "public"."SessionStatus")) LIMIT $3 OFFSET $4
prisma:query SELECT "public"."User"."id", "public"."User"."username", "public"."User"."email", "public"."User"."name", "public"."User"."role"::text, "public"."User"."department", "public"."User"."status"::text FROM "public"."User" WHERE "public"."User"."id" IN ($1) OFFSET $2
Session verification result: authenticated
Fetching equipment with params: {
  page: 1,
  limit: 10,
  sortBy: 'createdAt',
  sortOrder: 'desc',
  where: { status: 'ACTIVE' }
}
prisma:query SELECT 1
prisma:query SELECT "public"."Equipment"."id", "public"."Equipment"."equipmentCode", "public"."Equipment"."name", "public"."Equipment"."description", "public"."Equipment"."model", "public"."Equipment"."manufacturer", "public"."Equipment"."unit", "public"."Equipment"."price", "public"."Equipment"."specifications", "public"."Equipment"."catalogId", "public"."Equipment"."status"::text, "public"."Equipment"."createdBy", "public"."Equipment"."createdAt", "public"."Equipment"."updatedAt" FROM "public"."Equipment" WHERE "public"."Equipment"."status" = CAST($1::text AS "public"."Status") ORDER BY "public"."Equipment"."createdAt" DESC LIMIT $2 OFFSET $3
prisma:query SELECT COUNT(*) AS "_count._all" FROM (SELECT "public"."Equipment"."id" FROM "public"."Equipment" WHERE "public"."Equipment"."status" = CAST($1::text AS "public"."Status") OFFSET $2) AS "sub"
prisma:query SELECT "public"."Catalog"."id", "public"."Catalog"."catalogCode", "public"."Catalog"."catalogName" FROM "public"."Catalog" WHERE "public"."Catalog"."id" IN ($1) OFFSET $2
prisma:query SELECT "public"."User"."id", "public"."User"."name", "public"."User"."email" FROM "public"."User" WHERE "public"."User"."id" IN ($1) OFFSET $2
Found 1 equipment items, total: 1
 GET /api/equipment?status=ACTIVE 200 in 1658ms
 ⨯ Failed to generate static paths for /api/bidding-documents/[id]:
[Error [PageNotFoundError]: Cannot find module for page: /api/bidding-documents/[id]/route] {
  type: 'PageNotFoundError',
  code: 'ENOENT'
}
 ○ Compiling /equipment ...
 ✓ Compiled /equipment in 1434ms (2291 modules)
 GET /equipment 200 in 1792ms
 ○ Compiling /api/auth/session ...
 ✓ Compiled /api/auth/session in 2.1s (2295 modules)
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/d/WorkSpace/viet-my-medical/.next/server/app/api/equipment/route.js'] {
  errno: -2,
  syscall: 'open',
  code: 'ENOENT',
  path: '/mnt/d/WorkSpace/viet-my-medical/.next/server/app/api/equipment/route.js',
  page: '/api/equipment'
}
prisma:query SELECT 1
prisma:query SELECT "public"."Session"."id", "public"."Session"."userId", "public"."Session"."token", "public"."Session"."refreshToken", "public"."Session"."ipAddress", "public"."Session"."userAgent", "public"."Session"."status"::text, "public"."Session"."expiresAt", "public"."Session"."createdAt", "public"."Session"."updatedAt", "public"."Session"."loggedOutAt" FROM "public"."Session" WHERE ("public"."Session"."id" = $1 AND "public"."Session"."status" = CAST($2::text AS "public"."SessionStatus")) LIMIT $3 OFFSET $4
prisma:query SELECT "public"."User"."id", "public"."User"."username", "public"."User"."email", "public"."User"."name", "public"."User"."role"::text, "public"."User"."department", "public"."User"."status"::text FROM "public"."User" WHERE "public"."User"."id" IN ($1) OFFSET $2
prisma:query SELECT 1
prisma:query SELECT "public"."Session"."id", "public"."Session"."userId", "public"."Session"."token", "public"."Session"."refreshToken", "public"."Session"."ipAddress", "public"."Session"."userAgent", "public"."Session"."status"::text, "public"."Session"."expiresAt", "public"."Session"."createdAt", "public"."Session"."updatedAt", "public"."Session"."loggedOutAt" FROM "public"."Session" WHERE ("public"."Session"."id" = $1 AND "public"."Session"."status" = CAST($2::text AS "public"."SessionStatus")) LIMIT $3 OFFSET $4
prisma:query SELECT "public"."User"."id", "public"."User"."username", "public"."User"."email", "public"."User"."name", "public"."User"."role"::text, "public"."User"."department", "public"."User"."phone", "public"."User"."avatar", "public"."User"."status"::text, "public"."User"."emailVerified", "public"."User"."twoFactorEnabled", "public"."User"."createdAt", "public"."User"."updatedAt" FROM "public"."User" WHERE "public"."User"."id" IN ($1) OFFSET $2
 ○ Compiling /_error ...
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/d/WorkSpace/viet-my-medical/.next/server/app/bidding-documents/page.js'] {
  errno: -2,
  syscall: 'open',
  code: 'ENOENT',
  path: '/mnt/d/WorkSpace/viet-my-medical/.next/server/app/bidding-documents/page.js',
  page: '/bidding-documents'
}
 GET /api/auth/session 200 in 3663ms
prisma:query SELECT COUNT(*) AS "_count._all" FROM (SELECT "public"."Catalog"."id" FROM "public"."Catalog" WHERE "public"."Catalog"."status" = CAST($1::text AS "public"."Status") OFFSET $2) AS "sub"
prisma:query SELECT "public"."Catalog"."id", "public"."Catalog"."catalogCode", "public"."Catalog"."catalogName", "public"."Catalog"."description", "public"."Catalog"."status"::text, "public"."Catalog"."createdBy", "public"."Catalog"."createdAt", "public"."Catalog"."updatedAt", COALESCE("aggr_selection_0_Equipment"."_aggr_count_equipments", 0) AS "_aggr_count_equipments" FROM "public"."Catalog" LEFT JOIN (SELECT "public"."Equipment"."catalogId", COUNT(*) AS "_aggr_count_equipments" FROM "public"."Equipment" WHERE 1=1 GROUP BY "public"."Equipment"."catalogId") AS "aggr_selection_0_Equipment" ON ("public"."Catalog"."id" = "aggr_selection_0_Equipment"."catalogId") WHERE "public"."Catalog"."status" = CAST($1::text AS "public"."Status") ORDER BY "public"."Catalog"."createdAt" DESC LIMIT $2 OFFSET $3
prisma:query SELECT "public"."User"."id", "public"."User"."name", "public"."User"."email" FROM "public"."User" WHERE "public"."User"."id" IN ($1) OFFSET $2
 GET /api/catalogs?status=ACTIVE 200 in 3720ms
 ✓ Compiled /_error in 2.6s (2630 modules)
 GET /api/equipment?page=1&limit=10&sortBy=createdAt&sortOrder=desc 500 in 7499ms
 GET /bidding-documents 500 in 6436ms
 GET /bidding-documents 200 in 399ms
 GET /bidding-documents 200 in 159ms
 GET /bidding-documents 200 in 363ms
 ○ Compiling /_not-found ...
 ✓ Compiled /_not-found in 1464ms (2630 modules)
 GET /_next/static/css/app/layout.css?v=1757318307260 404 in 2119ms
 GET /_next/static/chunks/main-app.js?v=1757318307260 404 in 2003ms
 GET /_next/static/chunks/app/bidding-documents/page.js 404 in 1985ms
 GET /_next/static/chunks/app-pages-internals.js 404 in 1996ms
 GET /_next/static/chunks/app/layout.js 404 in 2014ms
 GET /bidding-documents 200 in 263ms
 GET /_next/static/css/app/layout.css?v=1757318311873 404 in 114ms
 GET /_next/static/chunks/app/bidding-documents/page.js 404 in 260ms
 GET /_next/static/chunks/app/layout.js 404 in 268ms
 GET /_next/static/chunks/app-pages-internals.js 404 in 277ms
 GET /_next/static/chunks/main-app.js?v=1757318311873 404 in 256ms
 GET /bidding-documents 200 in 217ms
 GET /_next/static/chunks/main-app.js?v=1757318321407 404 in 223ms
 GET /_next/static/chunks/app-pages-internals.js 404 in 230ms
 GET /_next/static/css/app/layout.css?v=1757318321407 404 in 246ms
 GET /_next/static/chunks/app/layout.js 404 in 222ms
 GET /_next/static/chunks/app/bidding-documents/page.js 404 in 265ms
 GET /bidding-documents 200 in 177ms
 GET /_next/static/chunks/app-pages-internals.js 404 in 210ms
 GET /_next/static/chunks/main-app.js?v=1757318323122 404 in 215ms
 GET /_next/static/css/app/layout.css?v=1757318323122 404 in 230ms
 GET /_next/static/chunks/app/layout.js 404 in 200ms
 GET /_next/static/chunks/app/bidding-documents/page.js 404 in 243ms
 GET /bidding-documents 200 in 186ms
 GET /_next/static/chunks/app-pages-internals.js 404 in 234ms
 GET /_next/static/chunks/main-app.js?v=1757318324178 404 in 239ms
 GET /_next/static/css/app/layout.css?v=1757318324178 404 in 250ms
 GET /_next/static/chunks/app/layout.js 404 in 231ms
 GET /_next/static/chunks/app/bidding-documents/page.js 404 in 237ms
 GET /bidding-documents 200 in 71ms
 GET /bidding-documents 200 in 179ms
 GET /_next/static/css/app/layout.css?v=1757318325072 404 in 223ms
 GET /_next/static/chunks/app/layout.js 404 in 232ms
 GET /_next/static/chunks/app-pages-internals.js 404 in 238ms
 GET /_next/static/chunks/main-app.js?v=1757318325072 404 in 249ms
 GET /_next/static/chunks/app/bidding-documents/page.js 404 in 226ms
 GET /bidding-documents 200 in 208ms
 GET /_next/static/css/app/layout.css?v=1757318551650 404 in 127ms
 GET /_next/static/chunks/main-app.js?v=1757318551650 404 in 207ms
 GET /_next/static/chunks/app/layout.js 404 in 214ms
 GET /_next/static/chunks/app-pages-internals.js 404 in 225ms
 GET /_next/static/chunks/app/bidding-documents/page.js 404 in 201ms
 GET /bidding-documents 200 in 316ms
 GET /_next/static/chunks/app/layout.js 404 in 274ms
 GET /_next/static/chunks/main-app.js?v=1757318602519 404 in 287ms
 GET /_next/static/chunks/app-pages-internals.js 404 in 282ms
 GET /_next/static/css/app/layout.css?v=1757318602519 404 in 331ms
 GET /_next/static/chunks/app/bidding-documents/page.js 404 in 269ms
 ○ Compiling / ...
 ✓ Compiled / in 868ms (1231 modules)
 GET / 200 in 1491ms
 GET /_next/static/chunks/app-pages-internals.js 404 in 309ms
 GET /_next/static/chunks/main-app.js?v=1757318606753 404 in 338ms
 GET /_next/static/css/app/layout.css?v=1757318606753 404 in 309ms
 GET /_next/static/chunks/app/layout.js 404 in 374ms
 GET /_next/static/chunks/app/page.js 404 in 382ms
 ○ Compiling /_error ...
 ✓ Compiled /_error in 986ms (2462 modules)
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/d/WorkSpace/viet-my-medical/.next/server/pages/_document.js'] {
  errno: -2,
  syscall: 'open',
  code: 'ENOENT',
  path: '/mnt/d/WorkSpace/viet-my-medical/.next/server/pages/_document.js'
}
 GET / 500 in 5127ms
   Reload env: .env.local
 ○ Compiling /_not-found ...
 ✓ Compiled /_not-found in 2.9s (2703 modules)
 ⨯ Error: ENOENT: no such file or directory, open '/mnt/d/WorkSpace/viet-my-medical/.next/server/vendor-chunks/next.js'
    at <unknown> (.next/server/app/_not-found/page.js:375:47)
    at Object.<anonymous> (.next/server/app/_not-found/page.js:378:3) {
  errno: -2,
  syscall: 'open',
  code: 'ENOENT',
  path: '/mnt/d/WorkSpace/viet-my-medical/.next/server/vendor-chunks/next.js'
}
 GET /_next/static/webpack/171866d2b466f2a2.webpack.hot-update.json 500 in 2046ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ○ Compiling / ...
 ✓ Compiled / in 739ms (1317 modules)
 ⨯ Error: ENOENT: no such file or directory, open '/mnt/d/WorkSpace/viet-my-medical/.next/server/vendor-chunks/next.js'
    at <unknown> (.next/server/app/page.js:429:47)
    at Object.<anonymous> (.next/server/app/page.js:432:3) {
  errno: -2,
  syscall: 'open',
  code: 'ENOENT',
  path: '/mnt/d/WorkSpace/viet-my-medical/.next/server/vendor-chunks/next.js',
  page: '/'
}
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/d/WorkSpace/viet-my-medical/.next/server/pages/_document.js'] {
  errno: -2,
  syscall: 'open',
  code: 'ENOENT',
  path: '/mnt/d/WorkSpace/viet-my-medical/.next/server/pages/_document.js'
}
 GET / 500 in 1318ms
 ⨯ Error: ENOENT: no such file or directory, open '/mnt/d/WorkSpace/viet-my-medical/.next/server/vendor-chunks/next.js'
    at <unknown> (.next/server/app/_not-found/page.js:375:47)
    at Object.<anonymous> (.next/server/app/_not-found/page.js:378:3) {
  errno: -2,
  syscall: 'open',
  code: 'ENOENT',
  path: '/mnt/d/WorkSpace/viet-my-medical/.next/server/vendor-chunks/next.js'
}
 ⨯ Error: ENOENT: no such file or directory, open '/mnt/d/WorkSpace/viet-my-medical/.next/server/vendor-chunks/next.js'
    at <unknown> (.next/server/app/_not-found/page.js:375:47)
    at Object.<anonymous> (.next/server/app/_not-found/page.js:378:3) {
  errno: -2,
  syscall: 'open',
  code: 'ENOENT',
  path: '/mnt/d/WorkSpace/viet-my-medical/.next/server/vendor-chunks/next.js'
}
 ⨯ Error: ENOENT: no such file or directory, open '/mnt/d/WorkSpace/viet-my-medical/.next/server/vendor-chunks/next.js'
    at <unknown> (.next/server/app/_not-found/page.js:375:47)
    at Object.<anonymous> (.next/server/app/_not-found/page.js:378:3) {
  errno: -2,
  syscall: 'open',
  code: 'ENOENT',
  path: '/mnt/d/WorkSpace/viet-my-medical/.next/server/vendor-chunks/next.js'
}
 GET /_next/static/chunks/fallback/react-refresh.js 500 in 307ms
 GET /_next/static/chunks/fallback/webpack.js 500 in 290ms
 GET /_next/static/chunks/fallback/main.js 500 in 278ms
 ⨯ Error: ENOENT: no such file or directory, open '/mnt/d/WorkSpace/viet-my-medical/.next/server/vendor-chunks/next.js'
    at <unknown> (.next/server/app/_not-found/page.js:375:47)
    at Object.<anonymous> (.next/server/app/_not-found/page.js:378:3) {
  errno: -2,
  syscall: 'open',
  code: 'ENOENT',
  path: '/mnt/d/WorkSpace/viet-my-medical/.next/server/vendor-chunks/next.js'
}
 ⨯ Error: ENOENT: no such file or directory, open '/mnt/d/WorkSpace/viet-my-medical/.next/server/vendor-chunks/next.js'
    at <unknown> (.next/server/app/_not-found/page.js:375:47)
    at Object.<anonymous> (.next/server/app/_not-found/page.js:378:3) {
  errno: -2,
  syscall: 'open',
  code: 'ENOENT',
  path: '/mnt/d/WorkSpace/viet-my-medical/.next/server/vendor-chunks/next.js'
}
 GET /_next/static/chunks/fallback/pages/_app.js 500 in 153ms
 GET /_next/static/chunks/fallback/pages/_error.js 500 in 151ms
   Reload env: .env.local
 ✓ Compiled in 2s (2708 modules)
 ✓ Compiled in 3.3s (2476 modules)
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/mnt/d/WorkSpace/viet-my-medical/.next/cache/webpack/client-development/0.pack.gz_' -> '/mnt/d/WorkSpace/viet-my-medical/.next/cache/webpack/client-development/0.pack.gz'
 ✓ Compiled in 1784ms (1155 modules)
 ✓ Compiled in 1495ms (1155 modules)
 ✓ Compiled in 1695ms (1155 modules)
 ✓ Compiled in 1718ms (1155 modules)
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/mnt/d/WorkSpace/viet-my-medical/.next/cache/webpack/client-development/0.pack.gz_' -> '/mnt/d/WorkSpace/viet-my-medical/.next/cache/webpack/client-development/0.pack.gz'
