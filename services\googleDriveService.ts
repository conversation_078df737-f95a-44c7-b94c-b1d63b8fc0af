import { google } from 'googleapis';
import { cookies } from 'next/headers';

export class GoogleDriveService {
  private static instance: GoogleDriveService;

  private constructor() {}

  static getInstance(): GoogleDriveService {
    if (!GoogleDriveService.instance) {
      GoogleDriveService.instance = new GoogleDriveService();
    }
    return GoogleDriveService.instance;
  }

  async getOAuth2Client() {
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_REDIRECT_URI
    );

    // Try to get tokens from cookies
    const cookieStore = await cookies();
    const accessToken = cookieStore.get('google_access_token')?.value;
    const refreshToken = cookieStore.get('google_refresh_token')?.value;

    if (accessToken) {
      oauth2Client.setCredentials({
        access_token: accessToken,
        refresh_token: refreshToken
      });
    }

    return oauth2Client;
  }

  async getDriveClient() {
    const auth = await this.getOAuth2Client();
    return google.drive({ version: 'v3', auth });
  }

  getAuthUrl(state?: string): string {
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_REDIRECT_URI
    );

    return oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: [
        'https://www.googleapis.com/auth/drive.file',
        'https://www.googleapis.com/auth/drive.readonly'
      ],
      state: state || '/equipment'
    });
  }

  async isAuthenticated(): Promise<boolean> {
    try {
      const cookieStore = await cookies();
      const accessToken = cookieStore.get('google_access_token')?.value;
      return !!accessToken;
    } catch {
      return false;
    }
  }

  async revokeAccess() {
    const cookieStore = await cookies();
    cookieStore.delete('google_access_token');
    cookieStore.delete('google_refresh_token');
  }
}

export const googleDriveService = GoogleDriveService.getInstance();