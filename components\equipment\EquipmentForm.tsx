'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useCreateEquipment, useUpdateEquipment } from '@/hooks/queries/useEquipment';
import { useQueryClient } from '@tanstack/react-query';
import { useCatalogs } from '@/hooks/queries/useCatalogs';
import { useToast } from '@/hooks/useToast';
import { Button } from '@/components/ui/Button';
import { Select } from '@/components/ui/Select';
import { GoogleDriveAttachments } from './GoogleDriveAttachments';
import { equipmentService } from '@/services/equipmentService';
import { Package, Settings, FileText, Plus, Trash2, AlertCircle } from 'lucide-react';
import type { Equipment, CreateEquipmentDto, UpdateEquipmentDto } from '@/types/equipment';

interface EquipmentFormProps {
  equipment?: Equipment;
  onSuccess?: () => void;
}

export function EquipmentForm({ equipment, onSuccess }: EquipmentFormProps) {
  const router = useRouter();
  const toast = useToast();
  const queryClient = useQueryClient();
  const isEdit = !!equipment;

  const [formData, setFormData] = useState({
    equipmentCode: equipment?.equipmentCode || '',
    name: equipment?.name || '',
    description: equipment?.description || '',
    model: equipment?.model || '',
    manufacturer: equipment?.manufacturer || '',
    unit: equipment?.unit || '',
    catalogId: equipment?.catalogId || '',
    status: equipment?.status || 'ACTIVE' as 'ACTIVE' | 'INACTIVE',
    price: equipment?.price || 0,
    specifications: equipment?.specifications || {}
  });

  const [specificationFields, setSpecificationFields] = useState<{ specifications: string; sourceReference: string }[]>(() => {
    const specs = equipment?.specifications as Record<string, any> || {};
    // Convert old format to new format if needed
    if (specs.items && Array.isArray(specs.items)) {
      return specs.items;
    } else {
      // Convert old key-value format to new combined format
      const oldEntries = Object.entries(specs).filter(([key]) => key !== 'items');
      if (oldEntries.length > 0) {
        const combined = oldEntries.map(([key, value]) => `${key}: ${value}`).join(', ');
        return [{ specifications: combined, sourceReference: '' }];
      }
    }
    return [{ specifications: '', sourceReference: '' }];
  });
  
  const [documents, setDocuments] = useState(equipment?.documents || []);
  const [deletedDocumentIds, setDeletedDocumentIds] = useState<string[]>([]);

  const { data: catalogsData } = useCatalogs({ status: 'ACTIVE' });
  const catalogs = catalogsData?.catalogs || [];

  const createMutation = useCreateEquipment();
  const updateMutation = useUpdateEquipment();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Convert specification fields to object with new format
      const specifications: Record<string, any> = {
        items: specificationFields.filter(field => field.specifications && field.specifications.trim())
      };

      if (isEdit) {
        const updateData: UpdateEquipmentDto = {
          equipmentCode: formData.equipmentCode,
          name: formData.name,
          description: formData.description,
          model: formData.model,
          manufacturer: formData.manufacturer,
          unit: formData.unit,
          catalogId: formData.catalogId,
          status: formData.status,
          price: formData.price,
          specifications
        };
        
        await updateMutation.mutateAsync({
          id: equipment.id,
          data: updateData
        });
        
        // Delete documents that were marked for deletion
        if (deletedDocumentIds.length > 0) {
          for (const docId of deletedDocumentIds) {
            try {
              await equipmentService.deleteDocument(equipment.id, docId);
            } catch (error) {
              console.error('Error deleting document:', error);
              toast.error('Error deleting document');
            }
          }
          toast.success('Documents deleted successfully');
        }
        
        // Save new documents if any were added
        const newDocuments = documents.filter(doc => doc.id.startsWith('temp-'));
        if (newDocuments.length > 0) {
          try {
            await equipmentService.saveDocuments(equipment.id, newDocuments);
            toast.success('New documents saved successfully');
          } catch (error) {
            console.error('Error saving documents:', error);
            toast.error('Error saving documents. Please try again.');
          }
        }
        
        // Invalidate equipment query to refresh data
        await queryClient.invalidateQueries({ queryKey: ['equipment', equipment.id] });
      } else {
        const createData: CreateEquipmentDto = {
          equipmentCode: formData.equipmentCode,
          name: formData.name,
          description: formData.description,
          model: formData.model,
          manufacturer: formData.manufacturer,
          unit: formData.unit,
          catalogId: formData.catalogId,
          status: formData.status,
          price: formData.price,
          specifications
        };

        const newEquipment = await createMutation.mutateAsync(createData);
        
        // Save documents if any were selected
        if (newEquipment && documents.length > 0) {
          try {
            await equipmentService.saveDocuments(newEquipment.id, documents);
            toast.success('Documents saved successfully');
            // Invalidate equipment query to refresh data
            await queryClient.invalidateQueries({ queryKey: ['equipment', newEquipment.id] });
          } catch (error) {
            console.error('Error saving documents:', error);
            toast.error('Error saving documents. Please try again.');
          }
        }
      }

      if (onSuccess) {
        onSuccess();
      } else {
        router.push('/equipment');
      }
    } catch (error) {
      console.error('Failed to save equipment:', error);
    }
  };

  const handleAddSpecificationField = () => {
    setSpecificationFields([...specificationFields, { specifications: '', sourceReference: '' }]);
  };

  const handleRemoveSpecificationField = (index: number) => {
    setSpecificationFields(specificationFields.filter((_, i) => i !== index));
  };

  const handleSpecificationChange = (index: number, field: 'specifications' | 'sourceReference', value: string) => {
    const newFields = [...specificationFields];
    newFields[index][field] = value.trim();
    setSpecificationFields(newFields);
  };
  
  const handleDocumentsChange = (updatedDocuments: any[]) => {
    setDocuments(updatedDocuments);
  };
  
  const handleDeletedDocumentsChange = (deletedIds: string[]) => {
    setDeletedDocumentIds(deletedIds);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* Basic Information Section */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-lg">
              <Package className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Basic Information</h2>
              <p className="text-sm text-gray-600">Main equipment information</p>
            </div>
          </div>
        </div>
        <div className="p-6">
        
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label className="flex items-center text-sm font-medium text-gray-700">
                <AlertCircle className="w-4 h-4 text-red-500 mr-1" />
                Equipment Code <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="text"
                value={formData.equipmentCode}
                onChange={(e) => setFormData({ ...formData, equipmentCode: e.target.value.trim() })}
                className="w-full h-11 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white"
                placeholder="Enter equipment code"
                required
              />
            </div>

            <div className="space-y-2">
              <label className="flex items-center text-sm font-medium text-gray-700">
                <AlertCircle className="w-4 h-4 text-red-500 mr-1" />
                Equipment Name <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value.trim() })}
                className="w-full h-11 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white"
                placeholder="Enter equipment name"
                required
              />
            </div>

            <div className="space-y-2">
              <label className="flex items-center text-sm font-medium text-gray-700">
                <AlertCircle className="w-4 h-4 text-red-500 mr-1" />
                Category <span className="text-red-500 ml-1">*</span>
              </label>
              <Select
                value={formData.catalogId}
                onChange={(e) => setFormData({ ...formData, catalogId: e.target.value })}
                className="w-full h-11 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white"
                required
              >
                <option value="">Select category</option>
                {catalogs.map((catalog) => (
                  <option key={catalog.id} value={catalog.id}>
                    {catalog.catalogName}
                  </option>
                ))}
              </Select>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Model
              </label>
              <input
                type="text"
                value={formData.model}
                onChange={(e) => setFormData({ ...formData, model: e.target.value.trim() })}
                className="w-full h-11 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white"
                placeholder="Enter equipment model"
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Manufacturer
              </label>
              <input
                type="text"
                value={formData.manufacturer}
                onChange={(e) => setFormData({ ...formData, manufacturer: e.target.value.trim() })}
                className="w-full h-11 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white"
                placeholder="Enter manufacturer"
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Unit of Measure
              </label>
              <input
                type="text"
                value={formData.unit}
                onChange={(e) => setFormData({ ...formData, unit: e.target.value.trim() })}
                className="w-full h-11 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white"
                placeholder="E.g: Piece, Set, Kg..."
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Status
              </label>
              <Select
                value={formData.status}
                onChange={(e) => setFormData({ ...formData, status: e.target.value as 'ACTIVE' | 'INACTIVE' })}
                className="w-full h-11 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white"
              >
                <option value="ACTIVE">Active</option>
                <option value="INACTIVE">Inactive</option>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Price (VND)
              </label>
              <input
                type="number"
                value={formData.price}
                onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) || 0 })}
                className="w-full h-11 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white"
                placeholder="0"
                min="0"
                step="0.01"
              />
            </div>

            <div className="md:col-span-2 space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value.trim() })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-gray-50 focus:bg-white resize-none"
                rows={4}
                placeholder="Detailed equipment description..."
              />
            </div>
          </div>
        </div>
      </div>

      {/* Specifications Section */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-8 h-8 bg-purple-100 rounded-lg">
                <Settings className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-gray-900">Technical Specifications</h2>
                <p className="text-sm text-gray-600">Equipment specifications and source references</p>
              </div>
            </div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleAddSpecificationField}
              className="flex items-center gap-2 bg-white border-purple-200 text-purple-600 hover:bg-purple-50 hover:border-purple-300"
            >
              <Plus className="w-4 h-4" />
              Add Specification
            </Button>
          </div>
        </div>
        
        <div className="p-6">
          <div className="space-y-4">
            {specificationFields.map((field, index) => (
              <div key={index} className="space-y-3 p-4 border border-gray-200 rounded-lg">
                <div className="flex justify-between items-center">
                  <label className="text-sm font-medium text-gray-700">Specification Item #{index + 1}</label>
                  {specificationFields.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => handleRemoveSpecificationField(index)}
                      className="px-3 py-1 border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>
                
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Equipment Specifications
                  </label>
                  <input
                    type="text"
                    placeholder="E.g: Power: 1000W, Voltage: 220V, Frequency: 50Hz..."
                    value={field.specifications}
                    onChange={(e) => handleSpecificationChange(index, 'specifications', e.target.value)}
                    className="w-full h-11 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200 bg-gray-50 focus:bg-white"
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Source Reference
                  </label>
                  <input
                    type="text"
                    placeholder="E.g: Document A.pdf page 2, Catalog B page 15..."
                    value={field.sourceReference}
                    onChange={(e) => handleSpecificationChange(index, 'sourceReference', e.target.value)}
                    className="w-full h-11 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200 bg-gray-50 focus:bg-white"
                  />
                </div>
              </div>
            ))}
            
            {specificationFields.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Settings className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                <p>No technical specifications yet</p>
                <p className="text-sm">Click "Add Specification" to start</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Documents Section */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-8 h-8 bg-green-100 rounded-lg">
              <FileText className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Attached Documents</h2>
              <p className="text-sm text-gray-600">
                {isEdit ? 'Manage documents and attachments' : 'Select documents to upload after creating equipment'}
              </p>
            </div>
          </div>
        </div>
        
        <div className="p-6">
          <GoogleDriveAttachments
            equipmentId={equipment?.id}
            initialDocuments={documents}
            onDocumentsChange={handleDocumentsChange}
            onDeletedDocumentsChange={handleDeletedDocumentsChange}
            isEdit={true}
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
          <div className="text-sm text-gray-600">
            {isEdit ? 'Save changes to update equipment information' : 'Double-check the information before creating new equipment'}
          </div>
          <div className="flex gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/equipment')}
              className="px-6 py-3 border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={createMutation.isPending || updateMutation.isPending}
              className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {createMutation.isPending || updateMutation.isPending
                ? 'Processing...'
                : isEdit
                ? 'Update Equipment'
                : 'Create New Equipment'}
            </Button>
          </div>
        </div>
      </div>
    </form>
  );
}