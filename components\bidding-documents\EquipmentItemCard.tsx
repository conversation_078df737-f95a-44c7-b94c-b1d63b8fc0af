'use client'

import { useState } from 'react'
import { Package, Upload, FileText, Trash2, Eye } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { useRemoveEquipmentItem, useUploadTenderDocument } from '@/hooks/queries/useBiddingDocuments'
import { useToast } from '@/hooks/useToast'
import type { BiddingEquipmentItem } from '@/types/biddingDocument'

interface EquipmentItemCardProps {
  item: BiddingEquipmentItem
  biddingDocumentId: string
}

export function EquipmentItemCard({ item, biddingDocumentId }: EquipmentItemCardProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [pageRange, setPageRange] = useState({ from: '', to: '' })
  const toast = useToast()
  
  const removeMutation = useRemoveEquipmentItem()
  const uploadMutation = useUploadTenderDocument()

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file type
    const validTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
    if (!validTypes.includes(file.type)) {
      toast.error('Chỉ chấp nhận file PDF hoặc Word')
      return
    }

    setIsUploading(true)
    try {
      await uploadMutation.mutateAsync({
        biddingDocumentId,
        equipmentItemId: item.id,
        file,
        pageRange: pageRange.from && pageRange.to ? {
          from: parseInt(pageRange.from),
          to: parseInt(pageRange.to)
        } : undefined
      })
      
      // Reset form
      setPageRange({ from: '', to: '' })
      e.target.value = ''
    } catch (error) {
      // Error handled by mutation
    } finally {
      setIsUploading(false)
    }
  }

  const handleRemove = () => {
    if (window.confirm('Bạn có chắc chắn muốn xóa thiết bị này khỏi hồ sơ?')) {
      removeMutation.mutate({
        biddingDocumentId,
        equipmentItemId: item.id
      })
    }
  }

  return (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-start gap-3">
          <Package className="w-5 h-5 text-gray-400 mt-1" />
          <div>
            <h4 className="font-medium text-gray-900 dark:text-gray-100">
              {item.equipment.code} - {item.equipment.name}
            </h4>
            {item.pageRange && (
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Phạm vi trang: {item.pageRange.from} - {item.pageRange.to}
              </p>
            )}
          </div>
        </div>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={handleRemove}
          disabled={removeMutation.isPending}
          className="text-red-600 hover:text-red-700"
        >
          <Trash2 className="w-4 h-4" />
        </Button>
      </div>

      {/* Tender Documents */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Tài liệu yêu cầu mời thầu ({item.tenderDocuments?.length || 0})
          </h5>
        </div>

        {item.tenderDocuments?.map((doc) => (
          <div key={doc.id} className="flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-700 rounded">
            <FileText className="w-4 h-4 text-gray-400" />
            <div className="flex-1 min-w-0">
              <p className="text-sm text-gray-900 dark:text-gray-100 truncate">
                {doc.fileName}
              </p>
              {doc.pageRange && (
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Trang: {doc.pageRange.from} - {doc.pageRange.to}
                </p>
              )}
            </div>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => window.open(doc.fileUrl, '_blank')}
            >
              <Eye className="w-4 h-4" />
            </Button>
          </div>
        ))}

        {/* Upload Form */}
        <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-700 rounded">
          <div className="grid grid-cols-3 gap-2 mb-2">
            <input
              type="number"
              placeholder="Từ trang"
              value={pageRange.from}
              onChange={(e) => setPageRange(prev => ({ ...prev, from: e.target.value }))}
              className="col-span-1 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded"
            />
            <input
              type="number"
              placeholder="Đến trang"
              value={pageRange.to}
              onChange={(e) => setPageRange(prev => ({ ...prev, to: e.target.value }))}
              className="col-span-1 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded"
            />
            <label className="col-span-1">
              <input
                type="file"
                accept=".pdf,.doc,.docx"
                onChange={handleFileUpload}
                disabled={isUploading}
                className="hidden"
              />
              <Button
                as="span"
                size="sm"
                variant="outline"
                disabled={isUploading}
                className="w-full"
              >
                <Upload className="w-3 h-3 mr-1" />
                {isUploading ? 'Loading...' : 'Tải lên'}
              </Button>
            </label>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Chọn phạm vi trang cần AI xử lý (tùy chọn)
          </p>
        </div>
      </div>
    </div>
  )
}