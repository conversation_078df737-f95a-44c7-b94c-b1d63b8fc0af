import { BaseService } from './baseService'
import { DashboardStats } from '@/types/dashboard'

export interface RecentBiddingDocument {
  id: string
  code: string
  name: string
  title: string
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED'
  customerName: string
  createdAt: string
  updatedAt: string
  createdBy: string
  attachmentCount: number
  value: string
  endDate: string
}

class DashboardService extends BaseService {
  private static instance: DashboardService

  private constructor() {
    super({ baseUrl: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002' })
  }

  static getInstance(): DashboardService {
    if (!DashboardService.instance) {
      DashboardService.instance = new DashboardService()
    }
    return DashboardService.instance
  }

  async getDashboardStats(signal?: AbortSignal): Promise<DashboardStats> {
    const response = await this.request<{ data: DashboardStats }>(
      'GET',
      '/api/dashboard/stats',
      undefined,
      { signal }
    )
    return response.data
  }

  async getRecentBiddingDocuments(signal?: AbortSignal): Promise<RecentBiddingDocument[]> {
    const response = await this.request<{ data: RecentBiddingDocument[] }>(
      'GET',
      '/api/dashboard/recent-biddings',
      undefined,
      { signal }
    )
    return response.data
  }
}

export const dashboardService = DashboardService.getInstance()