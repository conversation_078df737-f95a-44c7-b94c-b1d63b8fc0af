import { BaseService } from './baseService'
import { DashboardStats } from '@/types/dashboard'

export interface RecentBiddingDocument {
  id: string
  code: string
  name: string
  title: string
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED'
  customerName: string
  createdAt: string
  updatedAt: string
  createdBy: string
  attachmentCount: number
  value: string
  endDate: string
}

class DashboardService extends BaseService {
  private static instance: DashboardService

  private constructor() {
    super({ useLocalApi: true })
  }

  static getInstance(): DashboardService {
    if (!DashboardService.instance) {
      DashboardService.instance = new DashboardService()
    }
    return DashboardService.instance
  }

  async getDashboardStats(signal?: AbortSignal): Promise<any> {
    // Return mock dashboard stats instead of making API calls
    // This prevents external API dependencies during development/testing
    return {
      totalBiddingDocuments: {
        value: 42,
        change: '+12.5%',
        trend: 'up' as const
      },
      activeBiddingDocuments: {
        value: 8,
        change: '+5.2%',
        trend: 'up' as const
      },
      totalEquipment: {
        value: 156,
        change: '-2.3%',
        trend: 'down' as const
      },
      totalUsers: {
        value: 23,
        change: '0%',
        trend: 'stable' as const
      }
    }
  }

  async getRecentBiddingDocuments(signal?: AbortSignal): Promise<RecentBiddingDocument[]> {
    // Return mock recent bidding documents instead of making API calls
    // This provides realistic test data without external dependencies
    return [
      {
        id: '1',
        code: 'BD-2024-001',
        name: 'Gói thầu mua sắm thiết bị y tế',
        title: 'Mua sắm máy X-quang kỹ thuật số',
        status: 'IN_PROGRESS',
        customerName: 'Bệnh viện Đa khoa Trung ương',
        createdAt: '2024-01-15T08:30:00Z',
        updatedAt: '2024-01-20T14:45:00Z',
        createdBy: 'admin',
        attachmentCount: 5,
        value: '2,500,000,000',
        endDate: '2024-02-15T17:00:00Z'
      },
      {
        id: '2',
        code: 'BD-2024-002',
        name: 'Gói thầu thiết bị phòng mổ',
        title: 'Cung cấp hệ thống phòng mổ hiện đại',
        status: 'PENDING',
        customerName: 'Bệnh viện Nhi Trung ương',
        createdAt: '2024-01-18T09:15:00Z',
        updatedAt: '2024-01-18T09:15:00Z',
        createdBy: 'user1',
        attachmentCount: 3,
        value: '5,200,000,000',
        endDate: '2024-02-28T17:00:00Z'
      },
      {
        id: '3',
        code: 'BD-2024-003',
        name: 'Gói thầu thiết bị xét nghiệm',
        title: 'Mua sắm máy xét nghiệm sinh hóa',
        status: 'COMPLETED',
        customerName: 'Bệnh viện Bạch Mai',
        createdAt: '2024-01-10T10:00:00Z',
        updatedAt: '2024-01-22T16:30:00Z',
        createdBy: 'admin',
        attachmentCount: 8,
        value: '1,800,000,000',
        endDate: '2024-01-20T17:00:00Z'
      },
      {
        id: '4',
        code: 'BD-2024-004',
        name: 'Gói thầu thiết bị chẩn đoán',
        title: 'Cung cấp máy MRI 3 Tesla',
        status: 'IN_PROGRESS',
        customerName: 'Bệnh viện Chợ Rẫy',
        createdAt: '2024-01-22T11:20:00Z',
        updatedAt: '2024-01-25T10:15:00Z',
        createdBy: 'user2',
        attachmentCount: 6,
        value: '12,500,000,000',
        endDate: '2024-03-15T17:00:00Z'
      },
      {
        id: '5',
        code: 'BD-2024-005',
        name: 'Gói thầu thiết bị hồi sức',
        title: 'Mua sắm máy thở cao cấp',
        status: 'PENDING',
        customerName: 'Bệnh viện E',
        createdAt: '2024-01-24T14:00:00Z',
        updatedAt: '2024-01-24T14:00:00Z',
        createdBy: 'user3',
        attachmentCount: 2,
        value: '950,000,000',
        endDate: '2024-02-10T17:00:00Z'
      }
    ]
  }
}

export const dashboardService = DashboardService.getInstance()