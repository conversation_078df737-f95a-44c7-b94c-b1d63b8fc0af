import { BaseService } from './baseService'
import { DashboardStats } from '@/types/dashboard'

export interface RecentBiddingDocument {
  id: string
  code: string
  name: string
  title: string
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED'
  customerName: string
  createdAt: string
  updatedAt: string
  createdBy: string
  attachmentCount: number
  value: string
  endDate: string
}

export interface DashboardActivity {
  id: string
  type: 'BIDDING_CREATED' | 'BIDDING_UPDATED' | 'BIDDING_COMPLETED' | 'EQUIPMENT_CREATED'
  title: string
  description: string
  timestamp: string
  icon: string
  documentId?: string | null
  documentCode?: string | null
  documentName?: string | null
  customerName?: string | null
  status?: string | null
  user: {
    id: string
    name: string
    username: string
    email: string
  }
  attachmentCount: number
}

export interface BiddingDocumentOverview {
  id: string
  code: string
  name: string
  description: string | null
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED'
  customerName: string | null
  createdAt: string
  updatedAt: string
  creator: {
    id: string
    name: string
    username: string
    email: string
  }
  attachmentCount: number
  equipmentItemsCount: number
  progress: number
  priority: 'low' | 'medium' | 'high'
  daysSinceCreated: number
  estimatedCompletion: string
  isOverdue: boolean
  tags: string[]
}

export interface DashboardAnalytics {
  biddingTrend: Array<{
    period: string
    total: number
    completed: number
    inProgress: number
    pending: number
  }>
  successRate: {
    percentage: number
    total: number
    completed: number
    distribution: Array<{
      status: string
      count: number
      percentage: number
    }>
  }
  customerDistribution: Array<{
    name: string
    count: number
    percentage: number
  }>
  equipmentCategories: Array<{
    name: string
    code: string
    count: number
    description: string | null
  }>
  userActivity: Array<{
    id: string
    name: string
    username: string
    documentsCount: number
    role: string
  }>
  monthlyGrowth: Array<{
    month: string
    count: number
    previousCount: number
    growthPercentage: number
  }>
  performanceMetrics: {
    avgCompletionDays: number
    totalDocuments: number
    activeDocuments: number
    completionRate: number
  }
  dateRange: {
    from: string
    to: string
    period: string
  }
  generatedAt: string
}

class DashboardService extends BaseService {
  private static instance: DashboardService

  private constructor() {
    // Use local API client to connect to Next.js API routes
    super({ useLocalApi: true })
  }

  static getInstance(): DashboardService {
    if (!DashboardService.instance) {
      DashboardService.instance = new DashboardService()
    }
    return DashboardService.instance
  }

  async getDashboardStats(signal?: AbortSignal): Promise<any> {
    const response = await this.get<any>('/dashboard/stats', signal)
    return response
  }

  async getRecentBiddingDocuments(signal?: AbortSignal): Promise<RecentBiddingDocument[]> {
    const response = await this.get<{ success: boolean; data: RecentBiddingDocument[] }>('/dashboard/recent-biddings', signal)
    return response.data
  }

  async getDashboardActivities(
    page = 1, 
    limit = 20, 
    options: {
      type?: 'created' | 'updated' | 'completed'
      dateFrom?: string
      dateTo?: string
    } = {},
    signal?: AbortSignal
  ): Promise<{ activities: DashboardActivity[]; pagination: any }> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...options
    })
    
    const response = await this.get<{ activities: DashboardActivity[]; pagination: any }>(`/dashboard/activities?${params}`, signal)
    return response
  }

  async getBiddingDocumentsOverview(
    page = 1,
    limit = 10,
    options: {
      status?: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED'
      customerName?: string
      search?: string
      urgent?: boolean
      sortBy?: string
      sortOrder?: 'asc' | 'desc'
      dateFrom?: string
      dateTo?: string
    } = {},
    signal?: AbortSignal
  ): Promise<{ documents: BiddingDocumentOverview[]; pagination: any; summary: any }> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...Object.fromEntries(
        Object.entries(options).filter(([_, value]) => value !== undefined)
      )
    })
    
    const response = await this.get<{ documents: BiddingDocumentOverview[]; pagination: any; summary: any }>(`/dashboard/bidding-documents?${params}`, signal)
    return response
  }

  async getDashboardAnalytics(
    options: {
      dateFrom?: string
      dateTo?: string
      period?: 'week' | 'month' | 'quarter' | 'year'
    } = {},
    signal?: AbortSignal
  ): Promise<DashboardAnalytics> {
    const params = new URLSearchParams(
      Object.fromEntries(
        Object.entries(options).filter(([_, value]) => value !== undefined)
      )
    )
    
    const queryString = params.toString()
    const endpoint = queryString ? `/dashboard/analytics?${queryString}` : '/dashboard/analytics'
    
    const response = await this.get<DashboardAnalytics>(endpoint, signal)
    return response
  }

}

export const dashboardService = DashboardService.getInstance()