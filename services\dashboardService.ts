import { BaseService } from './baseService'
import { DashboardStats } from '@/types/dashboard'

export interface RecentBiddingDocument {
  id: string
  code: string
  name: string
  title: string
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED'
  customerName: string
  createdAt: string
  updatedAt: string
  createdBy: string
  attachmentCount: number
  value: string
  endDate: string
}

class DashboardService extends BaseService {
  private static instance: DashboardService

  private constructor() {
    super({ useLocalApi: true })
  }

  static getInstance(): DashboardService {
    if (!DashboardService.instance) {
      DashboardService.instance = new DashboardService()
    }
    return DashboardService.instance
  }

  async getDashboardStats(signal?: AbortSignal): Promise<DashboardStats> {
    const response = await this.get<{ success: boolean; data: DashboardStats }>(
      '/api/dashboard/stats',
      signal
    )
    return response.data
  }

  async getRecentBiddingDocuments(signal?: AbortSignal): Promise<RecentBiddingDocument[]> {
    const response = await this.get<{ success: boolean; data: RecentBiddingDocument[] }>(
      '/api/dashboard/recent-biddings',
      signal
    )
    return response.data
  }
}

export const dashboardService = DashboardService.getInstance()