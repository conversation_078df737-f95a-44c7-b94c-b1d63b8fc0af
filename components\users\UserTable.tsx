'use client'

import React, { useState } from 'react'
import { Edit, Eye, User as UserIcon, Mail, Phone, Shield, Key, ToggleLeft, ToggleRight } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Dialog } from '@/components/ui/Dialog'
import type { User } from '@/types/user'
import { useResetPassword, useToggleUserStatus } from '@/hooks/queries/useUsers'
import { useAuth } from '@/contexts/AuthContext'

interface UserTableProps {
  users: User[]
  isLoading: boolean
  onEdit: (user: User) => void
}

const roleLabels = {
  ADMIN: 'Quản trị viên',
  USER: 'Người dùng',
}

const statusLabels = {
  ACTIVE: 'Hoạt động',
  INACTIVE: 'Không hoạt động',
}

export function UserTable({ users, isLoading, onEdit }: UserTableProps) {
  const { user: currentUser } = useAuth()
  const resetPasswordMutation = useResetPassword()
  const toggleStatusMutation = useToggleUserStatus()
  
  const [deactivateDialogOpen, setDeactivateDialogOpen] = useState(false)
  const [userToDeactivate, setUserToDeactivate] = useState<User | null>(null)

  const handleResetPassword = async (user: User) => {
    if (confirm(`Bạn có chắc chắn muốn đặt lại mật khẩu cho ${user.name} thành "admin123"?`)) {
      await resetPasswordMutation.mutateAsync(user.id)
    }
  }

  const handleToggleStatus = async (user: User) => {
    if (user.id === currentUser?.id) {
      return
    }
    
    // If deactivating, show confirmation dialog
    if (user.status === 'ACTIVE') {
      setUserToDeactivate(user)
      setDeactivateDialogOpen(true)
    } else {
      // If activating, proceed directly
      await toggleStatusMutation.mutateAsync(user.id)
    }
  }
  
  const confirmDeactivation = async () => {
    if (!userToDeactivate) return
    
    try {
      await toggleStatusMutation.mutateAsync(userToDeactivate.id)
      setDeactivateDialogOpen(false)
      setUserToDeactivate(null)
    } catch (error) {
      console.error('Failed to deactivate user:', error)
    }
  }
  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="animate-pulse">
          <div className="h-14 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800"></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="border-t border-gray-200 dark:border-gray-700">
              <div className="h-20 bg-gray-50 dark:bg-gray-800 p-6">
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded-md w-3/4"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded-md w-1/2"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (users.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="text-center py-16">
          <div className="mx-auto w-20 h-20 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-full flex items-center justify-center mb-6">
            <UserIcon className="w-10 h-10 text-blue-500 dark:text-blue-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Chưa có người dùng nào
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 max-w-sm mx-auto">
            Thêm người dùng đầu tiên để bắt đầu quản lý tài khoản
          </p>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800">
            <tr>
              <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                Người dùng
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                Vai trò
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                Phòng ban
              </th>
              <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                Trạng thái
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                Xác thực
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                Đăng nhập cuối
              </th>
              <th className="px-6 py-4 text-right text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                Thao tác
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-100 dark:divide-gray-700">
            {users.map((user, index) => (
              <tr 
                key={user.id} 
                className={`hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/10 dark:hover:to-indigo-900/10 transition-all duration-200 group ${
                  index % 2 === 0 ? 'bg-white dark:bg-gray-800' : 'bg-gray-50/50 dark:bg-gray-800/50'
                }`}
              >
                <td className="px-6 py-5 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      <div className="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                        <UserIcon className="h-6 w-6 text-gray-600 dark:text-gray-300" />
                      </div>
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {user.name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        @{user.username}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center mt-1">
                        <Mail className="h-3 w-3 mr-1" />
                        {user.email}
                      </div>
                      {user.phone && (
                        <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center mt-1">
                          <Phone className="h-3 w-3 mr-1" />
                          {user.phone}
                        </div>
                      )}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-5 whitespace-nowrap">
                  <div className="flex items-center">
                    <Shield className={`h-4 w-4 mr-2 ${
                      user.role === 'ADMIN' ? 'text-red-500' : 'text-blue-500'
                    }`} />
                    <span className="text-sm text-gray-900 dark:text-gray-100">
                      {roleLabels[user.role]}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-5 whitespace-nowrap">
                  <span className="text-sm text-gray-900 dark:text-gray-100">
                    {user.department || '-'}
                  </span>
                </td>
                <td className="px-6 py-5 whitespace-nowrap text-center">
                  <span className={`inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-semibold rounded-full border ${
                    user.status === 'ACTIVE'
                      ? 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200 dark:from-green-900/40 dark:to-emerald-900/40 dark:text-green-200 dark:border-green-700'
                      : 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 border-gray-200 dark:from-gray-800/40 dark:to-gray-700/40 dark:text-gray-300 dark:border-gray-600'
                  }`}>
                    <div className={`w-2 h-2 rounded-full ${
                      user.status === 'ACTIVE' ? 'bg-green-500' : 'bg-gray-400'
                    }`} />
                    {statusLabels[user.status]}
                  </span>
                </td>
                <td className="px-6 py-5 whitespace-nowrap">
                  <div className="flex flex-col space-y-1">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.emailVerified
                        ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                        : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                    }`}>
                      Email: {user.emailVerified ? 'Đã xác thực' : 'Chưa xác thực'}
                    </span>
                    {user.twoFactorEnabled && (
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                        2FA: Bật
                      </span>
                    )}
                  </div>
                </td>
                <td className="px-6 py-5 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {user.lastLoginAt ? 
                    new Date(user.lastLoginAt).toLocaleDateString('vi-VN', {
                      day: '2-digit',
                      month: '2-digit',
                      year: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    }) : '-'
                  }
                </td>
                <td className="px-6 py-5 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex items-center justify-end gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => onEdit(user)}
                      title="Chỉnh sửa"
                      className="w-8 h-8 hover:bg-amber-100 hover:text-amber-600 dark:hover:bg-amber-900/20 dark:hover:text-amber-400 transition-colors"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleResetPassword(user)}
                      title="Đặt lại mật khẩu"
                      disabled={resetPasswordMutation.isPending}
                      className="w-8 h-8 hover:bg-purple-100 hover:text-purple-600 dark:hover:bg-purple-900/20 dark:hover:text-purple-400 transition-colors"
                    >
                      <Key className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleToggleStatus(user)}
                      title={user.status === 'ACTIVE' ? 'Vô hiệu hóa' : 'Kích hoạt'}
                      disabled={user.id === currentUser?.id || toggleStatusMutation.isPending}
                      className={`w-8 h-8 transition-colors ${
                        user.status === 'ACTIVE' 
                          ? 'hover:bg-orange-100 hover:text-orange-600 dark:hover:bg-orange-900/20 dark:hover:text-orange-400'
                          : 'hover:bg-green-100 hover:text-green-600 dark:hover:bg-green-900/20 dark:hover:text-green-400'
                      }`}
                    >
                      {user.status === 'ACTIVE' ? <ToggleRight className="h-4 w-4" /> : <ToggleLeft className="h-4 w-4" />}
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
    
    {/* Deactivation Confirmation Dialog */}
    <Dialog
      open={deactivateDialogOpen}
      onClose={() => {
        setDeactivateDialogOpen(false)
        setUserToDeactivate(null)
      }}
      title="Xác nhận vô hiệu hóa người dùng"
      description={
        userToDeactivate ? 
        `Người dùng ${userToDeactivate.name} sẽ mất quyền truy cập vào hệ thống và không thể đăng nhập cho đến khi được kích hoạt lại. Bạn có chắc chắn muốn tiếp tục?` :
        ''
      }
    >
      <div className="flex justify-end gap-3 mt-6">
        <Button
          variant="outline"
          onClick={() => {
            setDeactivateDialogOpen(false)
            setUserToDeactivate(null)
          }}
        >
          Hủy
        </Button>
        <Button
          variant="primary"
          onClick={confirmDeactivation}
          disabled={toggleStatusMutation.isPending}
          className="bg-red-600 hover:bg-red-700 text-white"
        >
          {toggleStatusMutation.isPending ? 'Đang xử lý...' : 'Vô hiệu hóa'}
        </Button>
      </div>
    </Dialog>
    </>
  )
}