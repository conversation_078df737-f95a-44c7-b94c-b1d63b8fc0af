import { http, HttpResponse } from 'msw'
import type { LoginRequest, LoginResponse, User } from '@/types/auth'

const mockUser: User = {
  id: 1,
  email: '<EMAIL>',
  name: 'Admin User',
  role: 'ADMIN',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
}

export const handlers = [
  // Auth handlers
  http.post('/api/auth/login', async ({ request }) => {
    const body = await request.json() as LoginRequest
    
    if (body.email === '<EMAIL>' && body.password === 'password123') {
      const response: LoginResponse = {
        token: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token',
        user: mockUser,
      }
      return HttpResponse.json(response)
    }
    
    return HttpResponse.json(
      { error: 'Invalid credentials' },
      { status: 401 }
    )
  }),

  http.post('/api/auth/logout', () => {
    return new HttpResponse(null, { status: 200 })
  }),

  http.get('/api/auth/session', () => {
    return HttpResponse.json({
      user: mockUser,
      session: {
        id: 'session-123',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date().toISOString(),
      },
    })
  }),

  http.post('/api/auth/refresh', () => {
    const response: LoginResponse = {
      token: 'new-mock-jwt-token',
      refreshToken: 'new-mock-refresh-token',
      user: mockUser,
    }
    return HttpResponse.json(response)
  }),

  // Dashboard handlers
  http.get('/api/dashboard/metrics', () => {
    return HttpResponse.json({
      totalProjects: 45,
      activeProjects: 12,
      totalBudget: **********,
      completionRate: 78.5,
      trends: {
        projects: [10, 12, 15, 14, 16, 18, 20],
        budget: [1000, 1200, 1100, 1400, 1600, 1500, 1800],
      },
    })
  }),

  // Bidding handlers
  http.get('/api/biddings', ({ request }) => {
    const url = new URL(request.url)
    const page = url.searchParams.get('page') || '1'
    const limit = url.searchParams.get('limit') || '10'
    
    return HttpResponse.json({
      biddings: [
        {
          id: 1,
          projectName: 'Medical Equipment Procurement',
          status: 'OPEN',
          budget: 500000000,
          deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          contractors: 5,
        },
        {
          id: 2,
          projectName: 'Hospital Construction Phase 1',
          status: 'IN_PROGRESS',
          budget: **********,
          deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          contractors: 8,
        },
      ],
      total: 2,
      page: parseInt(page),
      totalPages: 1,
    })
  }),

  http.post('/api/biddings', async ({ request }) => {
    const body = await request.json()
    
    return HttpResponse.json({
      id: 3,
      ...body,
      status: 'OPEN',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    })
  }),

  http.put('/api/biddings/:id', async ({ params, request }) => {
    const { id } = params
    const body = await request.json()
    
    return HttpResponse.json({
      id: Number(id),
      ...body,
      updatedAt: new Date().toISOString(),
    })
  }),

  http.delete('/api/biddings/:id', ({ params }) => {
    const { id } = params
    return new HttpResponse(null, { status: 204 })
  }),
]