#!/usr/bin/env node

const http = require('http');
const fs = require('fs');
const path = require('path');

const BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002';

async function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsed
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (e) => {
      reject(e);
    });

    if (data) {
      req.write(data);
    }
    
    req.end();
  });
}

async function testBiddingDocumentAPI() {
  console.log('🧪 Testing Bidding Document API...');
  console.log('📍 Base URL:', BASE_URL);
  
  // Test 1: Create bidding document without authentication (should fail)
  console.log('\n1️⃣ Testing bidding document creation without auth...');
  try {
    const testData = {
      code: 'TEST-' + Date.now(),
      name: 'Test Bidding Document',
      description: 'Test description',
      customerName: 'Test Customer',
    };

    const options = {
      hostname: 'localhost',
      port: 3002,
      path: '/api/bidding-documents',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const response = await makeRequest(options, JSON.stringify(testData));
    console.log('Status:', response.statusCode);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    if (response.statusCode === 401) {
      console.log('✅ Authentication properly required');
    } else {
      console.log('❌ Expected 401 Unauthorized');
    }
  } catch (error) {
    console.log('❌ Request failed:', error.message);
  }

  // Test 2: Test server information endpoint
  console.log('\n2️⃣ Testing server response headers...');
  try {
    const options = {
      hostname: 'localhost',
      port: 3002,
      path: '/api/bidding-documents',
      method: 'GET',
      headers: {
        'User-Agent': 'bidding-document-test/1.0',
      },
    };

    const response = await makeRequest(options);
    console.log('Status:', response.statusCode);
    console.log('Headers:', JSON.stringify(response.headers, null, 2));
    
    if (response.statusCode) {
      console.log('✅ Server is responding');
    } else {
      console.log('❌ Server not responding');
    }
  } catch (error) {
    console.log('❌ Server connection failed:', error.message);
    console.log('🔍 This might indicate:');
    console.log('   - Development server is not running (npm run dev)');
    console.log('   - Port 3002 is not accessible');
    console.log('   - Database connection issues');
  }

  // Test 3: Test with invalid JSON
  console.log('\n3️⃣ Testing with invalid JSON...');
  try {
    const options = {
      hostname: 'localhost',
      port: 3002,
      path: '/api/bidding-documents',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer fake-token',
      },
    };

    const response = await makeRequest(options, 'invalid json');
    console.log('Status:', response.statusCode);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    if (response.statusCode === 400) {
      console.log('✅ Invalid JSON properly handled');
    } else {
      console.log('❌ Expected 400 Bad Request for invalid JSON');
    }
  } catch (error) {
    console.log('❌ Request failed:', error.message);
  }
}

console.log('🚀 Bidding Document API Test Suite');
console.log('===================================');
testBiddingDocumentAPI().then(() => {
  console.log('\n✨ Test suite completed!');
  console.log('\n📝 To fix any issues:');
  console.log('   1. Ensure development server is running: npm run dev');
  console.log('   2. Check database connection in .env.local');
  console.log('   3. Review server logs for detailed error messages');
  console.log('   4. Check file system permissions for uploads directory');
}).catch((error) => {
  console.error('💥 Test suite failed:', error);
  process.exit(1);
});