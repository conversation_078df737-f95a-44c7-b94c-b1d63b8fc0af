'use client'

import React from 'react'
import { useParams, useRouter } from 'next/navigation'
import { ArrowLeft, Edit, Package, Calendar, User, FileText, Settings } from 'lucide-react'
import { MainLayout } from '@/components/layout/MainLayout'
import { Button } from '@/components/ui/Button'
import { useCatalog, useCatalogEquipments } from '@/hooks/queries/useCatalogs'
import { useAuth } from '@/contexts/AuthContext'

export default function CatalogDetailPage() {
  const params = useParams()
  const router = useRouter()
  const catalogId = params.id as string
  const { user } = useAuth()
  const isAdmin = user?.role === 'ADMIN'

  const { data: catalog, isLoading: isCatalogLoading } = useCatalog(catalogId)
  const { data: equipments, isLoading: isEquipmentsLoading } = useCatalogEquipments(catalogId)

  if (isCatalogLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </MainLayout>
    )
  }

  if (!catalog) {
    return (
      <MainLayout>
        <div className="p-6">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Không tìm thấy danh mục
            </h2>
            <Button
              variant="primary"
              onClick={() => router.push('/catalogs')}
              className="mt-4"
            >
              Quay lại danh sách
            </Button>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="p-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => router.push('/catalogs')}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Quay lại
              </Button>
              <div>
                <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                  Chi tiết danh mục
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Thông tin chi tiết về danh mục {catalog.catalogCode}
                </p>
              </div>
            </div>
            {isAdmin && (
              <Button
                variant="primary"
                onClick={() => router.push(`/catalogs?edit=${catalog.id}`)}
                className="flex items-center gap-2"
              >
                <Edit className="w-4 h-4" />
                Chỉnh sửa
              </Button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Info */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                Thông tin cơ bản
              </h2>
              <div className="space-y-4">
                <div>
                  <label className="text-sm text-gray-500 dark:text-gray-400">Mã danh mục</label>
                  <p className="text-gray-900 dark:text-gray-100 font-mono font-medium">
                    {catalog.catalogCode}
                  </p>
                </div>
                <div>
                  <label className="text-sm text-gray-500 dark:text-gray-400">Tên danh mục</label>
                  <p className="text-gray-900 dark:text-gray-100">
                    {catalog.catalogName}
                  </p>
                </div>
                <div>
                  <label className="text-sm text-gray-500 dark:text-gray-400">Mô tả</label>
                  <p className="text-gray-900 dark:text-gray-100">
                    {catalog.description || <span className="italic text-gray-400">Chưa có mô tả</span>}
                  </p>
                </div>
                <div>
                  <label className="text-sm text-gray-500 dark:text-gray-400">Trạng thái</label>
                  <span className={`inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-semibold rounded-full border ${
                    catalog.status === 'ACTIVE'
                      ? 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200 dark:from-green-900/40 dark:to-emerald-900/40 dark:text-green-200 dark:border-green-700'
                      : 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 border-gray-200 dark:from-gray-800/40 dark:to-gray-700/40 dark:text-gray-300 dark:border-gray-600'
                  }`}>
                    <div className={`w-2 h-2 rounded-full ${
                      catalog.status === 'ACTIVE' ? 'bg-green-500' : 'bg-gray-400'
                    }`} />
                    {catalog.status === 'ACTIVE' ? 'Hoạt động' : 'Không hoạt động'}
                  </span>
                </div>
              </div>
            </div>

            {/* Equipment List */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Danh sách thiết bị ({equipments?.length || 0})
                </h2>
                {isAdmin && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.push('/equipment/new')}
                  >
                    <Package className="w-4 h-4 mr-2" />
                    Thêm thiết bị
                  </Button>
                )}
              </div>

              {isEquipmentsLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                </div>
              ) : equipments && equipments.length > 0 ? (
                <div className="space-y-3">
                  {equipments.map((equipment: any) => (
                    <div
                      key={equipment.id}
                      className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer"
                      onClick={() => router.push(`/equipment/${equipment.id}`)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-gray-100">
                            {equipment.name}
                          </h4>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            Mã: {equipment.equipmentCode} • Model: {equipment.model || 'N/A'}
                          </p>
                        </div>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          equipment.status === 'ACTIVE'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                            : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                        }`}>
                          {equipment.status === 'ACTIVE' ? 'Hoạt động' : 'Không hoạt động'}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Package className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
                  <p className="text-gray-500 dark:text-gray-400">
                    Chưa có thiết bị nào trong danh mục này
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar Info */}
          <div className="space-y-6">
            {/* Metadata */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                Thông tin hệ thống
              </h3>
              <div className="space-y-4 text-sm">
                {catalog.creator && (
                  <div>
                    <div className="flex items-center gap-2 text-gray-500 dark:text-gray-400 mb-1">
                      <User className="w-4 h-4" />
                      <span>Người tạo</span>
                    </div>
                    <p className="text-gray-900 dark:text-gray-100">
                      {catalog.creator.name || catalog.creator.email}
                    </p>
                  </div>
                )}
                <div>
                  <div className="flex items-center gap-2 text-gray-500 dark:text-gray-400 mb-1">
                    <Calendar className="w-4 h-4" />
                    <span>Ngày tạo</span>
                  </div>
                  <p className="text-gray-900 dark:text-gray-100">
                    {new Date(catalog.createdAt).toLocaleString('vi-VN')}
                  </p>
                </div>
                <div>
                  <div className="flex items-center gap-2 text-gray-500 dark:text-gray-400 mb-1">
                    <Settings className="w-4 h-4" />
                    <span>Cập nhật lần cuối</span>
                  </div>
                  <p className="text-gray-900 dark:text-gray-100">
                    {new Date(catalog.updatedAt).toLocaleString('vi-VN')}
                  </p>
                </div>
              </div>
            </div>

            {/* Statistics */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                Thống kê
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500 dark:text-gray-400">Tổng số thiết bị</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {equipments?.length || 0}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500 dark:text-gray-400">Thiết bị hoạt động</span>
                  <span className="font-medium text-green-600 dark:text-green-400">
                    {equipments?.filter((e: any) => e.status === 'ACTIVE').length || 0}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500 dark:text-gray-400">Thiết bị ngừng</span>
                  <span className="font-medium text-gray-600 dark:text-gray-400">
                    {equipments?.filter((e: any) => e.status === 'INACTIVE').length || 0}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}