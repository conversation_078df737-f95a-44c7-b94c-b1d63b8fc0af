'use client';

import React from 'react';
import { ChevronDown } from 'lucide-react';

interface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'size'> {
  error?: boolean;
  label?: string;
  helperText?: string;
  selectSize?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'outlined';
}

export function Select({ 
  className = '', 
  error = false, 
  label,
  helperText,
  selectSize = 'md',
  variant = 'default',
  children, 
  ...props 
}: SelectProps) {
  
  // Size variants
  const sizeClasses = {
    sm: 'h-8 text-sm px-2 pr-8',
    md: 'h-10 text-sm px-3 pr-10',
    lg: 'h-12 text-base px-4 pr-12'
  };

  // Icon sizes
  const iconSizes = {
    sm: 'w-3 h-3 right-2',
    md: 'w-4 h-4 right-3',
    lg: 'w-5 h-5 right-4'
  };

  // Variant styles
  const variantClasses = {
    default: `
      border border-gray-300 dark:border-gray-600
      bg-white dark:bg-gray-900
      hover:border-gray-400 dark:hover:border-gray-500
      focus:border-blue-500 dark:focus:border-blue-400
      focus:ring-2 focus:ring-blue-500/20 dark:focus:ring-blue-400/20
    `,
    filled: `
      border-0 border-b-2 border-gray-300 dark:border-gray-600
      bg-gray-50 dark:bg-gray-800
      hover:bg-gray-100 dark:hover:bg-gray-700
      focus:border-blue-500 dark:focus:border-blue-400
      focus:bg-white dark:focus:bg-gray-900
      rounded-t-md rounded-b-none
    `,
    outlined: `
      border-2 border-gray-300 dark:border-gray-600
      bg-transparent
      hover:border-gray-400 dark:hover:border-gray-500
      focus:border-blue-500 dark:focus:border-blue-400
      focus:ring-2 focus:ring-blue-500/20 dark:focus:ring-blue-400/20
    `
  };

  const baseClasses = `
    w-full
    appearance-none
    rounded-md
    text-gray-900 dark:text-gray-100
    placeholder-gray-500 dark:placeholder-gray-400
    transition-all duration-200 ease-in-out
    outline-none
    disabled:opacity-50 disabled:cursor-not-allowed
    disabled:bg-gray-50 dark:disabled:bg-gray-800
    ${sizeClasses[selectSize]}
  `;

  const errorClasses = error
    ? 'border-red-500 dark:border-red-400 focus:border-red-500 dark:focus:border-red-400 focus:ring-red-500/20 dark:focus:ring-red-400/20'
    : variantClasses[variant];

  return (
    <div className="space-y-1">
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          {label}
        </label>
      )}
      
      <div className="relative group">
        <select
          className={`
            ${baseClasses} 
            ${errorClasses} 
            ${className}
          `}
          {...props}
        >
          {children}
        </select>
        
        <ChevronDown 
          className={`
            absolute top-1/2 -translate-y-1/2 pointer-events-none
            text-gray-400 dark:text-gray-500
            transition-transform duration-200
            group-hover:text-gray-600 dark:group-hover:text-gray-300
            ${iconSizes[selectSize]}
            ${error ? 'text-red-500 dark:text-red-400' : ''}
          `}
          aria-hidden="true"
        />
      </div>

      {helperText && (
        <p className={`text-xs ${error ? 'text-red-500 dark:text-red-400' : 'text-gray-500 dark:text-gray-400'}`}>
          {helperText}
        </p>
      )}
    </div>
  );
}