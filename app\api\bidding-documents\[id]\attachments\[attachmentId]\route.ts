import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from '@/lib/auth-server'
import { prisma } from '@/lib/db'
import { unlink } from 'fs/promises'
import path from 'path'

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; attachmentId: string }> }
) {
  try {
    const session = await getServerSession(request)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id, attachmentId } = await params

    // Get attachment details
    const attachment = await prisma.biddingDocumentAttachment.findFirst({
      where: {
        id: attachmentId,
        biddingDocumentId: id
      }
    })

    if (!attachment) {
      return NextResponse.json({ error: 'Attachment not found' }, { status: 404 })
    }

    // Delete file if it's stored locally
    if (attachment.source === 'local' && attachment.fileUrl) {
      try {
        const filePath = path.join(process.cwd(), attachment.fileUrl)
        await unlink(filePath)
      } catch (error) {
        console.error('Error deleting file:', error)
        // Continue even if file deletion fails
      }
    }

    // Delete database record
    await prisma.biddingDocumentAttachment.delete({
      where: { id: attachmentId }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting attachment:', error)
    return NextResponse.json(
      { error: 'Failed to delete attachment' },
      { status: 500 }
    )
  }
}