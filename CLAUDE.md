# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# Project: Bidding System (<PERSON><PERSON> thống thầu thông minh)

A Next.js-based bidding management system with authentication, role-based access control, and comprehensive testing infrastructure.

## Essential Commands

### Development
```bash
npm run dev              # Start dev server on port 3002
npm run build            # Build for production (ALWAYS run before committing)
npm run start            # Start production server
npm run lint             # Check code style
```

### Database (Prisma + PostgreSQL)
```bash
npm run db:generate      # Generate Prisma client
npm run db:push          # Push schema changes to database
npm run db:migrate       # Run migrations
npm run db:seed          # Seed database with initial data
npm run db:studio        # Open Prisma Studio GUI
```

### Testing
```bash
# Unit Tests (Jest)
npm run test:unit              # Run all unit tests
npm run test:unit:coverage     # With coverage report
npm run test:unit:watch        # Watch mode
npx jest path/to/test.tsx      # Run specific test file

# E2E Tests (Playwright)
npx playwright install         # Install browsers (first time only)
npm run test:e2e              # Run all E2E tests
npm run test:e2e:headed       # With visible browser
npm run test:e2e:ui           # Interactive UI mode
npx playwright test path/to/test.spec.ts  # Run specific E2E test

# All Tests
npm test                      # Run unit + E2E tests
```

## High-Level Architecture

### Tech Stack
- **Framework**: Next.js 15.4.5 (App Router)
- **Language**: TypeScript (strict mode)
- **Database**: PostgreSQL with Prisma ORM
- **Styling**: Tailwind CSS v4 (CSS-first config in `app/globals.css`)
- **State Management**: TanStack Query v5
- **Testing**: Jest + React Testing Library + Playwright
- **Authentication**: JWT with cookie-based sessions
- **CI/CD**: GitLab pipelines

### Database Schema (Prisma)
```
User (ADMIN, USER roles)
├── Session (JWT tokens, refresh tokens)
├── Catalog (equipment categories)
├── Equipment (equipment items with specifications)
└── AuditLog (action tracking)
```

### Project Structure
```
app/                    # Next.js app router pages
├── api/auth/          # Auth API routes
├── dashboard/         # Main dashboard
├── biddings/          # Bidding management
├── contractors/       # Contractor management
├── equipment/         # Equipment management (with MainLayout)
├── catalogs/          # Catalog management
├── users/             # User management
└── projects/          # Project management

components/            # React components
├── ui/               # Reusable UI components (Button, Dialog, Select)
├── layout/           # Layout components (SimpleSidebar, MainLayout)
├── equipment/        # Equipment-specific components
├── catalogs/         # Catalog-specific components
├── users/            # User-specific components
└── providers/        # Context providers

services/             # API service layer (Singleton pattern)
hooks/queries/        # TanStack Query hooks
contexts/             # React contexts (Auth, Theme)
types/                # TypeScript type definitions
prisma/               # Database schema and migrations

__tests__/            # Unit tests
e2e/                  # E2E tests
```

## Critical Development Patterns

### Service Layer Implementation
**ALWAYS** follow singleton pattern with BaseService:
```typescript
export class ServiceName extends BaseService {
  private static instance: ServiceName;
  
  private constructor() {
    super({ baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://************:8090/api/v1' });
  }
  
  static getInstance(): ServiceName {
    if (!ServiceName.instance) {
      ServiceName.instance = new ServiceName();
    }
    return ServiceName.instance;
  }
}

export const serviceName = ServiceName.getInstance();
```

### Data Fetching with TanStack Query
**NEVER** use useState/useEffect for API calls. **ALWAYS** use TanStack Query:
```typescript
// ✅ CORRECT
const { data, isLoading } = useQuery({
  queryKey: ['entity', filters],
  queryFn: ({ signal }) => service.getEntity(filters, signal),
});

// ❌ WRONG - Never do this
const [data, setData] = useState();
useEffect(() => { fetchData().then(setData); }, []);
```

### API Client Type Safety
**ALWAYS** provide explicit type parameters:
```typescript
// ✅ CORRECT
const response = await apiClient.get<ResponseType>('/endpoint');

// ❌ WRONG
const response = await apiClient.get('/endpoint'); // Returns 'unknown'
```

### Import/Export Patterns
```typescript
// Services and utilities: Named exports only
export const authService = AuthService.getInstance();
import { authService } from '@/services/authService';

// Never use default exports for services/utilities
```

### Next.js 15 Dynamic Route Parameters
In Next.js 15+, dynamic route parameters are now async and must be awaited:
```typescript
// ✅ CORRECT - Next.js 15+
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  // Use id...
}

// ❌ WRONG - Will cause build errors
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const id = params.id; // Type error in Next.js 15
}
```

This applies to all HTTP methods (GET, POST, PUT, DELETE, PATCH) in route handlers.

## Testing Strategy

### Test File Organization
- Unit tests: `__tests__/unit/` - Uses Jest setup without MSW (jest.setup.simple.ts)
- E2E tests: `e2e/` - Uses Playwright
- Test utilities: `__tests__/utils/test-utils.tsx`

### Current Test Status
- ✅ Button component: 25/25 tests passing
- ⚠️ MSW v2 has compatibility issues with Jest environment
- ✅ E2E tests configured for auth flow and dashboard

## UI/UX Standards

### Form Inputs
- Height: Always `h-10` (40px)
- Padding: `px-3 py-2` (or `pl-10` with icon)
- Base classes: `w-full h-10 border rounded-md focus:ring-2 focus:ring-blue-500`

### Search Input Debouncing
All search inputs **MUST** use `useDebounce` hook with 500ms delay:
```typescript
const debouncedSearch = useDebounce(searchValue, 500);
```

### Color Scheme (Light/Dark Mode)
- Text: `text-gray-900 dark:text-gray-100`
- Background: `bg-white dark:bg-gray-800`
- Border: `border-gray-300 dark:border-gray-600`

## GitLab CI/CD Pipeline

Stages: `install` → `lint` → `test` → `build` → `deploy`

Key jobs:
- `test:unit` - Runs Jest with coverage
- `test:e2e` - Runs Playwright tests
- `build:app` - Next.js production build
- `deploy:staging/production` - Manual deployment triggers

## Layout and Navigation

### Sidebar Navigation
- **Current Component**: `SimpleSidebar` (single source of truth)
- **Layout Wrapper**: All pages MUST use `<MainLayout>` wrapper
- **Menu Structure**: Role-based filtering (ADMIN/USER access levels)
- **Available Routes**: Dashboard, Biddings, Projects, Contractors, Catalogs, Equipment, Reports, Users, Settings

### Page Layout Pattern
```typescript
// ✅ CORRECT - All pages must follow this pattern
import { MainLayout } from '@/components/layout/MainLayout';

export default function PageComponent() {
  return (
    <MainLayout>
      <div className="p-6">
        {/* Page content */}
      </div>
    </MainLayout>
  );
}
```

## Toast Notifications

**IMPORTANT**: Project uses `sonner` for toast notifications, NOT react-hot-toast or react-toastify.

### Usage Pattern:
```typescript
import { useToast } from '@/hooks/useToast';

// Inside component/hook
const toast = useToast();
toast.success('Success message');
toast.error('Error message');
toast.info('Info message');
toast.warning('Warning message');
```

**Never use:**
- ❌ `import { toast } from 'react-hot-toast'`
- ❌ `import toast from 'react-toastify'`
- ❌ Direct import from `sonner`

**Always use:**
- ✅ `import { useToast } from '@/hooks/useToast'`

## Common Pitfalls to Avoid

1. **Never mix data fetching patterns** - Use TanStack Query exclusively
2. **Always run `npm run build` before committing** - Catches TypeScript errors
3. **Never use default exports for services**
4. **Always provide type parameters to apiClient methods**
5. **Use debouncing for all search inputs**
6. **Test with `jest.setup.simple.ts` config** - MSW v2 has issues
7. **Always await dynamic route params in Next.js 15** - Use `Promise<{ id: string }>` type
8. **Always wrap pages with MainLayout** - Ensures consistent sidebar navigation
9. **Keep single sidebar component** - Use SimpleSidebar only, avoid duplication
10. **Use correct toast library** - Always use `useToast` hook from `@/hooks/useToast`

## Environment Variables

Required in `.env.local`:
```
DATABASE_URL=postgresql://...
NEXT_PUBLIC_API_URL=http://...
NEXT_PUBLIC_APP_URL=http://localhost:3002
```

## Development Workflow

1. Create feature branch: `feature-[description]`
2. Write tests for new functionality
3. Run `npm run build` to check TypeScript
4. Run `npm run lint` for code style
5. Run `npm test` to verify all tests pass
6. Commit with descriptive messages
7. Push and create merge request in GitLab