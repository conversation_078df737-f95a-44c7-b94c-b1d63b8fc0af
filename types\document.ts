export const DOCUMENT_TYPES = {
  CATALOG: 'catalog',
  DATASHEET: 'datasheet',
  IMAGE: 'image',
  ISO: 'iso',
  LICENSE: 'license',
  FINANCIAL: 'financial',
  OTHER: 'other'
} as const;

export type DocumentType = typeof DOCUMENT_TYPES[keyof typeof DOCUMENT_TYPES];

export interface Document {
  id: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
  documentType: DocumentType;
  tags: string[];
  description?: string | null;
  uploadedBy?: string | null;
  uploadedAt: string;
  uploader?: {
    id: string;
    name: string;
    email: string;
  } | null;
}

export interface DocumentFilters {
  search?: string;
  documentType?: DocumentType | '';
  tags?: string[];
  page?: number;
  limit?: number;
  sortBy?: 'fileName' | 'uploadedAt';
  sortOrder?: 'asc' | 'desc';
}

export interface DocumentsResponse {
  documents: Document[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface CreateDocumentDto {
  file: File;
  documentType: DocumentType;
  tags?: string[];
  description?: string;
}

export interface UpdateDocumentDto {
  documentType?: DocumentType;
  tags?: string[];
  description?: string;
}

export const ALLOWED_FILE_TYPES = {
  'application/pdf': ['.pdf'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
  'application/vnd.ms-excel': ['.xls'],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
  'application/zip': ['.zip'],
  'application/x-zip-compressed': ['.zip'],
  'image/jpeg': ['.jpg', '.jpeg'],
  'image/png': ['.png'],
  'image/gif': ['.gif'],
  'image/webp': ['.webp']
};

export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return Math.round(bytes / Math.pow(k, i) * 100) / 100 + ' ' + sizes[i];
}

export function getDocumentTypeLabel(type: DocumentType): string {
  const labels: Record<DocumentType, string> = {
    catalog: 'Catalog',
    datasheet: 'Datasheet',
    image: 'Hình ảnh',
    iso: 'ISO/Tiêu chuẩn',
    license: 'Giấy phép',
    financial: 'Tài chính',
    other: 'Khác'
  };
  return labels[type] || type;
}

export function getFileIcon(fileName: string): string {
  const ext = fileName.split('.').pop()?.toLowerCase();
  switch(ext) {
    case 'pdf': return '📄';
    case 'doc':
    case 'docx': return '📝';
    case 'xls':
    case 'xlsx': return '📊';
    case 'zip': return '📦';
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'webp': return '🖼️';
    default: return '📎';
  }
}