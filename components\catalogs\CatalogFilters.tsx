'use client'

import React, { useState, useEffect } from 'react'
import { Search, X, Filter } from 'lucide-react'
import { useDebounce } from '@/hooks/useDebounce'
import { Select } from '@/components/ui/Select'
import { Button } from '@/components/ui/Button'
import type { CatalogFilters as CatalogFiltersType } from '@/types/catalog'

interface CatalogFiltersProps {
  filters: CatalogFiltersType
  onFilterChange: (filters: CatalogFiltersType) => void
}

export function CatalogFilters({ filters, onFilterChange }: CatalogFiltersProps) {
  const [searchValue, setSearchValue] = useState(filters.search || '')
  const debouncedSearch = useDebounce(searchValue, 500)

  useEffect(() => {
    if (debouncedSearch !== filters.search) {
      onFilterChange({ ...filters, search: debouncedSearch, page: 1 })
    }
  }, [debouncedSearch])

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value.trim())
  }

  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value as 'ACTIVE' | 'INACTIVE' | ''
    onFilterChange({ 
      ...filters, 
      status: value || undefined,
      page: 1 
    })
  }

  const clearFilters = () => {
    setSearchValue('')
    onFilterChange({
      page: 1,
      limit: filters.limit || 10,
      sortBy: filters.sortBy,
      sortOrder: filters.sortOrder
    })
  }

  const hasActiveFilters = !!(filters.search || filters.status)

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Search input */}
        <div className="md:col-span-2">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={searchValue}
              onChange={handleSearchChange}
              placeholder="Tìm kiếm theo mã, tên hoặc mô tả..."
              className="w-full h-10 pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md hover:border-gray-400 dark:hover:border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 transition-colors"
            />
          </div>
        </div>

        {/* Status filter */}
        <div>
          <Select
            value={filters.status || ''}
            onChange={handleStatusChange}
          >
            <option value="">Tất cả trạng thái</option>
            <option value="ACTIVE">Hoạt động</option>
            <option value="INACTIVE">Không hoạt động</option>
          </Select>
        </div>
      </div>

      {/* Clear filters button */}
      {hasActiveFilters && (
        <div className="mt-4 flex items-center justify-between border-t pt-4">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Đang áp dụng bộ lọc
          </div>
          <Button
            variant="secondary"
            size="sm"
            onClick={clearFilters}
            className="flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            Xóa bộ lọc
          </Button>
        </div>
      )}
    </div>
  )
}