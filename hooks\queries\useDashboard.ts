import { useQuery } from '@tanstack/react-query'
import { dashboardService, RecentBiddingDocument } from '@/services/dashboardService'
import { DashboardStats } from '@/types/dashboard'

// Query keys
export const dashboardKeys = {
  all: ['dashboard'] as const,
  stats: () => [...dashboardKeys.all, 'stats'] as const,
  recentBiddings: () => [...dashboardKeys.all, 'recent-biddings'] as const,
}

// Hook for dashboard statistics
export function useDashboardStats() {
  return useQuery({
    queryKey: dashboardKeys.stats(),
    queryFn: ({ signal }) => dashboardService.getDashboardStats(signal),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 3, // Retry failed requests 3 times
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
    throwOnError: false, // Don't throw errors, let components handle them
  })
}

// Hook for recent bidding documents
export function useRecentBiddingDocuments() {
  return useQuery({
    queryKey: dashboardKeys.recentBiddings(),
    queryFn: ({ signal }) => dashboardService.getRecentBiddingDocuments(signal),
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: false,
    retry: 3, // Retry failed requests 3 times
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
    throwOnError: false, // Don't throw errors, let components handle them
  })
}