import { useQuery } from '@tanstack/react-query'
import { dashboardService, RecentBiddingDocument, DashboardActivity, BiddingDocumentOverview, DashboardAnalytics } from '@/services/dashboardService'
import { DashboardStats } from '@/types/dashboard'

// Query keys
export const dashboardKeys = {
  all: ['dashboard'] as const,
  stats: () => [...dashboardKeys.all, 'stats'] as const,
  recentBiddings: () => [...dashboardKeys.all, 'recent-biddings'] as const,
  activities: (page?: number, limit?: number, options?: any) => [...dashboardKeys.all, 'activities', { page, limit, ...options }] as const,
  biddingDocuments: (page?: number, limit?: number, options?: any) => [...dashboardKeys.all, 'bidding-documents', { page, limit, ...options }] as const,
  analytics: (options?: any) => [...dashboardKeys.all, 'analytics', options] as const,
}

// Hook for dashboard statistics
export function useDashboardStats() {
  return useQuery({
    queryKey: dashboardKeys.stats(),
    queryFn: ({ signal }) => dashboardService.getDashboardStats(signal),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 3, // Retry failed requests 3 times
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
    throwOnError: false, // Don't throw errors, let components handle them
  })
}

// Hook for recent bidding documents
export function useRecentBiddingDocuments() {
  return useQuery({
    queryKey: dashboardKeys.recentBiddings(),
    queryFn: ({ signal }) => dashboardService.getRecentBiddingDocuments(signal),
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: false,
    retry: 3, // Retry failed requests 3 times
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
    throwOnError: false, // Don't throw errors, let components handle them
  })
}

// Hook for dashboard activities
export function useDashboardActivities(
  page = 1,
  limit = 20,
  options: {
    type?: 'created' | 'updated' | 'completed'
    dateFrom?: string
    dateTo?: string
  } = {}
) {
  return useQuery({
    queryKey: dashboardKeys.activities(page, limit, options),
    queryFn: ({ signal }) => dashboardService.getDashboardActivities(page, limit, options, signal),
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchOnWindowFocus: false,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    throwOnError: false,
  })
}

// Hook for bidding documents overview
export function useBiddingDocumentsOverview(
  page = 1,
  limit = 10,
  options: {
    status?: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED'
    customerName?: string
    search?: string
    urgent?: boolean
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
    dateFrom?: string
    dateTo?: string
  } = {}
) {
  return useQuery({
    queryKey: dashboardKeys.biddingDocuments(page, limit, options),
    queryFn: ({ signal }) => dashboardService.getBiddingDocumentsOverview(page, limit, options, signal),
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: false,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    throwOnError: false,
  })
}

// Hook for dashboard analytics
export function useDashboardAnalytics(
  options: {
    dateFrom?: string
    dateTo?: string
    period?: 'week' | 'month' | 'quarter' | 'year'
  } = {}
) {
  return useQuery({
    queryKey: dashboardKeys.analytics(options),
    queryFn: ({ signal }) => dashboardService.getDashboardAnalytics(options, signal),
    staleTime: 5 * 60 * 1000, // 5 minutes (analytics data doesn't change as frequently)
    refetchOnWindowFocus: false,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    throwOnError: false,
  })
}