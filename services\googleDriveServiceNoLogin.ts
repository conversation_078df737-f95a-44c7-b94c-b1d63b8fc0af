import { google } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';
import { Readable } from 'stream';
import { console } from 'inspector';

export class GoogleDriveServiceNoLogin {
  private static instance: GoogleDriveServiceNoLogin;
  private oauth2Client: OAuth2Client | null = null;
  private driveClient: any = null;
  private isInitialized = false;

  private constructor() {}

  static getInstance(): GoogleDriveServiceNoLogin {
    if (!GoogleDriveServiceNoLogin.instance) {
      GoogleDriveServiceNoLogin.instance = new GoogleDriveServiceNoLogin();
    }
    return GoogleDriveServiceNoLogin.instance;
  }

  async initialize() {
    console.log(this.isInitialized)
    if (this.isInitialized) return true;

    try {
      const client_id = process.env.GOOGLE_CLIENT_ID;
      const client_secret = process.env.GOOGLE_CLIENT_SECRET;
      const refresh_token = process.env.GOOGLE_REFRESH_TOKEN;

      console.log("client_id", client_id);
      console.log("client_secret", client_secret);
      console.log("refresh_token", refresh_token);

      if (!client_id || !client_secret || !refresh_token) {
        console.warn('Google OAuth2 credentials not configured. Google Drive features will be disabled.');
        return false;
      }

      // Create OAuth2 client
      this.oauth2Client = new google.auth.OAuth2(
        client_id,
        client_secret,
        process.env.GOOGLE_REDIRECT_URI
      );

      // Set refresh token
      this.oauth2Client.setCredentials({
        refresh_token: refresh_token
      });

      console.log("refresh_token:" , refresh_token)

      // Get new access token using refresh token
      console.log('Getting access token using refresh token...');
      const { credentials } = await this.oauth2Client.refreshAccessToken();
      this.oauth2Client.setCredentials(credentials);
      console.log('Access token obtained successfully');

      // Auto refresh token when expired
      this.oauth2Client.on('tokens', (tokens) => {
        console.log('Token auto-refreshed');
        if (tokens.refresh_token) {
          console.log('New refresh token received');
        }
      });

      // Create Drive client
      this.driveClient = google.drive({
        version: 'v3',
        auth: this.oauth2Client
      });

      this.isInitialized = true;
      console.log('Google Drive initialized - No login required!');
      return true;
    } catch (error) {
      console.error('Failed to initialize Google Drive:', error);
      this.isInitialized = false;
      return false;
    }
  }

  async getDriveClient() {
    if (!this.isInitialized) {
      const initialized = await this.initialize();
      if (!initialized) {
        throw new Error('Google Drive service not initialized. Please check credentials.');
      }
    }
    return this.driveClient;
  }

  async uploadFile(fileBuffer: Buffer, fileName: string, mimeType: string) {
    const drive = await this.getDriveClient();
    
    // Convert to Google Docs/Sheets format if applicable
    let googleMimeType = mimeType;
    if (mimeType.includes('word') || mimeType.includes('document')) {
      googleMimeType = 'application/vnd.google-apps.document';
    } else if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) {
      googleMimeType = 'application/vnd.google-apps.spreadsheet';
    }

    const fileMetadata: any = {
      name: fileName,
      mimeType: googleMimeType
    };

    if (process.env.GOOGLE_DRIVE_FOLDER_ID) {
      fileMetadata.parents = [process.env.GOOGLE_DRIVE_FOLDER_ID];
    }

    // Convert Buffer to readable stream
    const stream = Readable.from(fileBuffer);
    
    const media = {
      mimeType: mimeType,
      body: stream
    };

    const response = await drive.files.create({
      requestBody: fileMetadata,
      media: media,
      fields: 'id, name, mimeType, webViewLink, webContentLink'
    });

    // Set file permissions to be viewable by anyone with the link
    try {
      await drive.permissions.create({
        fileId: response.data.id,
        requestBody: {
          role: 'reader',
          type: 'anyone'
        }
      });
      console.log('File permissions set successfully');
    } catch (permError) {
      console.warn('Failed to set file permissions:', permError);
      // Continue even if permission setting fails
    }

    // Generate edit URL
    let editUrl = '';
    if (response.data.mimeType === 'application/vnd.google-apps.document') {
      editUrl = `https://docs.google.com/document/d/${response.data.id}/edit`;
    } else if (response.data.mimeType === 'application/vnd.google-apps.spreadsheet') {
      editUrl = `https://docs.google.com/spreadsheets/d/${response.data.id}/edit`;
    }

    return {
      fileId: response.data.id,
      fileName: fileName,
      mimeType: response.data.mimeType,
      webViewLink: response.data.webViewLink,
      webContentLink: response.data.webContentLink,
      editUrl: editUrl,
      embedUrl: editUrl ? `${editUrl}?embedded=true` : `https://drive.google.com/file/d/${response.data.id}/preview`
    };
  }

  async listFiles() {
    const drive = await this.getDriveClient();
    
    let query = "trashed=false and (mimeType='application/vnd.google-apps.document' or mimeType='application/vnd.google-apps.spreadsheet' or mimeType='application/vnd.openxmlformats-officedocument.wordprocessingml.document' or mimeType='application/msword' or mimeType='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' or mimeType='application/vnd.ms-excel')";
    
    if (process.env.GOOGLE_DRIVE_FOLDER_ID) {
      query = `'${process.env.GOOGLE_DRIVE_FOLDER_ID}' in parents and ${query}`;
    }

    const response = await drive.files.list({
      q: query,
      fields: 'files(id, name, mimeType, createdTime, modifiedTime, size)',
      orderBy: 'modifiedTime desc',
      pageSize: 100
    });

    return response.data.files || [];
  }

  async deleteFile(fileId: string) {
    const drive = await this.getDriveClient();
    await drive.files.delete({ fileId });
    return true;
  }

  async getFile(fileId: string) {
    const drive = await this.getDriveClient();
    
    const response = await drive.files.get({
      fileId: fileId,
      fields: 'id, name, mimeType, webViewLink, webContentLink, size, modifiedTime, iconLink, thumbnailLink'
    });

    let editUrl = '';
    let embedLink = '';
    
    if (response.data.mimeType === 'application/vnd.google-apps.document') {
      editUrl = `https://docs.google.com/document/d/${fileId}/edit`;
      embedLink = `https://docs.google.com/document/d/${fileId}/preview`;
    } else if (response.data.mimeType === 'application/vnd.google-apps.spreadsheet') {
      editUrl = `https://docs.google.com/spreadsheets/d/${fileId}/edit`;
      embedLink = `https://docs.google.com/spreadsheets/d/${fileId}/preview`;
    } else if (response.data.mimeType === 'application/vnd.google-apps.presentation') {
      editUrl = `https://docs.google.com/presentation/d/${fileId}/edit`;
      embedLink = `https://docs.google.com/presentation/d/${fileId}/preview`;
    } else {
      embedLink = `https://drive.google.com/file/d/${fileId}/preview`;
    }

    console.log(editUrl, embedLink)

    return {
      ...response.data,
      embedLink,
      editUrl,
      embedUrl: editUrl ? `${editUrl}?embedded=true` : `https://drive.google.com/file/d/${fileId}/preview`
    };
  }

  // Always return authenticated since we use refresh token
  async isAuthenticated(): Promise<boolean> {
        console.log(this.isInitialized)

    return this.isInitialized || await this.initialize();
  }

  async exportFile(fileId: string, mimeType: string): Promise<{ data: ArrayBuffer; fileName: string }> {
    const drive = await this.getDriveClient();
    
    // Get file metadata first to get the name
    const fileMetadata = await drive.files.get({
      fileId: fileId,
      fields: 'name'
    });
    
    // Export the file
    const response = await drive.files.export({
      fileId: fileId,
      mimeType: mimeType
    }, {
      responseType: 'arraybuffer'
    });
    
    // Determine file extension based on export mime type
    let extension = 'pdf';
    if (mimeType.includes('word')) extension = 'docx';
    else if (mimeType.includes('sheet')) extension = 'xlsx';
    else if (mimeType.includes('presentation')) extension = 'pptx';
    
    const fileName = `${fileMetadata.data.name}.${extension}`;
    
    return {
      data: response.data,
      fileName: fileName
    };
  }

  async downloadFile(fileId: string): Promise<ArrayBuffer> {
    const drive = await this.getDriveClient();
    
    const response = await drive.files.get({
      fileId: fileId,
      alt: 'media'
    }, {
      responseType: 'arraybuffer'
    });
    
    return response.data;
  }
}

export const googleDriveServiceNoLogin = GoogleDriveServiceNoLogin.getInstance();